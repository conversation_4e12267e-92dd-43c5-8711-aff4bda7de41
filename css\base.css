@font-face {
	font-family: 'LigatureSymbols';

	src: url('../fonts/LigatureSymbols-2.11.eot');

	src: url('../fonts/LigatureSymbols-2.11-1.eot') format('embedded-opentype'),
		url('../fonts/LigatureSymbols-2.11.woff') format('woff'),
		url('../fonts/LigatureSymbols-2.11.ttf') format('truetype'),
		url('../fonts/LigatureSymbols-2.11.svg') format('svg');

	src: url('../fonts/LigatureSymbols-2.11.ttf') format('truetype');

	font-weight: normal;

	font-style: normal;
}

.lsf,
.lsf-icon:before {
	font-family: 'LigatureSymbols';

	-webkit-text-rendering: optimizeLegibility;

	-moz-text-rendering: optimizeLegibility;

	-ms-text-rendering: optimizeLegibility;

	-o-text-rendering: optimizeLegibility;

	text-rendering: optimizeLegibility;

	-webkit-font-smoothing: antialiased;

	-moz-font-smoothing: antialiased;

	-ms-font-smoothing: antialiased;

	-o-font-smoothing: antialiased;

	font-smoothing: antialiased;
}

.container {
	/*background: #fff;*/

	padding: 10px 0;

	position: relative;

	-webkit-transition: padding 100ms ease;

	-moz-transition: padding 100ms ease;

	transition: padding 100ms ease;
}

.container.no-padding {
	padding: 0;
}

.container-inner {
	clear: both;

	margin: 0 auto;

	max-width: 1140px;

	padding: 0 20px;

	position: relative;
}

.container-inner-bordered {
	/*margin-top: 30px;*/

	padding-top: 30px;
}

.line-hori {
	border-top: 1px solid #333;

	width: 100%;

	margin: 136px 0px 0px 0px;
}

.container-spacy {
	padding: 75px 0;
}

.container-tight {
	padding: 50px 0;
}

.container-tighter {
	padding: 0;
}

.container-narrow {
	max-width: 700px;
}

.container-bordered {
	border-top: 1px solid #e6e6e6;
}

.container-bordered-yellow {
	border-top: 1px solid #e9da08;
}

.container-promo {
	margin: 0 20px;
}

.container-promo-inner {
	background-color: #f7f7f7;

	border: 1px solid #e6e6e6;

	-webkit-border-radius: 0.25em;

	-moz-border-radius: 0.25em;

	border-radius: 0.25em;

	font-size: 1.5em;

	font-weight: 100;

	line-height: 5em;

	max-width: 960px;

	overflow: hidden;
}

.container-promo .button-base {
	font-size: 16px;

	line-height: 1.5em;

	margin-top: -1.75em;

	margin-left: 2em;

	position: absolute;

	right: 20px;

	top: 50%;
}

.container-promo .button-base:active {
	-webkit-transform: translateY(1px);

	transform: translateY(1px);
}

.container-lightgray {
	background: #f8f8f8;

	border-top: 1px solid #e6e6e6;
}

.container-lightgray + .container-lightgray {
	border-top: none;
}

.container-yellow {
	background: #fff000;

	color: #000;
}

.container-lightgray + .container-yellow {
	border-top: 1px solid #f2e307;
}

.container-darkyellow {
	background-color: #efdc00;
}

.container-black {
	color: #7e7e7e;

	background: #000;
}

.container-black a {
	color: #fff;

	/*font-weight: 700;*/
}

.container-black h1,
.container-black h2,
.container-black h3 {
	color: #fff;
}

.container-darkgray {
	/* background: #2763aa;*/

	color: #ccc;

	margin-top: 30px;
}

.container-darkgray h3 {
	color: #fff;

	font-weight: 100;
}

.container-quote-mark {
	background: #999;

	-webkit-border-radius: 2em;

	-moz-border-radius: 2em;

	border-radius: 2em;

	color: #fff;

	font-family: 'LigatureSymbols';

	font-size: 64px;

	height: 0;

	left: 50%;

	line-height: 1em;

	margin-left: -0.625em;

	padding: 0.15em 0.15em 1.15em;

	position: absolute;

	top: -0.7em;

	-webkit-font-smoothing: antialiased;

	-moz-osx-font-smoothing: grayscale;
}

.container-featuredin {
	overflow: hidden;
}

.puff {
	float: left;

	margin-right: 40px;

	width: 40%;
}

.puff-3,
.puff-3-narrow {
	margin: 0 0 0 9%;

	width: 27.3333%;
}

.puff-5 {
	width: 17.24%;

	max-width: 415px;

	margin: 10px 1.72% 0;
}

.puff-first {
	margin-left: 0;
}

.puff-last {
	margin-right: 0;
}

.puff > h3 {
	font-size: 24px;

	font-weight: 300;

	line-height: 30px;

	margin: 0 0 10px;
}

#disclaimer {
	font-size: 13px;

	color: #fff;

	text-align: center;
}

#disclaimer a {
	color: #d2d2d2;

	text-decoration: none;
}

#disclaimer a:hover {
	color: #ffffff;

	text-decoration: underline;
}

.info {
	width: 450px;

	margin-bottom: 100px;

	position: relative;
}

.info-tight {
	margin: 0;
}

.info-left {
	float: left;
}

.info-right {
	float: right;
}

.clearfix,
.info-row {
	clear: both;
}

.info-row-last .info {
	margin-bottom: 0;
}

.info-img {
	display: block;

	margin: 0 auto;
}

.info-sidebar {
	margin-left: 8%;

	width: 25%;
}

.noscroll {
	overflow: hidden;
}

.width-1-2 {
	width: 50%;
}

.width-1-3 {
	width: 33.33%;
}

.width-2-3 {
	width: 66.67%;
}

.width-1-5 {
	width: 20%;
}

.width-4-5 {
	width: 80%;
}

.container-map {
	color: #fff;

	display: none;
}

#map-container {
	background-image: url(../../images/img-worldmap.png);

	height: 480px;

	margin: 4em auto 2em;

	position: relative;

	width: 740px;
}

#map-main {
	position: absolute;
}

.map-dot-strong {
	opacity: 1;

	position: absolute;

	top: 50%;

	left: 50%;

	width: 100px;

	height: 100px;

	margin: -44px 0 0 -44px;
}

.map-dot-strong-fade {
	opacity: 0;

	width: 14px;

	height: 14px;

	margin: 0;

	-webkit-transition: margin 500ms, width 500ms, height 500ms, opacity 200ms ease-out 300ms;

	-moz-transition: margin 500ms, width 500ms, height 500ms, opacity 200ms ease-out 300ms;

	transition: margin 500ms, width 500ms, height 500ms, opacity 200ms ease-out 300ms;
}

.packages-list {
	color: #333;

	margin: 5em auto 0;

	white-space: nowrap;
}

.packages-item {
	background-color: #f7f7f7;

	background-image: -moz-linear-gradient(top, #fff, #f7f7f7);

	background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#fff), to(#f7f7f7));

	background-image: -webkit-linear-gradient(top, #fff, #f7f7f7);

	background-image: -o-linear-gradient(top, #fff, #f7f7f7);

	background-image: linear-gradient(to bottom, #fff, #f7f7f7);

	background-repeat: repeat-x;

	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffffff', endColorstr='#fff7f7f7', GradientType=0);

	border: 0 solid #d2d2d2;

	-webkit-box-sizing: border-box;

	-moz-box-sizing: border-box;

	box-sizing: border-box;

	display: inline-block;

	margin-top: 20px;

	text-align: center;

	position: relative;

	white-space: normal;

	width: 25%;
}

.packages-item .tooltip-container {
	color: #ccc;
}

.packages-item .tooltip-container:hover {
	color: #333;

	color: rgba(0, 0, 0, 0.8);
}

.packages-item .button-base {
	display: block;

	margin: 0 1.2em;
}

.packages-item {
	padding-bottom: 1.5em;
}

.packages-item-highlighted {
	background: #fff;

	-webkit-border-radius: 15px 15px 0 0;

	-moz-border-radius: 15px 15px 0 0;

	border-radius: 15px 15px 0 0;

	margin-top: 0;

	padding-bottom: 1.5em;

	width: 25%;
}

.packages-item-highlighted .packages-header {
	position: relative;

	background: #282828;

	background: -moz-linear-gradient(top, #282828 0, #222 100%);

	background: -webkit-linear-gradient(top, #282828 0, #222 100%);

	background: linear-gradient(to bottom, #282828 0, #222 100%);

	padding-top: 20px;

	font-size: 1.7em;

	color: #fff;

	-webkit-border-radius: 8px 8px 0 0;

	-moz-border-radius: 8px 8px 0 0;

	border-radius: 8px 8px 0 0;

	font-family: 'Helevetica Neue', Helvetica, Arial, sans-serif !important;

	font-weight: 500;
}

.packages-item-highlighted .button-base {
	background-color: #57c700;

	box-shadow: 0 3px 0 #4db300, 0 5px 2px rgba(0, 0, 0, 0.2);

	color: #fff;
}

.packages-item-highlighted .button-base:hover {
	background-color: #51ba00;

	box-shadow: 0 3px 0 #48a600, 0 5px 2px rgba(0, 0, 0, 0.2);
}

.packages-item-highlighted .button-base:active {
	box-shadow: 0 2px 0 #48a600, 0 4px 2px rgba(0, 0, 0, 0.2);
}

.packages-item-first {
	-webkit-border-radius: 15px 0 0;

	-moz-border-radius: 15px 0 0;

	border-radius: 15px 0 0;

	border-right: none;
}

.packages-item-last {
	-webkit-border-radius: 0 15px 0 0;

	-moz-border-radius: 0 15px 0 0;

	border-radius: 0 15px 0 0;

	border-left: none;
}

.packages-item-border-right {
	position: absolute;

	border-right: 1px solid #e1e1e1;

	width: 100%;

	height: 292px;

	z-index: 200;
}

.packages-list-main .packages-item {
	padding-bottom: 0;
}

.packages-list-main .packages-item-first {
	padding-bottom: 0;

	-webkit-border-radius: 15px 0 0 8px;

	-moz-border-radius: 15px 0 0 8px;

	border-radius: 15px 0 0 8px;
}

.packages-list-main .packages-item-last {
	padding-bottom: 0;

	-webkit-border-radius: 0 15px 8px 0;

	-moz-border-radius: 0 15px 8px 0;

	border-radius: 0 15px 8px 0;
}

.packages-list-main .packages-item-border-right {
	position: relative;

	height: 100%;

	padding-bottom: 2.2em;
}

.packages-list-main .packages-item .packages-item-border-right {
	border-left: 0 solid #e1e1e1;

	border-right: 0;

	border-bottom: 1px solid #e1e1e1;
}

.packages-list-main .packages-item-first .packages-item-border-right {
	border-left: 1px solid #e1e1e1;

	border-bottom: 1px solid #e1e1e1;

	border-right: 1px solid #e1e1e1;

	-webkit-border-radius: 0 0 0 8px;

	-moz-border-radius: 0 0 0 8px;

	border-radius: 0 0 0 8px;
}

.packages-list-main .packages-item-highlight .packages-item-border-right {
	border-left: 0 solid #e1e1e1;

	border-right: 0;

	border-bottom: 1px solid #e1e1e1;
}

.packages-list-main .packages-item-last .packages-item-border-right {
	border-left: 0 solid #e1e1e1;

	border-right: 1px solid #e1e1e1;

	border-bottom: 1px solid #e1e1e1;

	-webkit-border-radius: 0 0 8px;

	-moz-border-radius: 0 0 8px;

	border-radius: 0 0 8px;
}

.packages-item-header-border-right {
	background: url(../../images/pricing/price-package-line-separator.png?jh35hg) no-repeat;

	position: absolute;

	width: 2px;

	border: 0 solid red;

	height: 82px;

	z-index: 200;

	top: 0;

	left: 100%;
}

.packages-header {
	font-family: HelveticaNeue-Thin, HelveticaNeue-Light, 'Helevetica Neue', Helvetica, Arial, sans-serif;

	background: #050505;

	background: -moz-linear-gradient(top, #050505 0, #202020 100%);

	background: -webkit-linear-gradient(top, #050505 0, #202020 100%);

	background: linear-gradient(to bottom, #050505 0, #202020 100%);

	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#050505', endColorstr='#202020', GradientType=0);

	border-bottom: 1px solid #ddd;

	font-size: 1.7em;

	font-weight: 100;

	line-height: 80px;

	margin: 0;

	text-shadow: 0;

	color: #f5f5f5;
}

.packages-item-first .packages-header {
	-webkit-border-radius: 8px 0 0;

	-moz-border-radius: 8px 0 0;

	border-radius: 8px 0 0;
}

.packages-item-last .packages-header {
	-webkit-border-radius: 0 8px 0 0;

	-moz-border-radius: 0 8px 0 0;

	border-radius: 0 8px 0 0;

	border-right: none;
}

.packages-tagline {
	background: #e7e7e7;

	background: -moz-linear-gradient(top, #fff 0, #e7e7e7 100%);

	background: -webkit-linear-gradient(top, #fff 0, #e7e7e7 100%);

	background: linear-gradient(to bottom, #fff 0, #e7e7e7 100%);

	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#fcfcfc', GradientType=0);

	border-bottom: 1px solid #ddd;

	color: #999;

	font-size: 0.7em;

	font-weight: 400;

	line-height: 2.1em;

	margin-bottom: 5.6em;

	padding-top: 0.1em;

	text-transform: uppercase;
}

.packages-features {
	border-top: 1px solid #dedede;

	margin: 1em 0;
}

.packages-features-item {
	border-bottom: 1px solid #dedede;

	padding: 0.25em;

	white-space: nowrap;

	overflow: visible;
}

.packages-price {
	font-weight: 100;

	color: #333;

	min-height: 110px;
}

.packages-price-container {
	min-height: 75px;
}

.packages-price-currency {
	font-size: 1.75em;

	margin-top: -0.7em;

	position: absolute;

	text-align: right;
}

.packages-price-whole {
	margin-left: 19px;

	font-size: 5em;

	font-weight: 500;
}

.packages-price-whole-small {
	font-size: 4em;
}

.packages-price-decimals {
	font-size: 22px;

	margin: -19px 0;

	position: absolute;
}

.packages-price-period {
	font-size: 16px;

	text-transform: uppercase;
}

.packages-price-crossed {
	height: 9px;

	left: 50%;

	margin: 12px -70px;

	position: absolute;

	width: 161px;
}

.packages-price-permonth {
	margin: 0.2em 0;
}

.packages-price-discountinfo,
.packages-price-discountinfo:last-child {
	color: #999;

	font-family: 'Helevetica Neue', Helvetica, Arial, sans-serif;

	font-size: 13px;

	font-weight: 400;

	line-height: 1.4em;

	margin-left: auto;

	margin-right: auto;

	margin-top: 10px;

	max-width: 19em;

	margin-bottom: 0.2em;
}

.packages-price-paybyinvoice {
	position: absolute;

	width: 200px;

	color: #333;

	font-size: 0.6em;

	line-height: 1.4em;

	max-width: 19em;

	font-weight: 400;

	margin: 0;

	left: 50%;

	margin-left: -100px;

	margin-top: 12px;
}

.packages-comparison {
	text-align: center;

	margin: 3em 0 1em;
}

.packages-comparison > a {
	border-bottom: 1px solid #000;

	font-weight: 700;
}

.packages-comparison:after {
	content: '\e005';

	font-family: 'LigatureSymbols';

	margin-left: 0.3em;

	vertical-align: middle;
}

.packages-discount-personal {
	background: url(../../images/discount/discount-30.png?jh35hg) no-repeat;

	width: 98px;

	height: 109px;

	position: absolute;

	left: 197px;

	top: -39px;
}

.packages-discount-pro {
	background: url(../../images/discount/discount-50.png?jh35hg) no-repeat;

	width: 124px;

	height: 144px;

	position: absolute;

	right: 7px;

	top: -55px;
}

.packages-discount-team {
	background: url(../../images/discount/discount-50-small.png?oh91br) no-repeat;

	height: 144px;

	position: absolute;

	right: -28px;

	top: -52px;

	width: 124px;
}

.packages-discount-starter {
	background: url(../../images/pricing/_30.png?jh35hg) no-repeat;

	width: 98px;

	height: 109px;

	position: absolute;

	right: 10px;

	top: -50px;

	z-index: 200;
}

.packages-discount-standard {
	background: url(../../images/pricing/_50.png?jh35hg) no-repeat;

	width: 124px;

	height: 144px;

	position: absolute;

	right: 0;

	top: -75px;

	z-index: 200;
}

.packages-discount-professional {
	background: url(../../images/pricing/_50.png?oh91br) no-repeat;

	height: 144px;

	position: absolute;

	right: 0;

	top: -55px;

	width: 124px;

	z-index: 200;
}

.packages-cta {
	font-size: 0.7em;

	font-weight: 700;

	margin-top: 1em;

	text-transform: uppercase;
}

.packages-cta em {
	color: #ff4c4c;

	font-style: normal;
}

.packages-icon-check {
	background: url(../../images/pricing/ico-check.png?oh91br) no-repeat;

	background-position: center;

	background-size: 30px auto;

	display: block;

	height: 30px;

	width: 100%;
}

.packages-icon-x {
	background: url(../../images/pricing/ico-x.png?oh91br) no-repeat;

	background-position: center;

	background-size: 30px auto;

	display: block;

	height: 30px;

	width: 100%;
}

.packages-icon-plus {
	background: url(../../images/pricing/ico-plus.png?oh91br) no-repeat;

	background-position: center;

	background-size: 30px auto;

	display: block;

	height: 30px;

	width: 100%;
}

.packages-icon-no,
.packages-icon-yes {
	position: relative;

	top: 0.1em;
}

.packages-icon-yes {
	color: #88d40f;
}

.packages-icon-no {
	color: #666;
}

.packages-shadow {
	position: absolute;

	height: 1098px;

	top: 80px;

	width: 8px;

	z-index: 400;
}

.packages-list-main .packages-shadow {
	height: 492px;

	top: 80px;
}

.packages-shadow-left {
	right: -1px;
}

.packages-shadow-right {
	left: -1px;
}

.packages-list .button-base {
	position: relative;

	z-index: 300;
}

.tooltip-container {
	margin: 16px 0 0 8px;

	position: relative;
}

.tooltip-container:hover > .tooltip-content {
	opacity: 1;

	-webkit-transition: opacity 100ms ease;

	-moz-transition: opacity 100ms ease;

	transition: opacity 100ms ease;

	visibility: visible;
}

.tooltip-content {
	background-color: #333;

	background-color: rgba(0, 0, 0, 0.8);

	-webkit-border-radius: 6px;

	-moz-border-radius: 6px;

	border-radius: 6px;

	bottom: 31px;

	color: #999;

	font-size: 14px;

	left: -116px;

	opacity: 0;

	-webkit-transition: visibility 0s linear 100ms, opacity 100ms ease;

	-moz-transition: visibility 0s linear 100ms, opacity 100ms ease;

	transition: visibility 0s linear 100ms, opacity 100ms ease;

	padding: 15px 20px;

	position: absolute;

	visibility: hidden;

	width: 200px;

	z-index: 1;
}

.tooltip-content:after {
	border-color: #333 transparent transparent;

	border-color: rgba(0, 0, 0, 0.8) transparent transparent;

	border-style: solid;

	border-width: 6px 6px 0;

	bottom: -6px;

	content: '';

	display: block;

	height: 0;

	left: 118px;

	position: absolute;

	width: 0;
}

.tooltip-content strong {
	color: #fff;

	margin-bottom: 1.5em;
}

.tooltip-content > p {
	line-height: 1.5em;

	margin: 0 0 0.5em;

	text-align: left;
}

.tooltip-content > p:last-child {
	margin-bottom: 0;
}

.footer.container-tight {
	padding: 30px 0;

	border-top: 1px solid #333;
}

.footer.container-tight1 {
	padding: 30px 0;
}

.footer h3 {
	font-size: 1.2em;

	margin: 0 0 0.3em;
}

.footer-links .puff {
	margin-bottom: 1em;

	margin-right: 15px;

	width: 22%;
}

.footer-links .puff-last {
	float: right;
}

.footer-links li {
	font-size: 1em;

	line-height: 2em;
}

.footer-links a {
	color: #a1a1a1;

	text-decoration: none;
}

.footer-links a:hover,
.footer-links-active a {
	color: #fff;

	text-decoration: underline;
}

.footer-links-community a {
	/*font-weight: 700;*/
}

.social-icon {
	background-color: #ed1c24;

	border-radius: 24px;

	color: #fff;

	float: left;

	font-family: 'LigatureSymbols';

	font-size: 1.2rem;

	height: 35px;

	letter-spacing: -0.1em;

	line-height: 2.15em;

	text-align: center;

	width: 35px;

	-webkit-font-smoothing: antialiased;

	-moz-osx-font-smoothing: grayscale;

	margin-right: 15px;
}

.social-icon-twitter {
	background-color: #16b3f4;
}

.social-icon-facebook {
	background-color: #4f72ce;
}

.social-icon-gplus {
	background-color: #ec332d;
}

.social-icon-linkdin {
	background-color: #007ab5;
}

.social-icon-instagram {
	/* background-color: #a63795 */

	background: radial-gradient(circle farthest-corner at 35% 90%, #fec564, transparent 50%),
		radial-gradient(circle farthest-corner at 0 140%, #fec564, transparent 50%),
		radial-gradient(ellipse farthest-corner at 0 -25%, #5258cf, transparent 50%),
		radial-gradient(ellipse farthest-corner at 20% -50%, #5258cf, transparent 50%),
		radial-gradient(ellipse farthest-corner at 100% 0, #893dc2, transparent 50%),
		radial-gradient(ellipse farthest-corner at 60% -20%, #893dc2, transparent 50%),
		radial-gradient(ellipse farthest-corner at 100% 100%, #d9317a, transparent),
		linear-gradient(#6559ca, #bc318f 30%, #e33f5f 50%, #f77638 70%, #fec66d 100%);
}

.social-bubble {
	background-color: #fff;

	border-radius: 3px;

	color: #000;

	float: left;

	font-size: 12px;

	font-weight: 700;

	margin: 10px 24px 0 11px;

	padding: 6px 12px;

	position: relative;
}

.social-bubble:before {
	left: -10px;

	border: solid transparent;

	content: '';

	height: 0;

	width: 0;

	position: absolute;

	border-right-color: #fff;

	border-width: 6px;

	top: 50%;

	margin-top: -5px;
}

.social-share-block {
	width: 160px;

	position: absolute;

	right: 15px;

	top: -24px;

	padding: 30px 0 0 120px;
}

.social-share-text {
	background: url(../../images/img-share-page.png) 10px 0 no-repeat;

	height: 50px;

	position: absolute;

	width: 135px;

	top: 0;

	left: 0;
}

.social-share-text-swe {
	background: url(../../images/img-share-page-swe.png) no-repeat;
}

.social-share-block .social-item {
	display: inline-block;

	margin-right: 0.1em;

	padding: 0;
}

.social-share-block .social-item:last-child {
	margin-right: 0;
}

.container-quote {
	text-align: center;

	max-width: 770px;

	margin: 40px auto 60px;
}

.container-quote a:hover {
	text-decoration: none;
}

.quote-text {
	color: #000;

	font-size: 1.5em;

	font-weight: 100;

	letter-spacing: -1px;

	line-height: 1.7em;

	margin: 0;
}

.quote-text > a {
	position: relative;

	top: -1em;

	font-weight: inherit;
}

.quote-source {
	color: #000;

	font-weight: 700;
}

.quote-source-extra {
	color: #999;

	color: rgba(0, 0, 0, 0.6);

	font-weight: 100;
}

.contact-container12 {
	position: absolute;

	left: 0;

	width: 57%;

	border-right: 1px solid #333333;
}

.contact-container {
	font-size: 1em;

	white-space: nowrap;

	position: absolute;

	right: 0;

	top: -11px;
}

.contact {
	display: inline-block;

	margin-left: 50px;

	position: relative;

	text-align: left;

	line-height: 21px;
}

.contact a {
	color: inherit;
}

a.contact-phone-icon {
	-webkit-border-radius: 24px;

	-moz-border-radius: 24px;

	border-radius: 24px;

	border: 2px solid #fff;

	color: #fff;

	display: block;

	font-family: pingdom-iconsregular;

	font-size: 1.3em;

	height: 44px;

	line-height: 2.7em;

	margin-top: -2em;

	position: absolute;

	right: 106%;

	text-align: center;

	top: 60%;

	width: 44px;
}

.contact a:hover {
	color: #fff;

	text-decoration: none;
}

.contact-phone {
	color: #fff;

	font-size: 1.2em;

	margin-bottom: 0;
}

.contact-email {
	margin-bottom: 0;
}

.contact-hours {
	float: right;
}

.slideshow-nav {
	height: 100%;

	padding-top: 30px;

	position: absolute;

	width: 10%;

	display: none;
}

.slideshow-nav:before {
	border-radius: 3em;

	border: 3px solid #e5e5e5;

	color: #fff;

	content: '\e008';

	font-family: 'LigatureSymbols';

	font-size: 14px;

	left: 50%;

	line-height: 3em;

	margin: -1.5em;

	position: absolute;

	text-align: center;

	top: 50%;

	width: 3em;

	-webkit-font-smoothing: antialiased;

	-moz-osx-font-smoothing: grayscale;
}

.slideshow-nav:hover:before {
	border-color: #000;

	color: #000;
}

.slideshow-nav:active:before {
	border-color: #fff;

	color: #fff;
}

.slideshow-nav-left {
	left: 0;
}

.slideshow-nav-right {
	right: 0;
}

.slideshow-nav-right:before {
	content: '\E112';
}

.slideshow-nav-left:before {
	content: '\E080';
}

.slideshow-item {
	background-position: 50% 100%;

	background-repeat: no-repeat;

	height: 431px;

	width: 792px;
}

.slideshow-item-asset {
	background-image: url(../images/asset-management.png);
}

.slideshow-item-hrm {
	background-image: url(../images/human-resource-management.png);
}

.slideshow-item-markd {
	background-image: url(../images/tatwa-markd.png);
}

.slideshow-item-pms {
	background-image: url(../images/project-monitoring-system.png);
}

.slideshow-item-online-form {
	background-image: url(../images/create-online-form.png);
}

.slideshow-item-micro-finanace {
	background-image: url(../images/Products-Microfinance-Solution1.png);
}

.slideshow-item-efficient {
	background-image: url(../images/Efficent-Procurement-Management-1.png);
}

.slideshow-item-apps1 {
	background-image: url(../images/services-system-integration.png);
}

.slideshow-item-email1 {
	background-image: url(../images/services-e-commerce.png);
}

.slideshow-item-multiple1 {
	background-image: url(../images/services-e-healthcare.png);
}

.slideshow-item-multiuser1 {
	background-image: url(../images/services-software-testing.png);
}

.slideshow-item-public1 {
	background-image: url(../images/services-digital-marketing.png);
}

.slideshow-item-report1 {
	background-image: url(../images/services-bpo.png);
}

.slideshow-item-root1 {
	background-image: url(../images/services-mobile-apps.png);
}

.slideshow-item-bpo {
	background-image: url(../images/services-bpo.png);
}

.slideshow-item-bpo {
	background-image: url(../images/services-bpo.png);
}

.slideshow-item-healthcare {
	background-image: url(../images/services-e-healthcare.png);
}

.slideshow-item-webtech {
	background-image: url(../images/services-webtech.png);
}

.slideshow-item-digital {
	background-image: url(../images/services-digital-marketing.png);
}

.slideshow-item-SAP {
	background-image: url(../images/service-sap.png);
}

.slideshow-item-itconsulting {
	background-image: url(../images/service-IT-Consulting.png);
}

.rslides {
	float: left;

	height: 431px;

	list-style: none;

	margin: 30px 0 0 5%;

	overflow: hidden;

	padding: 0;

	position: relative;

	width: 90%;
}

.rslides li {
	display: block;

	left: 0;

	position: absolute;

	top: 0;

	width: 100%;

	-webkit-backface-visibility: hidden;
}

.rslides li:first-child {
	display: block;

	float: left;

	position: relative;
}

.rslides img {
	border: 0;

	display: block;

	float: left;

	height: auto;

	width: 100%;
}

.container-trustedby {
	color: #999;

	padding: 7px 0 0;
}

.trustedby-text {
	font-size: 0.75em;

	font-weight: 700;

	line-height: 6.6em;

	padding-top: 0;

	text-transform: uppercase;

	margin: 0;
}

.trustedby-logos {
	font-size: 5em;

	height: 1em;

	margin-top: -0.56em;

	position: absolute;

	right: 0;

	text-align-last: justify;

	text-align: justify;

	top: 50%;

	width: 86%;
}

.trustedby-logos:after {
	content: '';

	display: inline-block;

	width: 100%;
}

.facebook-sprite {
	background: url(../../images/sprite-facebook.png) no-repeat;

	position: absolute;
}

#facebook-people {
	height: 176px;

	width: 142px;
}

#facebook-text {
	background-position: -142px 0;

	height: 87px;

	width: 105px;

	left: 170px;

	top: 46px;
}

#facebook-logo {
	color: #3c5a96;

	font-size: 60px;

	position: absolute;

	right: 24px;

	top: 15px;
}

#facebook-box {
	height: 150px;

	margin: 0 0 25px 270px;

	overflow: hidden;
}

.fb-like-box {
	display: block !important;

	margin-left: -10px;
}

#facebook-box iframe,
.fb-like-box > span {
	width: 100% !important;
}

.message-container {
	background-color: #44a5f2;

	-webkit-border-radius: 3px;

	-moz-border-radius: 3px;

	border-radius: 3px;

	color: #fff;

	left: 50%;

	line-height: 28px;

	margin: 0 0 0 -495px;

	opacity: 0;

	padding: 6px 12px 5px 2.9em;

	position: absolute;

	text-indent: -1.9em;

	top: 261px;

	-webkit-transition: visibility 251ms linear, opacity 250ms ease;

	-moz-transition: visibility 251ms linear, opacity 250ms ease;

	transition: visibility 251ms linear, opacity 250ms ease;

	visibility: hidden;

	width: 636px;

	z-index: 20;
}

.message-container-warning {
	background-color: #ff473e;
}

.message-icon {
	display: none;

	font-size: 1em;

	margin-right: 0.5em;
}

.message-container-warning .message-icon {
	display: inline;
}

.message-visible {
	opacity: 1;

	visibility: visible;

	-webkit-transition: visibility 0s linear, opacity 250ms ease;

	-moz-transition: visibility 0s linear, opacity 250ms ease;

	transition: visibility 0s linear, opacity 250ms ease;
}

.message-fixed {
	-webkit-border-radius: 0 0 3px 3px;

	-moz-border-radius: 0 0 3px 3px;

	border-radius: 0 0 3px 3px;

	border-top: none;

	position: fixed;

	top: 80px;
}

.message-close {
	direction: ltr;

	position: absolute;

	right: 10px;

	height: 14px;

	margin: 2px 0 0;

	opacity: 0.5;

	text-decoration: none;

	text-indent: -2000px;

	width: 14px;
}

.message-error {
	background: url(../images/icon-alerts-red.png) 15px 8px no-repeat #f46f2a;

	color: #fff;

	border: none;
}

.lightbox-shield {
	background-color: rgba(0, 0, 0, 0.7);

	bottom: 0;

	display: none;

	left: 0;

	position: fixed;

	right: 0;

	top: 0;

	opacity: 0;

	transition: opacity 100ms;

	z-index: 200001;
}

.lightbox-open {
	opacity: 1;

	transition: opacity 250ms;
}

.lightbox-content {
	background-color: #fff;

	box-shadow: 0 5px 40px 2px rgba(0, 0, 0, 0.5);

	height: 100px;

	left: 50%;

	margin: -50px -100px;

	position: fixed;

	top: 50%;

	width: 200px;
}

.lightbox-close {
	background: url(/../images/lightbox-close.png) no-repeat;

	cursor: pointer;

	height: 23px;

	opacity: 0.5;

	position: absolute;

	right: 20px;

	top: 16px;

	-webkit-transition: all 100ms linear;

	-moz-transition: all 100ms linear;

	transition: all 100ms linear;

	width: 23px;
}

.lightbox-close:hover {
	opacity: 1;

	-webkit-transform: rotate(90deg);

	-moz-transform: rotate(90deg);

	transform: rotate(90deg);
}

.livechat-mobile {
	background: url(/../images/img-livechat-mobile.png) no-repeat;

	display: none;

	height: 32px;

	margin: 1em auto 0;

	width: 104px;
}

.twitter-typeahead .tt-hint,
.twitter-typeahead .tt-query {
	margin-bottom: 0;
}

.tt-dropdown-menu {
	min-width: 160px;

	margin-top: 2px;

	padding: 5px 0;

	background-color: #fff;

	border: 1px solid #ccc;

	border: 1px solid rgba(0, 0, 0, 0.2);

	*border-right-width: 2px;

	*border-bottom-width: 2px;

	-webkit-border-radius: 6px;

	-moz-border-radius: 6px;

	border-radius: 6px;

	-webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);

	-moz-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);

	box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);

	-webkit-background-clip: padding-box;

	-moz-background-clip: padding;

	background-clip: padding-box;
}

.tt-suggestion {
	display: block;

	padding: 3px 20px;
}

.tt-suggestion.tt-is-under-cursor {
	color: #fff;

	background-color: #0081c2;

	background-image: -moz-linear-gradient(top, #08c, #0077b3);

	background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#08c), to(#0077b3));

	background-image: -webkit-linear-gradient(top, #08c, #0077b3);

	background-image: -o-linear-gradient(top, #08c, #0077b3);

	background-image: linear-gradient(to bottom, #08c, #0077b3);

	background-repeat: repeat-x;

	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff0088cc', endColorstr='#ff0077b3', GradientType=0);
}

.tt-suggestion.tt-is-under-cursor a {
	color: #fff;
}

.tt-suggestion p {
	margin: 0;
}

.button-contact {
	font-size: 16px;

	line-height: normal;

	margin-top: -1.75em;

	margin-left: 2em;

	position: absolute;

	right: 20px;

	top: 50%;
}

.button-contact:active {
	position: absolute;

	top: 50%;
}

.button-contact:before {
	background: url(/../images/img-have-a-question.png) no-repeat;

	content: '';

	height: 49px;

	left: -125px;

	position: absolute;

	top: -0.7em;

	width: 112px;
}

.contact-callUs {
	margin: 0.5em 0;

	font-size: 1.5em;
}

.contact-callUs strong {
	color: #fff;
}

.transition-none {
	-webkit-transition: none !important;

	-moz-transition: none !important;

	transition: none !important;
}

.templates {
	display: none;
}

.container-solarwinds-banner {
	padding-top: 10px;

	padding-bottom: 15px;

	text-align: center;

	font-size: 18px !important;
}

.container-solarwinds-banner a {
	text-decoration: underline;
}

.container-solarwinds-banner-logo {
	display: inline-block;

	width: 120px;

	height: 27px;

	background-image: url(../../images/press/sw-logo.png?oh91br);
}

.popup-modal-press-release {
	overflow: visible;

	max-width: 710px;

	-webkit-border-top-right-radius: 0;

	-moz-border-radius-topright: 0;

	border-top-right-radius: 0;
}

.popup-fixed-modal-wrapper {
	display: block;

	position: fixed;

	overflow-x: hidden;

	overflow-y: scroll;

	top: 0;

	left: 0;

	bottom: 0;

	right: 0;

	z-index: 3999;

	-webkit-overflow-scrolling: touch;
}

.popup-fixed-modal-wrapper > * {
	-webkit-transform: translateZ(0);

	-moz-transform: translateZ(0);

	-o-transform: translateZ(0);

	-ms-transform: translateZ(0);

	transform: translateZ(0);
}

.popup-fixed-modal-wrapper .popup-modal-close {
	z-index: 16000;
}

.popup-fixed-modal-wrapper .popup-modal {
	position: relative;

	left: 0;

	top: 0;

	margin: 0 auto;
}

.table-hack {
	display: table;

	height: 100%;
}

.table-hack-cell {
	display: table-cell;

	vertical-align: middle;

	height: 100%;
}

.enterprise-contact-modal {
	max-width: 1104px;

	width: 100%;

	border-radius: 0;

	padding: 0;

	background: #f7f7f7;
}

.enterprise-contact-modal .popup-modal-heading {
	background: #111;

	color: #fff000;

	margin: 0;

	border: none;

	padding: 20px 50px;

	font-weight: 700;
}

.enterprise-contact-modal .popup-modal-content {
	padding: 0;
}

.enterprise-modal-form-top-normal .enterprise-modal-form-top-bg {
	background: url(/../images/enterprise/contact-form/bg-top.jpg) no-repeat;

	width: 100%;

	height: 170px;
}

.enterprise-modal-form-top-mobile .enterprise-modal-form-top-bg {
	background: #000 url(/../images/enterprise/contact-form/bg-top-mobile.jpg) no-repeat;

	min-height: 170px;

	background-position: 0 center;

	background-size: cover;
}

.enterprise-contact-top-inner {
	color: #fff;

	text-align: right;

	line-height: 66px;

	padding: 52px 46px;
}

.enterprise-modal-form-heading {
	font-size: 1.5em;

	line-height: 1.3em;
}

.enterprise-modal-form-free-call-text {
	color: #fff000;

	display: inline-block;

	position: relative;
}

.enterprise-modal-form-free-call-text::before {
	content: '';

	display: block;

	position: absolute;

	bottom: -2px;

	left: 40px;

	background: url(/../images/enterprise/contact-form/sprite-enterprise-modal-form.png) no-repeat;

	background-position: 0 -42px;

	width: 107px;

	height: 5px;
}

.enterprise-modal-form-free-call-text::after {
	content: '';

	display: block;

	position: absolute;

	top: -26px;

	right: -46px;

	background: url(/../images/enterprise/contact-form/sprite-enterprise-modal-form.png) no-repeat;

	background-position: 0 -20px;

	width: 56px;

	height: 20px;
}

.enterprise-contact-top-inner > ul {
	display: inline-block;

	vertical-align: middle;

	margin-right: 28px;
}

.enterprise-call-now-button,
.enterprise-chat-button {
	display: inline-block;

	line-height: 26px;
}

.enterprise-call-now-button {
	margin-right: 8px;
}

.enterprise-modal-form-top-normal .enterprise-contact-info-list-container {
	background: #fff;
}

.enterprise-modal-form-container {
	box-shadow: inset 0 1px 3px #eee;
}

.enterprise-modal-form-container-inner {
	padding: 45px;
}

.enterprise-contact-info-list > li {
	display: inline-block;

	float: left;

	width: 33.3333%;
}

.enterprise-contact-info-list-mobile > li {
	margin-bottom: 34px;

	color: #fff;
}

.enterprise-contact-info-list-mobile .enterprise-contact-info-list-heading {
	color: #fff000;
}

.enterprise-contact-info-list-mobile-vertical-middle {
	line-height: 92px;

	float: left;

	max-width: 75%;
}

.enterprise-contact-info-list-mobile-vertical-middle-inner {
	display: inline-block;

	vertical-align: middle;
}

.enterprise-contact-info-inner {
	height: 70px;

	border-left: 1px solid #f7f7f7;

	border-right: 1px solid #f7f7f7;

	padding: 20px 0 20px 22px;
}

.enterprise-contact-info-list > li:first-child .enterprise-contact-info-inner,
.enterprise-contact-info-list > li:last-child .enterprise-contact-info-inner {
	border: none;
}

.enterprise-modal-form-top-normal .enterprise-contact-info-small-text {
	color: #999;
}

.enterprise-modal-info-link {
	text-decoration: none;

	font-weight: 700;
}

.enterprise-contact-info-list-icon {
	float: left;

	padding-right: 10px;
}

.enterprise-contact-sprite {
	background: url(/../images/enterprise/contact-form/sprite-enterprise-modal-form.png);

	background-repeat: no-repeat;
}

.enterprise-contact-sprite-close-modal {
	background-position: 0 0;

	width: 18px;

	height: 18px;
}

.enterprise-contact-sprite-handrawn-arrow {
	background-position: 0 -20px;

	width: 56px;

	height: 20px;
}

.enterprise-contact-sprite-handrawn-lines {
	background-position: 0 -42px;

	width: 107px;

	height: 5px;
}

.enterprise-contact-sprite-ico-call-cta {
	background-position: 0 -49px;

	width: 20px;

	height: 22px;
}

.enterprise-contact-sprite-ico-email {
	background-position: 0 -73px;

	width: 70px;

	height: 70px;
}

.enterprise-contact-sprite-ico-phone {
	background-position: 0 -145px;

	width: 70px;

	height: 70px;
}

.enterprise-contact-sprite-ico-time {
	background-position: 0 -217px;

	width: 70px;

	height: 70px;
}

.enterprise-contact-sprite-mobile {
	background: url(/../images/enterprise/contact-form/sprite-enterprise-modal-form-mobile.png);

	background-repeat: no-repeat;
}

.enterprise-contact-sprite-close-modal-mobile {
	background-position: 0 0;

	width: 22px;

	height: 22px;
}

.enterprise-contact-sprite-ico-mail-mobile {
	background-position: 0 -24px;

	width: 92px;

	height: 92px;
}

.enterprise-contact-sprite-ico-phone-mobile {
	background-position: 0 -118px;

	width: 92px;

	height: 92px;
}

.enterprise-contact-sprite-ico-time-mobile {
	background-position: 0 -212px;

	width: 92px;

	height: 92px;
}

.enterprise-modal-form-puff-2 {
	width: 48.5%;

	margin-right: 1.5%;

	float: left;
}

.enterprise-modal-form-puff-2 + .enterprise-modal-form-puff-2 {
	margin-right: 0;

	margin-left: 1.5%;
}

#enterprise-modal-form .input-row {
	margin-bottom: 0;
}

#enterprise-modal-form .input-text,
#enterprise-modal-form .twitter-typeahead {
	background: #fff;
}

#enterprise-modal-form .input-select-wrapper {
	display: block;
}

#enterprise-modal-form textarea[name='message'] {
	resize: none;

	height: 13.8em;
}

.enterprise-modal-form-inner {
	margin-bottom: 15px;
}

.enterprise-modal-form-top-mobile {
	display: none;

	color: #fff;
}

.enterprise-modal-form-top-mobile .enterprise-modal-form-top {
	padding: 65px 20px;

	font-size: 1.4em;
}

.enterprise-modal-form-top-mobile h3 {
	color: #fff000;
}

.enterprise-modal-submit-container {
	text-align: right;

	-webkit-transition: opacity 200ms ease;

	-o-transition: opacity 200ms ease;

	-moz-transition: opacity 200ms ease;

	transition: opacity 200ms ease;

	opacity: 1;
}

.enterprise-modal-submit-container-hidden {
	opacity: 0;
}

.enterprise-modal-checkbox-container {
	display: inline-block;

	margin-right: 20px;
}

.enterprise-submit-wrapper {
	display: inline-block;
}

.enterprise-modal-submit-sent-container .enterprise-modal-input-submit,
.enterprise-modal-submit-sent-container .enterprise-submit-wrapper {
	color: transparent;

	text-shadow: none;

	position: relative;
}

.enterprise-modal-submit-sent-container .enterprise-modal-input-submit {
	background-color: #57c700;

	box-shadow: 0 3px 0 #4db300, 0 6px 2px rgba(0, 0, 0, 0.2);
}

.enterprise-modal-submit-sent-container .enterprise-submit-wrapper::before {
	content: ' ';

	z-index: 1;

	position: absolute;

	display: block;

	left: 50%;

	top: 50%;

	margin-top: -12px;

	margin-left: -12px;

	background: url(/../images/enterprise/contact-form/contact-modal-loader.gif) no-repeat;

	width: 24px;

	height: 24px;
}

.enterprise-modal-submit-message-container img {
	display: inline-block;

	vertical-align: middle;
}

.press-release-intro {
	font-style: italic;
}

.press-release-list {
	list-style-type: disc;
}

.press-release-list li {
	margin-bottom: 0;
}

.press-release-header {
	margin-bottom: 0;

	font-size: 1.1em;

	font-family: 'Helevetica Neue', Helvetica, Arial;

	font-weight: 700;
}

.press-release-small {
	font-size: 0.8em;
}

.embargo-exclamation {
	font-size: 10em;

	text-align: center;

	margin-bottom: 30px;

	color: #000;
}

body > .zopim {
	display: none;
}

body > .zopim.pingdom-zopim {
	display: block;
}

.clientcards-list {
	height: 590px;

	margin-top: 60px;

	overflow: hidden;

	-webkit-perspective: 1000;

	-moz-perspective: 1000;

	perspective: 1000;

	-webkit-transition: height 500ms ease;

	-moz-transition: height 500ms ease;

	transition: height 500ms ease;
}

.clientcards-list-expanded {
	height: 1190px;

	-webkit-transition: height 1500ms ease;

	-moz-transition: height 1500ms ease;

	transition: height 1500ms ease;
}

.clientcards-item {
	background-color: #fff;

	color: #999;

	float: left;

	height: 190px;

	line-height: 172px;

	margin: 0 1% 8px 0;

	text-align: center;

	width: 19%;

	-webkit-transform: rotateY(0deg);

	-moz-transform: rotateY(0deg);

	transform: rotateY(0deg);

	-webkit-transform-style: preserve-3d;

	-moz-transform-style: preserve-3d;

	transform-style: preserve-3d;

	-webkit-backface-visibility: hidden;

	-moz-backface-visibility: hidden;

	backface-visibility: hidden;

	-webkit-transition: all 400ms ease;

	transition: all 400ms ease;
}

.clientcards-item-hidden {
	-webkit-transform: rotateY(180deg);

	-moz-transform: rotateY(180deg);

	transform: rotateY(180deg);
}

.clientcards-item:nth-child(5n) {
	margin-right: 0;
}

.clientcards-toggle {
	width: 186px;

	text-align: center;

	border: 2px solid #e8e8e8;

	height: 60px;

	line-height: 60px;

	display: block;

	margin: 55px auto 0;
}

.clientcards-toggle:hover {
	border-color: #ccc;
}

@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 168dpi) {
	.button-base {
		font-weight: 700;
	}

	.loading-spinner-small {
		background: url(/../images/animation-loading.gif) no-repeat;

		background-size: 16px auto;
	}

	.loading-spinner-small-sprite {
		background: url(/../images/sprite-loading.png) no-repeat;

		background-size: 16px auto;
	}

	.packages-icon-check {
		background-image: url(../../images/pricing/<EMAIL>?oh91br);
	}

	.packages-icon-x {
		background-image: url(../../images/pricing/<EMAIL>?oh91br);
	}

	.packages-icon-plus {
		background-image: url(../../images/pricing/<EMAIL>?oh91br);
	}

	.facebook-sprite {
		background-image: url(../../images/<EMAIL>);

		background-size: 246px auto;
	}

	.slideshow-item {
		background-size: 89%;
	}

	.slideshow-item-apps {
		background-image: url(../../images/slideshow-features/<EMAIL>);
	}

	.slideshow-item-email {
		background-image: url(../../images/slideshow-features/<EMAIL>);
	}

	.slideshow-item-multiple {
		background-image: url(../../images/slideshow-features/<EMAIL>);
	}

	.slideshow-item-multiuser {
		background-image: url(../../images/slideshow-features/<EMAIL>);
	}

	.slideshow-item-public {
		background-image: url(../../images/slideshow-features/<EMAIL>);
	}

	.slideshow-item-report {
		background-image: url(../images/healthcare-management.png);
	}

	.slideshow-item-root {
		background-image: url(../../images/slideshow-features/<EMAIL>);
	}

	.packages-discount-personal {
		background-image: url(../../images/discount/<EMAIL>?jh35hg);

		background-size: 98px auto;
	}

	.packages-discount-pro {
		background-image: url(../../images/discount/<EMAIL>?jh35hg);

		background-size: 124px auto;
	}

	.packages-discount-team {
		background-image: url(../../images/discount/<EMAIL>?oh91br);

		background-size: 98px auto;
	}

	.packages-discount-starter {
		background: url(../../images/pricing/<EMAIL>?jh35hg) no-repeat;

		background-size: 98px auto;
	}

	.packages-discount-standard {
		background: url(../../images/pricing/<EMAIL>?jh35hg) no-repeat;

		background-size: 124px auto;
	}

	.packages-discount-professional {
		background: url(../../images/pricing/<EMAIL>?oh91br) no-repeat;

		background-size: 124px auto;
	}

	.lightbox-close {
		background-image: url(/../images/<EMAIL>);

		background-size: 23px auto;
	}

	.livechat-mobile {
		background-image: url(/../images/<EMAIL>);

		background-size: 104px auto;
	}

	.button-contact:before {
		background-image: url(/../images/<EMAIL>);

		background-size: 112px auto;
	}

	.enterprise-contact-sprite,
	.enterprise-modal-form-free-call-text::after,
	.enterprise-modal-form-free-call-text::before {
		background-image: url(/../images/enterprise/contact-form/<EMAIL>);

		background-size: 107px auto;
	}

	.enterprise-contact-sprite-mobile {
		background-image: url(/../images/enterprise/contact-form/<EMAIL>);

		background-size: 92px auto;
	}

	.social-share-text {
		background-image: url(../../images/<EMAIL>);

		background-size: 112px auto;
	}

	.social-share-text-swe {
		background: url(../../images/<EMAIL>) no-repeat;

		background-size: 135px auto;
	}

	.container-solarwinds-banner-logo {
		background-image: url(../../images/press/<EMAIL>?oh91br);

		background-size: 120px auto;
	}
}

@media (min-width: 976px) {
	.puff-3-narrow:nth-of-type(3n + 1),
	.puff-3:nth-of-type(3n + 1) {
		margin-left: 0;

		clear: left;
	}

	.visible-phone,
	.visible-tablet {
		display: none !important;
	}

	.visible-desktop {
		display: inherit !important;
	}

	.hidden-desktop {
		display: none !important;
	}
}

@media (max-device-width: 1024px) and (orientation: landscape) {
	#top {
		position: absolute;

		top: 0;
	}

	.input-container-fixed > h3 {
		top: 0;
	}

	.input-container-fixed-bottom > h3 {
		bottom: 0;

		top: auto;
	}

	.message-container {
		left: 0;

		margin: 0;

		right: 0;

		top: 250px;

		width: auto;
	}

	.message-fixed {
		top: 0;
	}

	.enterprise-contact-modal {
		min-width: 0;
	}

	.enterprise-contact-sprite {
		background-size: 80.25px 215.25px;

		margin: 8px 0;
	}

	.enterprise-contact-sprite-ico-phone {
		background-position: 0 -109px;

		width: 52.5px;

		height: 52.5px;
	}

	.enterprise-contact-sprite-ico-time {
		background-position: 0 -163px;

		width: 52.5px;

		height: 52.5px;
	}

	.enterprise-contact-sprite-ico-email {
		background-position: 0 -55px;

		width: 52.5px;

		height: 52.5px;
	}

	.enterprise-contact-info-inner {
		padding: 14px 0 14px 16px;

		font-size: 0.8em;
	}
}

@media (max-width: 975px) and (min-width: 741px) {
	.packages-price-discountinfo {
		max-width: 13em;

		font-size: 10px;
	}

	.packages-price-paybyinvoice {
		margin-left: -85px;
	}

	.puff-3:nth-of-type(2n + 1) {
		clear: left;

		margin-left: 0;
	}

	.puff-3-narrow:nth-of-type(3n + 1) {
		margin-left: 0;

		clear: left;
	}

	.packages-price-currency {
		margin-top: -0.3em;
	}

	.packages-price-whole {
		font-size: 45px;
	}

	.packages-price-decimals {
		font-size: 16px;

		margin-top: -10px;
	}

	.packages-discount-personal {
		background-size: 90px auto;

		right: -5%;

		top: -47px;
	}

	.packages-discount-pro {
		background-size: 100px auto;

		right: -28px;

		top: -39px;
	}

	.packages-discount-team {
		right: -32px;

		top: -58px;
	}

	.packages-features-item {
		font-size: 0.7em;
	}

	.packages-item .button-base {
		font-size: 13px;
	}
}

@media (max-width: 975px) {
	.noscroll {
		overflow: hidden;

		position: fixed;
	}

	h1 {
		font-size: 36px;
	}

	h3 {
		margin: 0 0 0.5em;
	}

	.h3-slim + .h3-slim {
		margin-top: 0.5em;
	}

	#top {
		position: absolute;

		top: 0;
	}

	.input-container-fixed > h3 {
		top: 0;
	}

	.input-container-fixed-bottom > h3 {
		bottom: 0;

		top: auto;
	}

	.info {
		width: 100%;

		float: none;

		margin-bottom: 30px;
	}

	.info:last-child {
		margin-bottom: 70px;
	}

	.info-row-last .info {
		margin-bottom: 0;
	}

	.info-tight {
		margin: 0;
	}

	.container-inner {
		margin: 0 20px;
	}

	.container-map {
		margin: 0;

		padding: 0;
	}

	.container-quote {
		margin: 30px auto;
	}

	.container-promo {
		margin: 0;
	}

	.container-promo-inner {
		line-height: 2.5em;

		margin: 0 40px;

		padding: 1em 0;

		text-align: center;
	}

	.container-promo .button-base {
		position: relative;

		top: -0.2em;
	}

	.quote-text {
		font-size: 1.2em;
	}

	.container-customers {
		padding-top: 50px;
	}

	#customers-logos,
	.container-customers {
		-webkit-transition: none;

		-moz-transition: none;

		transition: none;
	}

	.menu-item {
		font-size: 0.95em;

		padding: 0;
	}

	#facebook-box {
		height: 202px;
	}

	#facebook-box iframe,
	.fb-like-box > span {
		height: 202px !important;

		width: 100% !important;
	}

	.fb-like-box,
	.fb-like-box > span {
		display: block !important;

		margin-left: 0;

		width: 100% !important;
	}

	.container-slideshow {
		margin-bottom: -3em;
	}

	.rslides {
		height: 320px;

		margin: 30px 0 20px 7%;

		width: 86%;
	}

	.slideshow-item {
		background-size: 570px auto;

		height: 320px;
	}

	.packages-list {
		width: auto;

		font-size: 0.9em;
	}

	.packages-discount-personal {
		left: auto;

		right: -1%;

		top: -55px;
	}

	.packages-discount-pro {
		right: -7px;

		top: -64px;
	}

	.packages-tagline {
		margin-bottom: 2.8em;
	}

	.puff {
		width: 46%;
	}

	.puff:nth-of-type(even) {
		margin-right: 0;
	}

	.puff-last {
		margin-bottom: 20px;
	}

	.puff-3 {
		margin-left: 8%;
	}

	.puff-3-narrow {
		margin-left: 5%;

		width: 30%;
	}

	.icon-customers {
		font-size: 5em;
	}

	.contact-container {
		float: none;

		margin: 1em 0 1.5em;

		text-align: center;

		position: relative;
	}

	.contact {
		width: auto;
	}

	.social-container {
		text-align: center;
	}

	.social-list {
		display: inline-block;
	}

	.social-item {
		display: inline-block;

		list-style: none;
	}

	.trustedby-logos {
		font-size: 4.3em;

		right: 1.5%;

		width: 82%;
	}

	.footer-links li {
		font-size: 1em;

		line-height: 1.7em;
	}

	.message-container {
		left: 0;

		margin: 0;

		right: 0;

		top: 280px;

		width: auto;
	}

	.message-fixed {
		top: 0;
	}

	.button-contact:before {
		display: none;
	}

	.clientcards-list {
		height: 405px;
	}

	.clientcards-list-expanded {
		height: 820px;
	}

	.clientcards-item {
		height: 130px;

		line-height: 110px;

		margin-bottom: 7px;
	}

	.packages-list-main .packages-shadow {
		height: 417px;
	}

	.enterprise-contact-modal {
		min-width: 0;
	}

	.enterprise-contact-sprite {
		background-size: 80.25px 215.25px;

		margin: 8px 0;
	}

	.enterprise-contact-sprite-ico-phone {
		background-position: 0 -109px;

		width: 52.5px;

		height: 52.5px;
	}

	.enterprise-contact-sprite-ico-time {
		background-position: 0 -163px;

		width: 52.5px;

		height: 52.5px;
	}

	.enterprise-contact-sprite-ico-email {
		background-position: 0 -55px;

		width: 52.5px;

		height: 52.5px;
	}

	.enterprise-contact-info-inner {
		padding: 14px 0 14px 16px;

		font-size: 0.8em;
	}

	.lightbox-content {
		margin: 30px auto;

		max-height: 95%;

		max-height: calc(100% - 50px);

		max-width: 100%;

		max-width: calc(100% - 20px);

		position: static;
	}

	.hidden-tablet,
	.visible-phone {
		display: none !important;
	}

	.visible-tablet {
		display: inherit !important;
	}

	.visible-desktop {
		display: none !important;
	}
}

@media (max-width: 740px) {
	.text-tagline {
		position: inherit;

		right: 0;
	}

	h1 {
		font-size: 26px;
	}

	h3 {
		font-size: 1.7em;

		line-height: 1.3em;
	}

	.text-group {
		white-space: normal;
	}

	.text-group-phone {
		white-space: nowrap;
	}

	blockquote {
		padding-bottom: 1em;
	}

	.quote-source-extra {
		white-space: nowrap;
	}

	.button-base {
		display: block;
	}

	.button-small {
		padding: 1em 0.8em;
	}

	.button-social {
		margin-bottom: 1em;
	}

	input.button-base {
		width: 100%;
	}

	.input-container {
		border-left: none;

		padding-left: 0;
	}

	.input-text {
		margin-bottom: 0.5em;

		width: 100%;
	}

	.twitter-typeahead {
		width: 100%;
	}

	.input-submit {
		margin-top: 1em;
	}

	.input-subinfo {
		margin-left: 0;

		width: auto;
	}

	a.input-link {
		margin-top: 0;
	}

	.input-select-wrapper.width-1-2,
	.input-text.width-1-2 {
		width: 100%;
	}

	.input.width-1-2 + .input.width-1-2 {
		margin-top: 0.5em;
	}

	.input-row {
		width: 100%;
	}

	.input-row-spacy {
		margin: 0 0 1em;
	}

	.label-left {
		line-height: 2.2em;

		width: 100%;
	}

	.label-right + .input-radio {
		margin-left: 0;
	}

	.info {
		margin-bottom: 20px;
	}

	.info:last-child {
		margin-bottom: 60px;
	}

	.info-row-last .info {
		margin-bottom: 0;
	}

	.info-tight {
		margin: 0;
	}

	.container {
		padding: 30px 0;
	}

	.container-inner {
		padding: 0 10px;
	}

	.container-promo-inner {
		font-size: 1.1em;

		line-height: 1.7em;

		margin: 0;

		padding: 1em 1em 1.5em;
	}

	.container-promo .button-base {
		display: block;

		margin: 1em 1em 0;

		position: static;
	}

	.container-quote {
		margin: 30px auto;
	}

	.puff {
		width: 100%;
	}

	.puff-first {
		margin-top: 0;
	}

	.puff > h3 {
		min-height: 0;
	}

	.puff-3,
	.puff-3-narrow {
		margin-left: 0;
	}

	a.menu-toggle {
		border: 1px solid #666;

		-webkit-border-radius: 4px;

		-moz-border-radius: 4px;

		border-radius: 4px;

		color: #666;

		font-family: 'LigatureSymbols';

		height: 30px;

		line-height: 32px;

		position: absolute;

		right: 0;

		text-align: center;

		top: 23px;

		width: 30px;

		-webkit-font-smoothing: none;
	}

	a.menu-toggle-active {
		border-color: #fff;

		color: #fff;
	}

	.menu-list {
		background: #000;

		left: -20px;

		margin-top: 0;

		position: absolute;

		right: -20px;

		top: 80px;

		overflow: hidden;

		height: 0;

		-webkit-transition: height 250ms ease;

		-moz-transition: height 250ms ease;

		transition: height 250ms ease;
	}

	.menu-item {
		display: list-item;
	}

	.menu-item:last-child {
		margin-bottom: 15px;
	}

	.menu-link {
		display: block;

		padding: 20px;
	}

	.input-container-fixed,
	.input-container-fixed-bottom {
		padding-top: 3em;
	}

	.input-checkbox-wrapper,
	.input-radio-wrapper {
		clear: both;
	}

	.input-radio-unit {
		margin: 0;

		width: 49%;
	}

	.icon-customers {
		font-size: 2em;
	}

	.container-customers {
		padding: 40px 0 70px;

		height: auto;

		transition: none;
	}

	.customers-text {
		font-size: 1.2em;
	}

	#customers-logos {
		font-size: 4em;
	}

	.customers-buttons {
		display: block;

		float: none;

		margin-top: 1em;
	}

	#facebook-people {
		top: -120px;

		right: 0;
	}

	#facebook-text {
		background-position: -142px -90px;

		left: 28px;

		top: -69px;
	}

	#facebook-logo {
		display: none;
	}

	#facebook-box {
		border: 1px solid #e6e6e6;

		height: auto;

		margin: 140px 0 0;

		overflow: hidden;

		padding-bottom: 12px;

		width: 100%;
	}

	#facebook-box iframe,
	.fb-like-box > span {
		height: 255px !important;
	}

	.container-qoute {
		margin-bottom: 40px;
	}

	.qoute-text {
		font-size: 16px;

		line-height: 2em;
	}

	#disclaimer {
		padding: 2.5em 0 0;

		text-align: center;
	}

	#disclaimer > span {
		display: block;

		float: none;

		margin: 0.7em 0;
	}

	.packages-discount-starter {
		top: -128px;

		right: -8px;
	}

	.packages-discount-professional,
	.packages-discount-standard {
		-moz-transform: scale(0.65);

		-webkit-transform: scale(0.65);

		transform: scale(0.65);

		top: -152px;

		right: -22px;
	}

	.packages-item-highlighted .packages-header {
		font-weight: 300;
	}

	.packages-list-main .packages-item-border-right {
		border: none !important;
	}

	.packages-list-main .packages-discount-starter {
		margin-top: 160px;
	}

	.packages-list-main .packages-discount-standard {
		margin-top: 180px;
	}

	.packages-list-main .packages-discount-professional {
		margin-top: 160px;
	}

	.packages-price-container {
		min-height: 0;
	}

	.packages-list {
		margin: 30px -30px;

		width: auto;
	}

	.packages-item {
		display: block;

		margin: 0 1em;

		overflow: hidden;

		text-align: center;

		width: auto;

		padding-bottom: 1em;
	}

	.packages-item + .packages-item {
		border-top: none;
	}

	.packages-item-highlighted {
		-webkit-border-radius: 0;

		-moz-border-radius: 0;

		border-radius: 0;

		width: auto;
	}

	.packages-item-highlighted .packages-header {
		padding-top: 0;
	}

	.packages-item:first-child {
		-webkit-border-radius: 0;

		-moz-border-radius: 0;

		border-radius: 0;

		border-right: 1px solid #d2d2d2;
	}

	.packages-item:last-child {
		-webkit-border-radius: 0;

		-moz-border-radius: 0;

		border-radius: 0;

		border-left: 1px solid #d2d2d2;
	}

	.packages-header {
		-webkit-border-radius: 0 !important;

		-moz-border-radius: 0 !important;

		border-radius: 0 !important;
	}

	.packages-item.packages-item-first,
	.packages-item.packages-item-last {
		border: 0;
	}

	.packages-price-currency {
		font-size: 20px;

		margin-top: -0.8em;
	}

	.packages-price-whole {
		margin-left: 15px;
	}

	.packages-price-decimals {
		font-size: 17px;

		margin: -18px 0;

		position: absolute;
	}

	.packages-price-period {
		font-size: 14px;
	}

	.packages-price {
		position: relative;
	}

	.packages-price-personal,
	.packages-price-pro,
	.packages-price-team {
		margin-right: 100px;
	}

	.packages-discount-personal {
		background-size: 100px auto;

		display: inline-block;

		left: auto;

		margin: -40px 5px;

		right: auto;

		top: auto;
	}

	.packages-discount-pro,
	.packages-discount-team {
		background-size: 90px auto;

		display: inline-block;

		margin: -35px 5px;

		right: auto;

		top: auto;
	}

	.packages-price-discountinfo {
		font-size: 0.9em;
	}

	.packages-shadow {
		display: none;
	}

	.footer-links {
		padding: 1.5em 0 0.5em;

		margin-top: 2em;
	}

	.footer-links h3 {
		font-size: 1.2em;

		margin-top: 0.1em;
	}

	.footer-links li {
		font-size: 1.1em;
	}

	.footer-links .puff {
		width: 100%;
	}

	.footer-links .puff-last {
		float: inherit;
	}

	.social-bubble {
		display: none;
	}

	.container-slideshow {
		margin: 0;

		padding: 0;
	}

	.slideshow-nav {
		display: none;
	}

	.rslides {
		margin: 0;

		width: 100%;
	}

	.rslides li {
		height: 160px;
	}

	.trustedby-text {
		line-height: 1em;

		padding-top: 0;
	}

	.trustedby-logos {
		font-size: 4.1em;

		height: 1.45em;

		margin: 0;

		position: static;

		width: 100%;
	}

	.message-container {
		top: 310px;

		padding-bottom: 12px;
	}

	.message-fixed {
		top: 0;
	}

	.social-share-block {
		margin-top: 30px;

		position: relative;

		right: 5px;

		top: 0;
	}

	.button-contact {
		margin: 0;

		position: static;
	}

	.button-contact:active {
		position: static;
	}

	.contact-callUs {
		font-size: 1.1em;

		margin: 0 0 1em;

		text-align: center;
	}

	.contact-callUs strong {
		white-space: nowrap;
	}

	.clientcards-list {
		margin-top: 20px;
	}

	.clientcards-item {
		font-size: 160%;

		height: 80px;

		line-height: 70px;

		margin-bottom: 3px;

		width: 32%;
	}

	.clientcards-item:nth-child(5n) {
		margin-right: 1%;
	}

	.clientcards-item:nth-child(3n) {
		margin-right: 0;
	}

	.clientcards-toggle,
	.enterprise-modal-form-top-normal {
		display: none;
	}

	.enterprise-modal-form-top-mobile {
		display: block;
	}

	.enterprise-modal-form-puff-2 {
		width: 100%;
	}

	.enterprise-modal-form-puff-2 + .enterprise-modal-form-puff-2 {
		margin: 0;
	}

	.enterprise-contact-modal {
		background: 0 0;
	}

	.enterprise-modal-form-container-inner {
		background: #f7f7f7;

		padding: 20px;
	}

	.enterprise-contact-modal .popup-modal-heading {
		display: none;
	}

	.enterprise-contact-modal .input-row {
		margin-bottom: 1em;
	}

	.enterprise-modal-form-container-inner > h3 {
		font-size: 1.418em;
	}

	.enterprise-modal-submit-container {
		text-align: left;
	}

	.enterprise-submit-wrapper {
		width: 100%;
	}

	.hidden-phone {
		display: none !important;
	}

	.visible-phone {
		display: inherit !important;
	}

	.visible-phone.discount {
		margin-top: 10px;
	}

	.visible-desktop,
	.visible-tablet {
		display: none !important;
	}

	#kayako_sitebadgecontainer {
		display: none;
	}
}

@media (max-width: 375px) and(min-width: 412px) {
	a.contact-phone-icon {
		top: 33% !important;
	}
}
