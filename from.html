<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">


<head>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-Y36RRN9KDY"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());

        gtag('config', 'G-Y36RRN9KDY');
    </script>lang="en">

    <head>

        <meta charset="UTF-8">

        <meta name="viewport" content="width=device-width, initial-scale=1.0">



        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/css/bootstrap.min.css" rel="stylesheet">

        <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.1/dist/js/bootstrap.min.js"
            integrity="sha384-Atwg2Pkwv9vp0ygtn1JAojH0nYbwNJLPhwyoVbhoPwBhjQPR5VtM2+xf0Uwh9KtT"
            crossorigin="anonymous"></script>

        <link rel="stylesheet"
            href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">



        <style>
            .cus-form {

                display: flex;

                flex-direction: column;

                justify-content: center;

                align-items: center;

            }

            .cus-inner-from {

                width: fit-content;

                padding: 20px;

                box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;

                border-radius: 10px;

            }

            .cus-button {

                padding-left: 40px;

                padding-right: 40px;

            }

            .cus-heading {

                background-color: #699dd2;

                color: white;

                border-radius: 4px;

            }
        </style>

        <title>Document</title>

    </head>

<body>

    <form>

        <div class="container">

            <div class="row">

                <form action="mailsent.php" method="post" enctype="multipart/form-data" class="cus-form">

                    <div class="cus-form">



                        <div class="cus-inner-from">

                            <p class="text-center fs-4 cus-heading">Please Fill up below form</p>

                            <div class="mb-3">

                                <input type="text" name="name" class="form-control" id="name"
                                    aria-describedby="emailHelp" placeholder="Enter your name">

                            </div>

                            <div class="mb-3">

                                <input type="email" name="email" class="form-control" id="email"
                                    aria-describedby="emailHelp" placeholder="Email address">

                            </div>

                            <div class="mb-3">

                                <input type="password" name="phone" class="form-control" id="phone"
                                    placeholder="Enter your contact number">

                            </div>



                            <div>

                                <select name="exp" class="form-select mb-3" aria-label="Default select example">

                                    <option selected>Select years of experiance</option>

                                    <option value="1">Fresher</option>

                                    <option value="2">1Yr's+</option>

                                    <option value="3">2Yr's+</option>

                                    <option value="3">3Yr's+</option>

                                    <option value="3">4Yr's+</option>

                                    <option value="3">5Yr's+</option>

                                    <option value="3">6Yr's+</option>

                                    <option value="3">10Yr's & Above</option>

                                </select>

                            </div>



                            <div>

                                <select name="position" class="form-select mb-3" aria-label="Default select example">

                                    <option selected>Choose your Position*</option>

                                    <option value="1">Fresher</option>

                                    <option value="2">1Yr's+</option>

                                    <option value="3">2Yr's+</option>

                                    <option value="3">3Yr's+</option>

                                    <option value="3">4Yr's+</option>

                                    <option value="3">5Yr's+</option>

                                    <option value="3">6Yr's+</option>

                                    <option value="3">10Yr's & Above</option>

                                </select>

                            </div>



                            <div>

                                <select name="ctc" class="form-select mb-3" aria-label="Default select example">

                                    <option selected>Current CTC*</option>

                                    <option value="1">1 Lakh</option>

                                    <option value="2">2Lakhs+</option>

                                    <option value="3">3Lakhs+</option>

                                    <option value="3">4Lakhs+</option>

                                    <option value="3">5Lakhs+</option>

                                    <option value="3">6Lakhs+</option>

                                    <option value="3">7-10Lakhs+</option>

                                    <option value="3">10Lakhs & Above</option>

                                </select>

                            </div>

                            <div style=" margin-bottom: 20px;">

                                <label class="mb-3" for="">Upload Resume</label> <br>

                                <input type="file" name="fileToUpload" id="fileToUpload" accept=".pdf, .docx" required>

                            </div>



                            <div style="margin-left: 0; margin-right: 0; text-align: center;">

                                <button type="submit" name="Send" id="send"
                                    class="btn btn-outline-info cus-button">Submit</button>

                            </div>

                        </div>

                    </div>

                </form>

            </div>

        </div>



    </form>

</body>

</html> -->