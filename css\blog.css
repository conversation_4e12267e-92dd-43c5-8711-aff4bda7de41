.blog-section {
  position: relative;
  padding: 100px 0px 80px;
}

.news-block {
  position: relative;
  margin-bottom: 30px;
}

.news-block .inner-box {
  position: relative;
}

.news-block .inner-box .image {
  position: relative;
  overflow: hidden;
  border-radius: 10px;
  background-color: #ff4c1c;
}

.news-block .inner-box .image img {
  position: relative;
  width: 100%;
  display: block;
  -webkit-transition: all 600ms ease;
  -ms-transition: all 600ms ease;
  -o-transition: all 600ms ease;
  -moz-transition: all 600ms ease;
  transition: all 600ms ease;
}

.news-block .inner-box:hover .image img {
  opacity: 0.5;
  transform: scale(1.05, 1.05);
}

.news-block .inner-box .lower-content {
  position: relative;
  padding: 20px 20px;
  border-radius: 10px;
  margin-right: 60px;
  margin-top: -65px;
  overflow: hidden;
  background-color: #ffffff;
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.15);
  border-top-left-radius: 0;
}

.news-block .inner-box .lower-content .color-layer {
  position: absolute;
  left: 0px;
  top: 0px;
  bottom: 0px;
  width: 10%;
  opacity: 0;
  -webkit-transition: all 600ms ease;
  -ms-transition: all 600ms ease;
  -o-transition: all 600ms ease;
  -moz-transition: all 600ms ease;
  transition: all 600ms ease;
  background: #0044cb;
  background: -webkit-linear-gradient(to left, #0044cb 0%, #00c7f9 100%);
  background: -moz-linear-gradient(to left, #0044cb 0%, #00c7f9 100%);
  background: linear-gradient(to left, #0044cb 0%, #00c7f9 100%);
}

.news-block .inner-box:hover .lower-content .color-layer {
  width: 100%;
  opacity: 1;
}

.news-block .inner-box .lower-content .post-meta {
  position: relative;
  -webkit-transition: all 600ms ease;
  -ms-transition: all 600ms ease;
  -o-transition: all 600ms ease;
  -moz-transition: all 600ms ease;
  transition: all 600ms ease;
  padding-left: 0px;
}

.news-block .inner-box:hover .lower-content .post-meta {
  transform: translateY(-25px);
}

.news-block .inner-box .lower-content .post-meta li {
  position: relative;
  color: #000000;
  font-size: 14px;
  margin-right: 15px;

  display: inline-block;
}

.news-block .inner-box .lower-content .post-meta li:last-child {
  margin-right: 0px;
}

.news-block .inner-box .lower-content h5 {
  margin-top: 8px;
  -webkit-transition: all 600ms ease;
  -ms-transition: all 600ms ease;
  -o-transition: all 600ms ease;
  -moz-transition: all 600ms ease;
  transition: all 600ms ease;
}

.news-block .inner-box:hover .lower-content h5 {
  transform: translateY(-30px);
}

.news-block .inner-box .lower-content h5 a {
  position: relative;
  color: #000c3f;
  -webkit-transition: all 300ms ease;
  -ms-transition: all 300ms ease;
  -o-transition: all 300ms ease;
  -moz-transition: all 300ms ease;
  transition: all 300ms ease;
  text-decoration: none;
}

.news-block .inner-box:hover .lower-content h5 a {
  color: #ffffff;
}

.news-block .inner-box .read-more {
  position: relative;
  color: #ffffff;
  font-size: 15px;
  font-weight: 600;
  display: inline-block;
  -webkit-transition: all 600ms ease;
  -ms-transition: all 600ms ease;
  -o-transition: all 600ms ease;
  -moz-transition: all 600ms ease;
  transition: all 600ms ease;
}

.news-block .inner-box:hover .read-more {
  transform: translateY(-28px);
  text-decoration: none;
}

.news-block .inner-box .read-more span {
  position: relative;
  top: 1px;
}

.news-block .inner-box .lower-content .content {
  position: relative;
  height: 88px;
  overflow: hidden;
}

.footer_top {
  background-image: url(../images/footer_top.jpg);

  background-repeat: no-repeat;

  background-size: cover;

  width: 100%;

  height: 288px;

  margin-top: 0px;
}

.arial38 {
  font-size: 38px;

  color: #fff;

  text-align: center;

  padding-top: 83px;
}

.button_area {
  width: fit-content;
  display: flex;

  height: auto;

  margin: 0 auto;
}

.button_blue {
  width: auto;

  height: auto;

  float: left;

  text-align: center;

  padding: 14px 20px;

  margin-right: 20px;

  border: none;

  border-radius: 6px;

  -moz-border-radius: 6px;

  -webkit-border-radius: 6px;

  -ms-border-radius: 6px;

  -o-border-radius: 6px;

  color: #fff !important;

  font-size: 16px;

  text-transform: uppercase;

  background-color: #097ec1;

  text-decoration: none !important;
}

.button_blue:hover {
  cursor: pointer;

  background-color: #ffc600;

  color: #191300 !important;

  text-decoration: none !important;
}

.button_border {
  width: auto;

  height: auto;

  float: left;

  text-align: center;

  padding: 12px 18px;

  border: 2px solid #fff;

  border-radius: 6px;

  -moz-border-radius: 6px;

  -webkit-border-radius: 6px;

  -ms-border-radius: 6px;

  -o-border-radius: 6px;

  color: #fff !important;

  font-size: 16px;

  text-transform: uppercase;

  text-decoration: none !important;
}

.button_border:hover {
  cursor: pointer;

  background-color: #ffc600;

  color: #191300 !important;

  border: 2px solid #ffc600;
}

.mt20 {
  margin-top: 20px;
}

.mt50 {
  margin-top: 30px;
  margin-bottom: 30px;
}
