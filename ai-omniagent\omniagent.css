.bgcol {
	background-color: rgb(5, 10, 48);
}

.custom-caption {
	position: absolute;
	left: 250px;
	bottom: 30%;
	text-align: left;
	padding: 10px;
	border-radius: 5px;
	color: #fff;
}

.custom-caption h5 {
	margin-bottom: 10px;
}

.custom-caption p {
	width: 50%;
	line-height: 1.5;
	margin-top: 10px;
}

/* About Omni Agent */

.container.py-5 {
	color: white;
}
.main-title {
	font-size: 2.5rem;
	font-weight: bold;
}
.subtitle {
	font-size: 1.2rem;
	margin-bottom: 2rem;
}
.feature-box {
	background-color: #112254;
	border-radius: 10px;
	padding: 1.5rem;
	text-align: center;
}
.feature-box h3 {
	font-size: 1.5rem;
	font-weight: bold;
}
.feature-box p {
	font-size: 1rem;
}
.play-icon {
	width: 50px;
	height: 50px;
	background-color: white;
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-left: 1rem;
}
.play-icon::after {
	content: '';
	width: 0;
	height: 0;
	border-left: 12px solid #0a163a;
	border-top: 6px solid transparent;
	border-bottom: 6px solid transparent;
}

/* Business Users */
.main-titles {
	font-size: 2.5rem;
	font-weight: bold;
}
.subtitles {
	font-size: 1.2rem;
	margin-bottom: 2rem;
}
.feature-boxx {
	background-color: #112254;
	border-radius: 10px;
	padding: 1.5rem;
	text-align: center;
}
.feature-boxx h3 {
	font-size: 1.5rem;
	font-weight: bold;
}
.feature-boxx p {
	font-size: 1rem;
}
.highlight-texts {
	font-size: 1.5rem;
	font-weight: bold;
	color: #80b3ff;
}
