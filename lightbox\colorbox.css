#colorbox, #cboxOverlay, #cboxWrapper {

	position: absolute;

	top: 0;

	left: 0;

	z-index: 999999!important;

	overflow: hidden;

}

#cboxOverlay {

	opacity: 0.8!important;

}

#cboxOverlay {

	position: fixed;

	width: 100%;

	height: 100%;

	background: url(overlay.png) 0 0 repeat

}

#cboxMiddleLeft, #cboxBottomLeft {

	clear: left

}

#cboxContent {

	position: relative;

	overflow: hidden;

	background: #fff

}

#cboxLoadedContent {

	overflow: auto;

	margin-bottom: 28px

}

#cboxLoadedContent iframe {

	display: block;

	width: 100%;

	height: 100%;

	border: 0

}

#cboxTitle {

	position: absolute;

	bottom: 4px;

	left: 0;

	text-align: center;

	width: 100%;

	color: #000;

	margin: 0

}

#cboxLoadingOverlay, #cboxLoadingGraphic {

	position: absolute;

	top: 0;

	left: 0;

	width: 100%

}

#cboxPrevious, #cboxNext, #cboxClose, #cboxSlideshow {

	cursor: pointer

}

#cboxTopLeft {

	width: 21px;

	height: 21px;

	background: url(controls.png) -100px 0 no-repeat

}

#cboxTopRight {

	width: 21px;

	height: 21px;

	background: url(controls.png) -129px 0 no-repeat

}

#cboxBottomLeft {

	width: 21px;

	height: 21px;

	background: url(controls.png) -100px -29px no-repeat

}

#cboxBottomRight {

	width: 21px;

	height: 21px;

	background: url(controls.png) -129px -29px no-repeat

}

#cboxMiddleLeft {

	width: 21px;

	background: url(controls.png) left top repeat-y

}

#cboxMiddleRight {

	width: 21px;

	background: url(controls.png) right top repeat-y

}

#cboxTopCenter {

	height: 21px;

	background: url(border.png) 0 0 repeat-x

}

#cboxBottomCenter {

	height: 21px;

	background: url(border.png) 0 -29px repeat-x

}

#cboxCurrent {

	position: absolute;

	bottom: 4px;

	left: 58px;

	color: #000

}

#cboxSlideshow {

	position: absolute;

	bottom: 4px;

	right: 30px;

	color: #0092ef

}

#cboxPrevious {

	position: absolute;

	bottom: 0;

	left: 0;

	background: url(controls.png) -75px 0 no-repeat;

	width: 25px;

	height: 25px;

	text-indent: -9999px

}

#cboxPrevious.hover {

	background-position: -75px -25px

}

#cboxNext {

	position: absolute;

	bottom: 0;

	left: 27px;

	background: url(controls.png) -50px 0 no-repeat;

	width: 25px;

	height: 25px;

	text-indent: -9999px

}

#cboxNext.hover {

	background-position: -50px -25px

}

#cboxLoadingOverlay {

	background: url(/lightbox/loading_background.png) center center no-repeat

}

#cboxLoadingGraphic {

	background: url(/lightbox/loading.gif) center center no-repeat

}

#cboxClose {

	position: absolute;

	bottom: 0;

	right: 0;

	background: url(controls.png) -25px 0 no-repeat;

	width: 25px;

	height: 25px;

	text-indent: -9999px

}

#cboxClose.hover {

	background-position: -25px -25px

}

.cboxIE #cboxTopLeft {

	background: transparent;

filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src=/lightbox/internet_explorer/borderTopLeft.png, sizingMethod='scale')

}

.cboxIE #cboxTopCenter {

	background: transparent;

filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src=/lightbox/internet_explorer/borderTopCenter.png, sizingMethod='scale')

}

.cboxIE #cboxTopRight {

	background: transparent;

filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src=/lightbox/internet_explorer/borderTopRight.png, sizingMethod='scale')

}

.cboxIE #cboxBottomLeft {

	background: transparent;

filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src=/lightbox/internet_explorer/borderBottomLeft.png, sizingMethod='scale')

}

.cboxIE #cboxBottomCenter {

	background: transparent;

filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src=/lightbox/internet_explorer/borderBottomCenter.png, sizingMethod='scale')

}

.cboxIE #cboxBottomRight {

	background: transparent;

filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src=/lightbox/internet_explorer/borderBottomRight.png, sizingMethod='scale')

}

.cboxIE #cboxMiddleLeft {

	background: transparent;

filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src=/lightbox/internet_explorer/borderMiddleLeft.png, sizingMethod='scale')

}

.cboxIE #cboxMiddleRight {

	background: transparent;

filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src=/lightbox/internet_explorer/borderMiddleRight.png, sizingMethod='scale')

}

a {

	outline: none

}

