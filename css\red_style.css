@charset "utf-8";



html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, font, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, caption, input[type="radio"], input[type="checkbox"] {



	margin: 0;



	padding: 0;



	border: 0;



	outline: 0;



	font-size: 100%;



	background: transparent;



	resize: none



}



body {



	line-height: 1



}



html, body {



	height: 100%



}



a, a:hover {



	text-decoration: none;



	outline: none



}



a:active, a:focus {



	outline: 0



}



img {



	border: none;



	vertical-align: -2px;



}



textarea {



	resize: none



}



a {



	color: #fff !important;



}



.fl {



	float: left



}



.fr {



	float: right



}



.ovfl-hidden {



	overflow: hidden



}



ul.reset {



	list-style: none



}



.clr {



	clear: both;



	overflow: hidden;



	height: 1px



}



@font-face {



	font-family: LocalArialNarrow;



	src: local("Arial Narrow");



}



html, body {



	width: 100%;



	height: 100%



}



body {



	font: normal 13px Arial, Helvetica, sans-serif;



	color: #676767



}



#main_home_out {



}



#wrapperarea {



	width: 100%;



	height:100%;



	margin:0;



	padding:0;



}



#wrapper_home {

	width: 100%;

	position: absolute;

	z-index: 1000;

	left: -3px;

	right: 0;

	top: 0;



}



#maincont {



	width: 100%



}



.bg_img {



	top: 0;



	bottom: 0



}



.bg_img img {



	width: 100%;



	height: 100%



}



#wrapper_home #header {



}



#wrapper_home #header .header_lft {



	float: left;



	width: 202px;

	

	margin-top: -119px !important;



}



#wrapper_home #header .header_lft .header_lft_top {



	background: url(../images/nav_list_bg2.png) no-repeat 0 0;



	overflow: hidden;



	padding: 0 7px 0 1px;



	height: 222px;



}



#wrapper_home #header .header_lft h1#logo {



	margin: 11px 0 13px;



	text-align: center



}



#wrapper_home #header .header_lft div#logo {



	margin: 11px 0 13px;



	text-align: center



}



#wrapper_home #header .header_lft div#logo a {



	display: block;



	width: 160px;



	height: 72px;



	margin: 0 auto



}



#wrapper_home #header .header_lft h1#logo a {



	display: block;



	width: 160px;



	height: 72px;



	margin: 0 auto



}



#wrapper_home #header .header_rght {



	float: right;



	width: auto;



	height: auto;



	/* padding: 0px 10px 0px 10px; */



	-webkit-border-bottom-right-radius: 6px;



	-webkit-border-bottom-left-radius: 6px;



	-moz-border-radius-bottomright: 6px;



	-moz-border-radius-bottomleft: 6px;



	border-bottom-right-radius: 6px;



	border-bottom-left-radius: 6px;



	background-color:rgba(30,147,215,0.8);



	position:relative;



	z-index:9999;

	

	margin-top: -119px !important;



}



#wrapper_home #header .header_rght a {



	float: left;



	font-size: 14px;



	color: #fff;



	font-family: Arial, Helvetica, sans-serif;



	padding: 14px 20px 14px 15px;



	margin: 0;



	border-right: none!important;



	border-radius:none;



}



#wrapper_home #header .header_rght a.media {



	border-radius: 0 0 0 8px



}



#wrapper_home #header .header_rght a span {



	padding-right:13px;



	display: block;



	border-right: 1px solid #fff;



}



#wrapper_home #header .header_rght a span.line {



	padding-right:13px;



	display: block;



	border-right:none;



}



#wrapper_home #header .header_rght a:hover, #wrapper_home #header .header_rght a.active {



	float: left;



	font-size: 14px;



	color: #fff;



	background: #0c79b8;



	font-family: Arial, Helvetica, sans-serif;



	margin: 0;



	border-right: none!important;



	padding: 14px 20px 14px 15px;



}



#wrapper_home #header .header_rght a:hover span, #wrapper_home #header .header_rght a.active span {



	border-right: none!important



}



#wrapper_home #header .header_dropdown {



	float: left;



	height: 55px;



	margin-left: -10px;



	margin-top: 2px!important;



	padding: 0;



	position: relative;



	width: 160px;



	z-index: 100;



}



#wrapper_home #header .header_dropdown:hover #country_dropdown {



	display: block!important;



	z-index: 9999;



}



#wrapper_home #header .header_dropdown:hover {



	background-color: #0c79b8!important;



	height:34px!important;



	width: 175px!important;



	/*-webkit-border-radius: 5px 5px 0px 0px;



	-moz-border-radius: 5px 5px 0px 0px;



	border-radius: 5px 5px 0px 0px;*/



	margin-top: 2px!important;



	left: 18px;



	border-radius:0px!important;



}



#wrapper_home #header .header_dropdown:hover span#countryTab {



	color: #fff!important;



	font-size: 13px!important;



	font-family: Arial, Helvetica, sans-serif!important;



	width: 135px!important;



	background: none!important;



	left: 0!important;



	top: 0px!important



}



#wrapper_home #header div.header_dropdown span#countryTab {



	background: none;



	cursor: pointer;



	font-size: 13px;



	color: #fff;



	font-family: Arial, Helvetica, sans-serif;



	height: 30px;



	overflow: hidden;



	padding: 2px 20px 6px 18px;



	position: absolute;



	left: 18px;



	width: 135px;



	z-index: 5;



	top: 0



}



#wrapper_home #header div.header_dropdown span#countryTab:hover {



	background: #0c79b8



}



#wrapper_home #header div.header_dropdown span#countryTab:hover img {



	margin-top: 4px!important



}



#wrapper_home #header div.header_dropdown span#countryTab:hover strong {



	margin-top: 7px!important



}



#wrapper_home #header div.header_dropdown span#countryTab strong {



	font-weight: normal;



	float: left;



	display: block;



	margin-top: 7px!important



}



#wrapper_home #header div.header_dropdown span#countryTab img {



	float: left;



	margin: 4px 0 0!important;



}



#wrapper_home #header .header_rght p {



	float: right;



	width: auto!important;



	margin-top: 7px



}



#wrapper_home #header .header_rght p input.input_search {



	width: 125px;



	float: left;



	border: none;



	height: 17px;



	background-color: #dedede



}



#wrapper_home #header .header_rght p input.sub_btn {



	background: url(../images/sub_btn_bg.gif) no-repeat 0 0;



	width: 18px;



	height: 19px;



	float: left;



	border: none;



	display: block;



	cursor: pointer



}



#wrapper_home #header .header_rght p.input_box {



	/*position: absolute;



	right: 15px;*/



	margin-top:12px;



	float:left;



}



ul.nav_list li {



	border-bottom: 1px solid #2683b8!important;



}



ul.nav_list li:last-child {



	border-bottom:none!important;



}



ul.nav_list li a {



	font-size: 13px;



	color: #f6f7f9;



	font-weight: bold;



	display: block;



	font-family: Arial, Helvetica, sans-serif;



	text-transform: uppercase;



	padding:11px 5px 11px 25px;



}



ul.nav_list li:hover {



	background-color: #096ea8



}



ul.nav_list li:hover .sub_menus {



	display: block



}



ul.nav_list li:hover .sub_menus_out {

	display: block

}



ul.nav_list li:hover a {



	color: #fefefe



}



ul.nav_list li:hover a span {



	background: url(../images/menu_right_arw.png) no-repeat scroll 100% center;



	display: block;



	width: 164px



}



.sub_menus_out {

	left: 185px;

	position: absolute;

	z-index: 10000;

	display: none;

	padding: 0 0 0 15px;	

	margin-top: -115px !important;

}



.sub_menus {



	background-color: #1e93d7;



	border-radius: 6px 6px 6px 6px;



	margin: 0 auto;



	overflow: hidden;



	padding: 20px 20px 10px;



	box-shadow: 2px 1px 5px #353535;



	opacity: 1;



}



.sub_menus .sub_menusinn {



	float: left;



	margin-right: 12px



}



.sub_menus h2 {



	font-size: 18px;



	color: #fff;



	margin-bottom: 18px;



	font-weight: normal;



	border-bottom: 1px dashed #fff;



	padding-bottom: 12px;



	font-weight:700;



}



.sub_menus .sub_menusinn1 {



	float: left



}



.sub_menus .sub_menusinn1 h3 {



	font-size: 16px;



	color: #fff;



	font-family: Helvetica, Arial, sans-serif;



	font-style: italic;



	margin-bottom: 12px;



	margin-top: 0



}



.sub_menus .sub_menusinn1 .sub_menu_desc {



	overflow: hidden



}



.sub_menus .sub_menusinn1 .sub_menu_desc .sub_menu_img {



	float: left;



	width: 92px;



	margin-right: 20px



}



.sub_menus .sub_menusinn1 .sub_menu_desc .sub_menu_descinn {



	float: left;



	width: 165px



}



.sub_menus .sub_menusinn1 .sub_menu_desc .sub_menu_descinn p {



	font-size: 14px;



	color: #fff;



	line-height: 18px;



	text-align:left;



}



.sub_menus .sub_menusinn1 .sub_menu_desc .sub_menu_descinn a {



	font-size: 14px;



	color: #fff;



	text-decoration: none;



	background: none;



	text-transform: none;



	font-weight: normal;



	padding: 0;



	display: inherit



}



ul.sub_menu_list {



	padding-right: 10px;



	border-right: 1px solid #fff;



}



ul.sub_menu_list li {



	margin-bottom:0px;



	border: none;



	background: none



}



ul.sub_menu_list li a {



	font-size: 14px;



	color: #fff;



	text-decoration: none;



	background: url(../images/bullet-white.png) no-repeat 0 12px;



	padding-left: 15px;



	font-weight: normal;



	padding: 8px 8px 8px 12px;



	text-transform: none



}



ul.sub_menu_list li:hover a {



	background: #096ea8 url(../images/bullet-white.png) no-repeat 0 12px;



}



ul.sub_menu_list li span {



	display: block;



	margin-top: 8px;



	margin-left: 0;



	font-size: 14px;



	color: #fff;



	text-decoration: none;



	padding: 5px 0 5px 10px;



	cursor: pointer



}



ul.sub_menu_list li span a {



	text-decoration: none;



	background: none!important;



	padding: 0!important;



	display: inline



}



ul.sub_menu_list li span a:hover {



	text-decoration: none;



	background: none!important;



	padding-left: 0!important;



}



ul.sub_menu_list li:hover span {



	background: #a22d23



}



ul.sub_menu_list li:hover span a {



	background: none!important



}



#main_btm {

    z-index: 100;

    margin-top: 0px;

    width: 100%;

    margin-left: auto;

    margin-right: auto;

}



#main_btm_inner {



	overflow: hidden



}



.container2 {

	margin-top: 0px;

	width: 80%;

	margin-left: auto;

	margin-right: auto;



}





/* ============================================================================== */

.home_tabs2 {

	/*width: 770px;

	position: relative;

	bottom:100px;

	float:left;*/

	width:884px;

	position: relative;

	bottom:100px;

	margin: 0px;

}





/* ============================================================================== */



.home_tabs {

    position: relative;

    bottom: 122px;

    left: -4px;

}







.home_tabs .home_tabs_btns {

	float: left;

	margin-right: 20px;

	padding-top: 10px;

}





.home_tabs a.home_tabsinn {

	background-color:#e01f27;

	-webkit-border-radius: 6px;

	-moz-border-radius: 6px;

	border-radius: 6px;

	width: 215px;

	height: 38px;

	display: block;

	text-align: center;

	font-size: 15px;

	color: #fff;

	text-transform: uppercase;

	font-family: LocalArialNarrow, arial narrow, sans-serif;

	font-stretch: condensed;

	padding: 14px 0 0 0;

	font-weight: bold;

}



.home_tabs a.home_tabsinn span {

	display: block;

	width: 200px;

}



.home_tabs a.home_tabsinn:hover {

	background: url(../images/btn-2.png) no-repeat 0 0;

	color: #132948;

}



.home_tabs a.home_tabsinn_short {

	background-color:#2393dc;

	-webkit-border-radius: 6px;

	-moz-border-radius: 6px;

	border-radius: 6px;

	width: 230px;

	height: 28px;

	display: block;

	text-align: center;

	font-size:16px;

	color: #fff;

	text-transform: uppercase;

	font-family: Arial, Helvetica, sans-serif;

	font-stretch: condensed;

	padding:15px 5px;

	font-weight: bold;

}



.home_tabs a.home_tabsinn_short1 {

			background-color:#2393dc;	

		-webkit-border-radius: 6px;	

		-moz-border-radius: 6px;	

		border-radius: 6px;	

		width: 230px;	

		height: 28px;	

		display: block;	

		text-align: center;	

		font-size:16px;	

		color: #fff;	

		text-transform: uppercase;	

		font-family: Arial, Helvetica, sans-serif;	

		font-stretch: condensed;	

		padding:15px 5px;	

		font-weight: bold;	

	}

	



	

.home_tabs a.home_tabsinn_short:hover {

	color: #fff;

}



.home_tabs a.home_tabsinn_short span {

	display: block;

	border: 0px solid red;

}



.for_hover:hover #pup {



	display: block!important



}



.for_hover1:hover #pup1 {



	display: block!important



}

.for_hover_si:hover #pup1-3 {

	

		display: block!important

	

	}



.for_hover2:hover #pup2 {



	display: block!important



}



.for_hover3:hover #pup3 {



	display: block!important



}



.for_hover4:hover #pup4 {



	display: block!important



}



.home_tabs .pup_out {

	padding-bottom: 3px

}



.pop_up {



	width:510px;



	background-color: #2683b8;



	-webkit-border-radius:6px;



	-moz-border-radius: 6px;



	border-radius:6px;



	overflow: hidden;



	display: none;



	float:left;



	box-shadow: 1px 1px 3px #404040;



}



.pop_up .left {



	width: 240px;



	float:left;



	margin-right:30px;



}



.pop_up .right {



	width: 240px;



	float:left;



}



#pup3 {



	width: 200px!important;



}



.pop_up h2 {



	font-size: 14px;



	color: #404040;



	font-family: Helvetica, Arial, sans-serif;



	margin-bottom: 20px;



	text-transform: uppercase;



	padding: 10px;



	-webkit-border-radius: 10px 10px 0px 0px;



	-moz-border-radius: 10px 10px 0px 0px;



	border-radius: 10px 10px 0px 0px;



	background: #fff url(../images/popup_main_head_bg.png) no-repeat 10px center;



	padding-left: 38px;



	margin-left: 0



}



.pop_up h3 {

	border-bottom: 1px dotted #d1d1d1;

	color: #ffffff;

	cursor: pointer;

	font-family: Helvetica, Arial, sans-serif;

	font-size: 14px;

	font-weight: normal;

	padding: 7px 0 7px 10px;

	background-color: #2683b8!important;

}



.pop_up h3:hover {



	background-color: #1e93d7!important;



	color:#fff;



}



.pop_up h4 {



	font-size: 14px;



	color: #ffffff;



	font-family: Helvetica, Arial, sans-serif;



	padding-bottom: 7px;



	padding-top:7px;



	border-top: 1px solid #d1d1d1;



	padding-left: 11px;



	font-weight: normal;



	padding-right: 12px;



	cursor: pointer



}



.pop_up h4:hover {

	background-color: #ffffff!important;

	color:#218ecd;

}



ul.popup_list {



	margin-left: 0



}



ul.popup_list li {



	margin-bottom: 5px;



	padding: 2px 0 2px 20px



}



ul.popup_list li:hover {



	background-color: #e01f27!important



}



ul.popup_list li a {



	font-size: 11px;



	font-family: Arial, Helvetica, sans-serif;



	color: #fff;



	background: url(../images/square_bullet_bg.png) no-repeat 0 4px;



	padding-left: 12px;



	display: block;



	margin-left: 5px



}



#footer {



	position: relative



}



#footer .footer_top {



	background: #666666;



	padding-bottom: 15px;



	width: 100%;



	float: left



}



#footer .footer_top .footer_topinn {



	width: 980px;



	margin: 0 auto;



	overflow: hidden;



	padding-top: 16px;



}



#footer .footer_top .footer_topinn .footer_grid {



	float: left;



	width: 160px;



	margin-left: 16px;



}



#footer .footer_top .footer_topinn .footer_grid h3 a {



	font-size: 20px;



	color: #fff;



	font-family: LocalArialNarrow, arial narrow, sans-serif;



	font-stretch: condensed;



	font-weight: normal;



	margin-bottom: 25px;



	text-decoration: none;



	display: block



}



#footer .footer_top .footer_topinn .footer_grid h3 a:hover {



	color: #F57B20



}



#footer .footer_top .footer_topinn .footer_grid a.more_new {



	color: #fff;



	font-size: 11px



}



#footer .footer_top .footer_topinn .footer_grid a.more_new:hover {



	color: #ee2e24



}



#footer .footer_top .footer_topinn .footer_grid2 {



	float: left;



	width: 260px;



	margin-left: 10px;



}



#footer .footer_top .footer_topinn .footer_grid2 h3 {



	font-size: 20px;



	color: #fff;



	font-family: arial narrow;



	font-weight: normal;



	margin-bottom: 25px;



	text-decoration: none;



	display: block



}



#footer .footer_top .footer_topinn .footer_grid2 .videobg {



	float: left;



	margin: 10px 0 0



}



#footer .footer_top .footer_topinn .footer_grid2 .videobg .video {



	border: 2px solid #CCCCCC;



}



#footer .footer_top .footer_topinn .follow_us {



	float: right;



	padding-left: 15px;



	border-left: 1px dashed #283951



}



#footer .footer_top .footer_topinn .follow_us h3 {



	font-size: 20px;



	color: #fff;



	font-family: arial narrow;



	font-weight: normal;



	margin-bottom: 25px



}



#footer .footer_top .footer_topinn .follow_us .follow_usinn {



	overflow: hidden



}



#footer .footer_top .footer_topinn .follow_us .follow_usinn a {



	font-size: 14px;



	color: #eaeaea;



	float: left;



	display: block;



	text-decoration: none;



	margin-left: 6px;



	padding-top: 20px;



	border-right: 1px solid #243650



}



#footer .footer_top .footer_topinn .follow_us .follow_usinn a.facebook {



	background: url(../images/facebook_img1.png) no-repeat 12px 0;



	width: 50px;



	padding-right: 10px;



	margin-right: 5px



}



#footer .footer_top .footer_topinn .follow_us .follow_usinn a.twitter {



	background: url(../images/twitter_img1.png) no-repeat 0 0;



	width: 30px;



	padding-right: 18px;



	margin-right: 14px



}



#footer .footer_top .footer_topinn .follow_us .follow_usinn a.blog {



	background: url(../images/blog_img1.png) no-repeat 0 0;



	width: 36px;



	margin-left: 0;



	margin-right: 10px;



	padding-right: 10px



}



#footer .footer_top .footer_topinn .follow_us .follow_usinn a.you_tube {



	background: url(../images/you_tube_img1.png) no-repeat center 0;



	padding-right: 10px;



	margin-right: 14px;



	border-right: none!important;



	margin-left: 0!important;



	margin-right: 0;



	padding-right: 0;



	width: 35px



}



#footer .footer_top .footer_topinn .follow_us .follow_usinn a.linkedin {



	background: url(../images/linkedin_home_icon.png) no-repeat 12px 0;



	width: 50px;



	padding-right: 10px;



	margin-right: 5px



}



#footer .footer_top .footer_topinn .follow_us .follow_usinn a.scribd {



	background: url(../images/scribd_icon.png) no-repeat 4px 0;



	width: 39px;



	padding-right: 9px;



	margin-right: 5px



}



#footer .footer_top .footer_topinn .follow_us .follow_usinn a.slideshare {



	background: url(../images/slideshare_icon.png) no-repeat 4px 0;



	width: 40px;



	padding-right: 9px;



	margin-right: 5px



}



#footer .footer_top .footer_topinn .follow_us .follow_usinn a.pinterest {



	background: url(../images/pinterest_icon.png) no-repeat 0 0;



	width: 48px;



	padding-right: 0;



	margin-right: 0;



	border-right: none!important



}



#footer .footer_top .footer_topinn .follow_us .follow_usinn a.facebook:hover {



	background: url(../images/facebook_img1.png) no-repeat 12px 0



}



#footer .footer_top .footer_topinn .follow_us .follow_usinn a.twitter:hover {



	background: url(../images/twitter_img1.png) no-repeat 0 0



}



#footer .footer_top .footer_topinn .follow_us .follow_usinn a.blog:hover {



	background: url(../images/blog_img1.png) no-repeat 0 0



}



#footer .footer_top .footer_topinn .follow_us .follow_usinn a.you_tube:hover {



	background: url(../images/you_tube_img1.png) no-repeat center 0



}



ul.footer_list {



	height: auto;



	overflow: hidden



}



ul.footer_list li {



	margin-bottom: 2px



}



ul.footer_list li a {



	font-size: 13px;



	color: #fefefe;



	font-family: Helvetica, sans-serif, Arial;



	background: url(../images/square_bullet_bg.png) no-repeat 0 center;



	padding-left: 12px;



}



ul.footer_list li a:hover {



	color: #F57B20;



	background: url(../images/square_bullet_org.png) no-repeat 0 center;



}



#footer .footer_btm {



	background: #666666



}



#main_btm .footer_btm_bg {



	background: #1e93d7;



	width: 100%;



	padding:7px 0;



	float: left;



	height: 25px;



	margin-top:-52px;



}



#main_btm_inner .footer_btm_bg {



	background: #cc3333;



	width: 100%;



	padding-top: 5px;



	float: left



}



#footer .footer_btm_top {



	width: 1024px;



	margin: 0 auto;



	height:30px



}



#footer .footer_btminn {



	width: 1024px;



	margin: 0 auto;



	padding: 12px 0;



	color: #fefefe;



	font-size: 11px



}



#footer .footer_btminn a {



	float: left;



	font-size: 14px;



	color: #ccc;



	font-weight: bold;



	display: block



}



#footer .footer_btminn p {



	font-size: 14px;



	color: #fefefe;



	font-family: Helvetica, Arial, sans-serif;



	float: right



}



#footer .footer_btminn p img {



}



#footer .footer_btm_top .copy {



	color: #FFFFFF;



	font-size: 11px;



	float: left;



	line-height: 18px;



	width: 643px;



	margin-top: 5px



}



#footer .footer_btm_top .copy #twitter {



	color: #FFFFFF;



	font-size: 11px;



	float: left;



	line-height: 18px;



	width: 643px



}



.slide-up-footer-arrow a {



	display: block;



	margin: 0 0 20px 0;



	width: 64px;



	height: 50px;



	overflow: hidden;



}



.slide-up-footer-arrow h5 {



	font: italic 18px/65px Georgia, Serif;



	-webkit-transition: all 0.2s linear;



	-moz-transition: all 0.2s linear;



	-o-transition: all 0.2s linear;



}



.slide-up-footer-arrow h5 img {



	margin-bottom: 10px;



}



.slide-up-footer-arrow a:hover h5 {



	margin-top: -65px;



	opacity: 0;



}



.slide-up-footer-arrow div {



	position: relative;



	color: black;



	font: 14px/15px Georgia, Serif;



	height: 40px;



	opacity: 0;



	-webkit-transform: rotate(6deg);



	-webkit-transition: all 0.4s linear;



	-moz-transform: rotate(6deg);



	-moz-transition: all 0.4s linear;



	-o-transform: rotate(6deg);



	-o-transition: all 0.4s linear;



}



.slide-up-footer-arrow a:hover div {



	opacity: 1;



	-webkit-transform: rotate(0);



	-moz-transform: rotate(0);



	-o-transform: rotate(0);



}



#footer a.for_arrow {



	background: url(../images/arrow_sprite.png) no-repeat 0 -36px;



	display: block;



	width: 78px;



	height: 34px;



	text-decoration: none;



	position: absolute;



	bottom: 0;



	margin: 0 auto;



	right: 0;



	left: 0



}



#footer a.for_arrow:hover {



	background: url(../images/arrow_sprite.png) no-repeat 0 0



}



#footer a.hide_popup {



	background: url(../images/over-down-footer-img.png) no-repeat 0 0;



	display: block;



	width: 78px;



	height: 35px;



	text-decoration: none;



	position: absolute;



	bottom: 0;



	margin: 0 auto;



	right: 0;



	left: 0



}



#footer a.hide_popup:hover {



	background: url(../images/d-arrow.png) no-repeat 0 0;



	display: block;



	width: 78px;



	height: 35px;



	text-decoration: none;



	position: absolute;



	bottom: 0;



	margin: 0 auto;



	right: 0;



	left: 0



}



#pup {



	left: 11px!important;

	position: absolute;

	bottom: 88px!important;

	z-index: 1000;

	width: 216px;

}



#pup1 {



	/* left: 232px!important; */

	position: absolute;

	bottom: 0!important;

	z-index: 1000;

	width: 520px;

}



#pup1-3 {	

		/* left: 489px!important; */	

		position: absolute;	

		bottom: 0!important;	

		z-index: 1000;	

		width: 335px;	

	}



#pup2 {

	left: 0px!important;

	position: absolute;

	bottom: 0!important;

	z-index: 1000;

	width:520px;

}



#pup3 {

	left: 540px!important;

	position: absolute;

	bottom: 0!important;

	z-index: 1000;

	width: 243px;

}



#pup4 {

	left: 538px!important;

	position: absolute;

	bottom: 0!important;

	z-index: 1000;

	width: 223px;

}



ul.sub_list {



	background-color: #fefefe



}



ul.nav_list1 {



	width: 194px;



	margin-left: 1px



}



ul.nav_list1 li {



	border-top: 1px dashed #AF2E24



}



ul.nav_list1 li a {



	font-size: 13px;



	color: #f6f7f9;



	font-weight: bold;



	display: block;



	padding-left: 28px;



	font-family: Arial, Helvetica, sans-serif;



	text-transform: uppercase;



	padding-bottom: 10px;



	padding-top: 10px



}



ul.nav_list1 li:hover {



}



ul.nav_list1 li:hover .sub_menus {



	display: block



}



ul.nav_list1 li.active a, ul.nav_list1 li:hover a {



	color: #fefefe;



	background: #e01f27;



}



ul.nav_list1 li ul.sub_list {



	background-color: white;



}



ul.nav_list1 li ul.sub_list li {



}



ul.nav_list1 li ul.sub_list li a {



	color: #4b4b4b;



	background: #fefefe url(../images/square_bullet_bg1.png) no-repeat 16px center;



	font-size: 13px;



	text-transform: none;



	font-family: Arial, Helvetica, sans-serif;



	font-weight: normal;



	padding-left: 28px;



	margin-left: 12px



}



ul.nav_list1 li ul.sub_list li a.active, ul.nav_list1 li ul.sub_list li a:hover {



	color: #F57B20



}



ul.nav_list1 li ul.sub_list li ul.sub_list1 li {



}



ul.nav_list1 li ul.sub_list li ul.sub_list1 li a {



	font-size: 14px;



	color: #797979;



	background: #fff!important



}



ul.nav_list1 li ul.sub_list li ul.sub_list1 li a:hover {



	color: #F57B20



}



ul.nav_list1 li ul.sub_list li ul.sub_list1 li a.active {



	color: #F57B20



}



ul.nav_list1 li ul.sub_list li ul.sub_list1 li a span {



	display: block;



	width: 146px;



	margin-left: 7px;



	margin-top: -13px;



}



ul.growth_list {



	margin-top: 10px;



	margin-left: 10px



}



ul.growth_list li {



	margin-bottom: 5px



}

