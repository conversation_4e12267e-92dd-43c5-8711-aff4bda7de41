/*! Copyright Pingdom AB 2015. All rights reserved. */

(function(){var heroImg;heroImg=new Image,heroImg.onload=function(){return $("#hero-container").addClass("hero-anim"),heroImg=null},heroImg.src="/_img/sprite-hero.png",$(document).ready(function(){var callback,clearAnimationFrame,customersButton,customersButtonText1,customersButtonText2,customersLogos,menuSignupEl,requestAnimationFrame,signupScrollLimit;return customersLogos=$("#customers-logos"),customersButton=$("#customers-button"),customersButtonText1=customersButton.text(),customersButtonText2=customersButton.attr("data-alt-text"),customersButton.on("click",function(e){return e.preventDefault(),customersButton.text()===customersButtonText1?(customersButton.text(customersButtonText2),customersLogos.css("height","3.5em"),Pingdom.utils.isMobile?customersLogos.css("height","auto"):$(".container-customers").css("height","30em")):(customersButton.text(customersButtonText1),customersLogos.css("height","1.8em"),Pingdom.utils.isMobile?window.scrollTo(0,customersLogos.offset().top-100):$(".container-customers").css("height","17em"))}),$("#hero-container").on("webkitAnimationEnd animationend","div",function(e){return $(e.delegateTarget).addClass("hero-hoverable"),$("#hero-container").off("webkitAnimationEnd animationend")}),Pingdom.gauge.init(),Pingdom.utils.isMobile&&$("#gauge").gauge("value",2.7),requestAnimationFrame=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||window.msRequestAnimationFrame||function(callback){return setTimeout(callback,16)},clearAnimationFrame=window.clearAnimationFrame||window.mozClearAnimationFrame||function(frame){return clearTimeout(frame)},menuSignupEl=$("#menu-signup"),signupScrollLimit=$(".button-introduction").offset().top,Pingdom.utils.isMobile?void 0:($(window).resize(function(){return requestAnimationFrame(function(){return Pingdom.gauge.rebase(),Pingdom.gauge.recalc()})}),callback=null,$(window).scroll(function(){return clearAnimationFrame(callback),callback=requestAnimationFrame(function(){return window.pageYOffset>signupScrollLimit?menuSignupEl.addClass("menu-signup"):menuSignupEl.removeClass("menu-signup"),Pingdom.parallax.recalc(),Pingdom.gauge.recalc()})}))}),$(window).load(function(){return Pingdom.slideshow.init(["report","multiple","public","email","root","multiuser","apps"]),Pingdom.utils.isMobile?void 0:Pingdom.map.init({scale:725,translate:[360,333],animate:!Pingdom.utils.isMobile,connect:function(){return $(".container-map").slideDown()}})}),$(window).resize(function(){return Pingdom.parallax.init()})}).call(this);