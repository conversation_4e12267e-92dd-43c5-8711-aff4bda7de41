/* -------------------------------------------

Name:       Josie
Version:    1.0
Author:	    bslthemes
Website:    https://bslthemes.com/
Developer:	millerDigitalDesign (https://themeforest.net/user/millerdigitaldesign/)

------------------------------------------- */
/*--------------------------------------------

1. common
    - main
    - settings
    - typography
    - social
    - slider
    - cursor
    - scrollbar
    - pagination
    - filter
    - form
    - buttons
    - spaces
2. components
    - preloader
    - top panel
    - sidebar
    - content
    - menu
    - banner
    - services
    - about
    - portfolio
    - blog
    - skills
    - experience
    - 404
3. dark mode settings

--------------------------------------------*/
/* -------------------------------------------

fonts

------------------------------------------- */
@import url("https://fonts.googleapis.com/css2?family=Kanit:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Sacramento&family=Unbounded:wght@200..900&display=swap");
/* -------------------------------------------

colors

------------------------------------------- */
/* -------------------------------------------

transition

------------------------------------------- */
/* -------------------------------------------

main

------------------------------------------- */
*,
*:before,
*:after {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding: 0;
  margin: 0;
}

body {
  font-family: "Kanit", sans-serif;
  color: #EFEFEF;
  font-weight: 300;
  font-size: 14px;
}

.mil-page-wrapper {
  position: relative;
  overflow: hidden;
}

/* -------------------------------------------

settings

------------------------------------------- */
.mil-relative {
  position: relative;
  z-index: 1;
}

.mil-df {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.mil-aic {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.mil-aie {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
}

.mil-ais {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}

.mil-jcc {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.mil-jce {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}

.mil-jcs {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}

.mil-jcb {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.mil-tac {
  text-align: center;
}

.mil-tar {
  text-align: right;
}

@media (max-width: 992px) {
  .mil-992-jcs {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
  }
  .mil-992-jcc {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
  .mil-992-tac {
    text-align: center;
  }
  .mil-992-tal {
    text-align: left;
  }
}
@media (max-width: 768px) {
  .mil-768-jcc {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
  .mil-768-tac {
    text-align: center;
  }
  .mil-768-tal {
    text-align: left;
  }
}
/* -------------------------------------------

typography

------------------------------------------- */
h1,
h2,
h3,
h4,
h5,
h6,
.mil-h1 {
  font-family: "Unbounded", sans-serif;
  color: #EFEFEF;
  font-weight: 400;
  line-height: normal;
  color: #EFEFEF;
}

.mil-fs14 {
  font-size: 14px;
}
@media (max-width: 768px) {
  .mil-fs14 {
    font-size: 12px;
  }
}

.mil-fs16 {
  font-size: 16px;
}
@media (max-width: 768px) {
  .mil-fs16 {
    font-size: 14px;
  }
}

.mil-fs18 {
  font-size: 18px;
}
@media (max-width: 768px) {
  .mil-fs18 {
    font-size: 16px;
  }
}

.mil-fs20 {
  font-size: 20px;
}
@media (max-width: 768px) {
  .mil-fs20 {
    font-size: 18px;
  }
}

.mil-fs24 {
  font-size: 24px;
}
@media (max-width: 768px) {
  .mil-fs24 {
    font-size: 22px;
  }
}

.mil-fs90 {
  font-size: calc(1rem + 3.5vw);
}

.mil-light {
  color: #090909;
}

.mil-dark {
  color: #EFEFEF;
}

.mil-soft {
  color: #949494;
}

.mil-accent {
  color: #91E01A;
}

.mil-upper {
  text-transform: uppercase;
}

.mil-shortened {
  width: 80%;
}

.mil-text-bg {
  background-color: #121212;
  padding-right: 20px;
}

.mil-text-link {
  color: #91E01A;
  position: relative;
}
.mil-text-link:before {
  content: "";
  width: 0;
  height: 1px;
  background-color: #91E01A;
  position: absolute;
  bottom: 2px;
  -webkit-transition: 0.2s cubic-bezier(0, 0, 0.3642, 1);
  transition: 0.2s cubic-bezier(0, 0, 0.3642, 1);
}
.mil-text-link:hover:before {
  width: 100%;
}

a {
  text-decoration: none;
  color: inherit;
}

.mil-suptitle {
  color: #EFEFEF;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 2px;
}

blockquote {
  padding: 30px 60px;
  border-left: solid 2px #91E01A;
}

/* -------------------------------------------

social

------------------------------------------- */
.mil-social {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: relative;
}
.mil-social li {
  list-style-type: none;
  margin-right: 30px;
}
.mil-social li a {
  text-transform: uppercase;
  font-size: 14px;
  font-weight: 400;
  color: #949494;
  -webkit-transition: 0.2s cubic-bezier(0, 0, 0.3642, 1);
  transition: 0.2s cubic-bezier(0, 0, 0.3642, 1);
}
.mil-social li a:hover {
  color: #EFEFEF;
}
.mil-social li:last-child {
  margin-right: 0;
}
@media (max-width: 992px) {
  .mil-social {
    display: none;
  }
}

/* -------------------------------------------

slider

------------------------------------------- */
.mil-swiper-pagination,
.mil-swiper-pagination-2 {
  font-size: 16px;
  padding-top: 60px;
  color: #EFEFEF;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 2px;
}

/* -------------------------------------------

cursor

------------------------------------------- */
.mil-cursor-follower {
  margin-top: -1.4%;
  margin-left: -0.9%;
  -webkit-transform: scale(1);
          transform: scale(1);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  position: absolute;
  background-color: rgba(239, 239, 239, 0.1);
  width: 30px;
  height: 30px;
  border-radius: 100%;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  pointer-events: none;
  z-index: 9999999;
  -webkit-transition: background-color 0.4s cubic-bezier(0, 0, 0.3642, 1), -webkit-transform 0.4s cubic-bezier(0.75, -1, 0.3, 2.33);
  transition: background-color 0.4s cubic-bezier(0, 0, 0.3642, 1), -webkit-transform 0.4s cubic-bezier(0.75, -1, 0.3, 2.33);
  transition: transform 0.4s cubic-bezier(0.75, -1, 0.3, 2.33), background-color 0.4s cubic-bezier(0, 0, 0.3642, 1);
  transition: transform 0.4s cubic-bezier(0.75, -1, 0.3, 2.33), background-color 0.4s cubic-bezier(0, 0, 0.3642, 1), -webkit-transform 0.4s cubic-bezier(0.75, -1, 0.3, 2.33);
}
.mil-cursor-follower:after {
  content: "iiiiii";
  min-height: 7px;
  min-width: 50px;
  text-align: center;
  border-radius: 10px;
  position: absolute;
  opacity: 0;
  -webkit-transform: scale(0);
          transform: scale(0);
  text-transform: uppercase;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 2px;
  color: #EFEFEF;
  -webkit-transition: 0.2s cubic-bezier(0, 0, 0.3642, 1);
  transition: 0.2s cubic-bezier(0, 0, 0.3642, 1);
  -webkit-transition-delay: 0s;
          transition-delay: 0s;
}
.mil-cursor-follower.mil-light-active, .mil-cursor-follower.mil-dark-active, .mil-cursor-follower.mil-gone-active, .mil-cursor-follower.mil-view-active, .mil-cursor-follower.mil-next-active, .mil-cursor-follower.mil-read-active, .mil-cursor-follower.mil-swipe-active {
  background-color: #91E01A;
  -webkit-transform: scale(3);
          transform: scale(3);
  -webkit-filter: drop-shadow(0px 2px 4px rgba(239, 239, 239, 0.1));
          filter: drop-shadow(0px 2px 4px rgba(239, 239, 239, 0.1));
}
.mil-cursor-follower.mil-light-active:after, .mil-cursor-follower.mil-dark-active:after, .mil-cursor-follower.mil-gone-active:after, .mil-cursor-follower.mil-view-active:after, .mil-cursor-follower.mil-next-active:after, .mil-cursor-follower.mil-read-active:after, .mil-cursor-follower.mil-swipe-active:after {
  -webkit-transition-delay: 0.2s;
          transition-delay: 0.2s;
  opacity: 1;
  -webkit-transform: scale(0.33);
          transform: scale(0.33);
}
.mil-cursor-follower.mil-light-active {
  background-color: #090909;
}
.mil-cursor-follower.mil-light-active:after {
  color: #EFEFEF;
}
.mil-cursor-follower.mil-dark-active {
  background-color: #EFEFEF;
}
.mil-cursor-follower.mil-dark-active:after {
  color: #090909;
  -webkit-filter: invert(80%);
          filter: invert(80%);
}
.mil-cursor-follower.mil-gone-active {
  background-color: rgba(9, 9, 9, 0.1);
  -webkit-transform: scale(0);
          transform: scale(0);
}
.mil-cursor-follower.mil-gone-active:after {
  content: ".";
}
.mil-cursor-follower.mil-next-active:after {
  content: "next";
}
.mil-cursor-follower.mil-view-active:after {
  content: "view";
}
.mil-cursor-follower.mil-read-active:after {
  content: "read";
}
.mil-cursor-follower.mil-swipe-active:after {
  content: url('data:image/svg+xml,<svg width="31" height="31" viewBox="0 0 31 31" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M29.4364 14.3667C29.4101 14.342 29.3824 14.3188 29.3535 14.2972L24.653 9.59663C24.5636 9.50467 24.4568 9.4314 24.3389 9.38106C24.2209 9.33073 24.0941 9.30434 23.9659 9.30344C23.8376 9.30253 23.7105 9.32712 23.5918 9.37578C23.4732 9.42444 23.3654 9.4962 23.2747 9.58689C23.184 9.67757 23.1122 9.78538 23.0636 9.90404C23.0149 10.0227 22.9903 10.1498 22.9912 10.2781C22.9921 10.4063 23.0185 10.5331 23.0688 10.6511C23.1192 10.7691 23.1925 10.8758 23.2844 10.9652L26.4226 14.1034L3.72315 14.1034L6.86129 10.9652C6.9541 10.8749 7.02784 10.7669 7.07815 10.6476C7.12846 10.5283 7.15432 10.4001 7.1542 10.2706C7.15408 10.1411 7.12797 10.013 7.07743 9.89378C7.02688 9.77456 6.95293 9.6667 6.85996 9.57659C6.67587 9.39817 6.42852 9.30007 6.17218 9.30383C5.91585 9.30759 5.67147 9.41289 5.4927 9.59663L0.806869 14.2825C0.68112 14.372 0.578597 14.4902 0.507853 14.6274C0.43711 14.7646 0.400197 14.9167 0.400197 15.071C0.400197 15.2253 0.437109 15.3775 0.507853 15.5146C0.578596 15.6518 0.68112 15.7701 0.806869 15.8596L5.4927 20.5454C5.58209 20.6373 5.68888 20.7106 5.80684 20.761C5.9248 20.8113 6.05158 20.8377 6.17983 20.8386C6.30808 20.8395 6.43522 20.8149 6.55389 20.7662C6.67255 20.7176 6.78035 20.6458 6.87104 20.5551C6.96173 20.4644 7.03348 20.3566 7.08214 20.238C7.1308 20.1193 7.15539 19.9922 7.15449 19.8639C7.15358 19.7357 7.12719 19.6089 7.07686 19.4909C7.02653 19.373 6.95325 19.2662 6.86129 19.1768L3.72315 16.0386L26.4226 16.0386L23.2844 19.1768C23.1925 19.2662 23.1192 19.373 23.0688 19.4909C23.0185 19.6089 22.9921 19.7357 22.9912 19.8639C22.9903 19.9922 23.0149 20.1193 23.0636 20.238C23.1122 20.3566 23.184 20.4644 23.2747 20.5551C23.3654 20.6458 23.4732 20.7176 23.5918 20.7662C23.7105 20.8149 23.8376 20.8395 23.9659 20.8386C24.0941 20.8377 24.2209 20.8113 24.3389 20.761C24.4568 20.7106 24.5636 20.6373 24.653 20.5454L29.3562 15.8422C29.4684 15.7573 29.5607 15.649 29.6267 15.5248C29.6927 15.4006 29.7309 15.2635 29.7385 15.123C29.7461 14.9826 29.7231 14.8421 29.6709 14.7115C29.6187 14.5809 29.5387 14.4632 29.4364 14.3667Z" fill="black"/></svg>');
  margin-top: 1px;
}
@media (max-width: 768px) {
  .mil-cursor-follower {
    display: none;
  }
}

.mil-c-swipe {
  cursor: -webkit-grab;
  cursor: grab;
}

/* -------------------------------------------

scrollbar

------------------------------------------- */
::-webkit-scrollbar {
  display: none;
}

.mil-progress-track {
  position: fixed;
  z-index: 999999999999;
  top: 0;
  right: 0;
  height: 100%;
  width: 5px;
  background-color: #121212;
}
.mil-progress-track .mil-progress {
  background-color: #91E01A;
  height: 0;
  width: 5px;
  border-radius: 4px;
  -webkit-transition: 0.4s cubic-bezier(0, 0, 0.3642, 1);
  transition: 0.4s cubic-bezier(0, 0, 0.3642, 1);
}
@media screen and (max-width: 768px) {
  .mil-progress-track {
    display: none;
  }
}

/* -------------------------------------------

pagination

------------------------------------------- */
.mil-pagination {
  margin-top: 60px;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.mil-pagination:before {
  content: "";
  height: 1px;
  width: 100%;
  background-color: rgba(148, 148, 148, 0.3);
  position: absolute;
  top: 50%;
}
.mil-pagination ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.mil-pagination ul li {
  margin-right: 10px;
  list-style-type: none;
}
.mil-pagination ul li:last-child {
  margin-right: 0;
}
.mil-pagination a {
  position: relative;
  background-color: #121212;
  display: block;
  border: solid 1px rgba(148, 148, 148, 0.3);
  height: 60px;
  width: 60px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-radius: 50%;
  color: rgba(148, 148, 148, 0.3);
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 2px;
  -webkit-transition: 0.4s cubic-bezier(0, 0, 0.3642, 1);
  transition: 0.4s cubic-bezier(0, 0, 0.3642, 1);
}
.mil-pagination a:hover {
  border: solid 1px #EFEFEF;
  color: #EFEFEF;
  -webkit-transform: scale(1.03);
          transform: scale(1.03);
}
.mil-pagination a.mil-active {
  border: solid 1px #91E01A;
  color: #090909;
  background-color: #91E01A;
  pointer-events: none;
}
.mil-pagination a.mil-pag-arrow {
  border: solid 1px #EFEFEF;
  color: #EFEFEF;
}
.mil-pagination a.mil-pag-arrow.mil-next {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}
.mil-pagination a.mil-pag-arrow.mil-next:hover {
  -webkit-transform: rotate(180deg) scale(1.03);
          transform: rotate(180deg) scale(1.03);
}
.mil-pagination a.mil-all {
  width: auto;
  border-radius: 60px;
  padding: 0 40px;
  border: solid 1px #EFEFEF;
  color: #EFEFEF;
}
@media (max-width: 550px) {
  .mil-pagination ul {
    display: none;
  }
  .mil-pagination a.mil-all {
    display: none;
  }
}

/* -------------------------------------------

filter

------------------------------------------- */
.mil-filter {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.mil-filter li {
  list-style-type: none;
}
.mil-filter li:after {
  content: "/";
  margin: 0 15px;
  color: rgba(148, 148, 148, 0.3);
}
.mil-filter li:last-child:after {
  display: none;
}
.mil-filter li a {
  color: #EFEFEF;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-size: 14px;
  -webkit-transition: 0.2s cubic-bezier(0, 0, 0.3642, 1);
  transition: 0.2s cubic-bezier(0, 0, 0.3642, 1);
}
.mil-filter li a:hover {
  color: #91E01A;
}
.mil-filter li a.mil-active {
  color: #91E01A;
}
@media (max-width: 550px) {
  .mil-filter li:after {
    margin: 0 10px;
  }
  .mil-filter li a {
    font-size: 12px;
  }
}

/* -------------------------------------------

form

------------------------------------------- */
form input, form textarea {
  padding: 0 15px;
  width: 100%;
  background-color: transparent;
  height: 50px;
  border: none;
  border-bottom: solid 1px #EFEFEF;
  font-size: 16px;
  font-family: "Kanit", sans-serif;
  font-weight: 300;
  color: #EFEFEF;
  -webkit-transition: 0.2s cubic-bezier(0, 0, 0.3642, 1);
  transition: 0.2s cubic-bezier(0, 0, 0.3642, 1);
}
form input::-webkit-input-placeholder, form textarea::-webkit-input-placeholder {
  color: #949494;
  opacity: 1;
  font-size: 16px;
  font-family: "Kanit", sans-serif;
  font-weight: 300;
  -webkit-transition: 0.2s cubic-bezier(0, 0, 0.3642, 1);
  transition: 0.2s cubic-bezier(0, 0, 0.3642, 1);
}
form input::-moz-placeholder, form textarea::-moz-placeholder {
  color: #949494;
  opacity: 1;
  font-size: 16px;
  font-family: "Kanit", sans-serif;
  font-weight: 300;
  -moz-transition: 0.2s cubic-bezier(0, 0, 0.3642, 1);
  transition: 0.2s cubic-bezier(0, 0, 0.3642, 1);
}
form input:-ms-input-placeholder, form textarea:-ms-input-placeholder {
  color: #949494;
  opacity: 1;
  font-size: 16px;
  font-family: "Kanit", sans-serif;
  font-weight: 300;
  -ms-transition: 0.2s cubic-bezier(0, 0, 0.3642, 1);
  transition: 0.2s cubic-bezier(0, 0, 0.3642, 1);
}
form input::-ms-input-placeholder, form textarea::-ms-input-placeholder {
  color: #949494;
  opacity: 1;
  font-size: 16px;
  font-family: "Kanit", sans-serif;
  font-weight: 300;
  -ms-transition: 0.2s cubic-bezier(0, 0, 0.3642, 1);
  transition: 0.2s cubic-bezier(0, 0, 0.3642, 1);
}
form input::placeholder, form textarea::placeholder {
  color: #949494;
  opacity: 1;
  font-size: 16px;
  font-family: "Kanit", sans-serif;
  font-weight: 300;
  -webkit-transition: 0.2s cubic-bezier(0, 0, 0.3642, 1);
  transition: 0.2s cubic-bezier(0, 0, 0.3642, 1);
}
form input:focus, form textarea:focus {
  outline: inherit;
  border-bottom: solid 1px #91E01A;
}
form input:focus::-webkit-input-placeholder, form textarea:focus::-webkit-input-placeholder {
  color: rgba(148, 148, 148, 0.3);
}
form input:focus::-moz-placeholder, form textarea:focus::-moz-placeholder {
  color: rgba(148, 148, 148, 0.3);
}
form input:focus:-ms-input-placeholder, form textarea:focus:-ms-input-placeholder {
  color: rgba(148, 148, 148, 0.3);
}
form input:focus::-ms-input-placeholder, form textarea:focus::-ms-input-placeholder {
  color: rgba(148, 148, 148, 0.3);
}
form input:focus::placeholder, form textarea:focus::placeholder {
  color: rgba(148, 148, 148, 0.3);
}
form textarea {
  height: auto;
}

/* -------------------------------------------

buttons

------------------------------------------- */
.mil-btn {
  border: none;
  cursor: pointer;
  border-radius: 60px;
  padding: 0 40px;
  height: 60px;
  font-family: "Kanit", sans-serif;
  background-color: #91E01A;
  color: #090909;
  text-decoration: none;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 16px;
  font-weight: 400;
  line-height: 100%;
  padding-bottom: 3px;
  white-space: nowrap;
  -webkit-transition: 0.4s cubic-bezier(0, 0, 0.3642, 1);
  transition: 0.4s cubic-bezier(0, 0, 0.3642, 1);
}
.mil-btn.mil-btn-lg {
  height: 90px;
  padding: 0 70px;
  font-size: 16px;
}
.mil-btn.mil-btn-border {
  border: solid 2px #EFEFEF;
  background-color: transparent;
  color: #EFEFEF;
}
.mil-btn.mil-btn-soft {
  background-color: #91E01A;
  color: #EFEFEF;
}
.mil-btn.mil-btn-link {
  background-color: transparent;
  padding: 0;
  color: #090909;
}
.mil-btn.mil-btn-link.mil-accent {
  color: #91E01A;
}
.mil-btn i {
  margin-left: 10px;
  -webkit-transition: 0.4s cubic-bezier(0, 0, 0.3642, 1);
  transition: 0.4s cubic-bezier(0, 0, 0.3642, 1);
}
.mil-btn:hover {
  -webkit-transform: scale(1.03);
          transform: scale(1.03);
  -webkit-filter: brightness(110%);
          filter: brightness(110%);
}
.mil-btn:hover i {
  margin-left: 15px;
}

/* -------------------------------------------

spaces

------------------------------------------- */
.mil-divider {
  height: 1px;
  width: 100%;
  background-color: rgba(148, 148, 148, 0.3);
}

.mil-mb10 {
  margin-bottom: 10px;
}

.mil-mb30 {
  margin-bottom: 30px;
}

.mil-mb40 {
  margin-bottom: 40px;
}

.mil-mb50 {
  margin-bottom: 50px;
}

.mil-mb60 {
  margin-bottom: 60px;
}

.mil-mb90 {
  margin-bottom: 90px;
}

.mil-p-90-90 {
  padding-top: 90px;
  padding-bottom: 90px;
}

.mil-p-0-90 {
  padding-bottom: 90px;
}

.mil-p-90-0 {
  padding-top: 90px;
}

.mil-p-90-60 {
  padding-top: 90px;
  padding-bottom: 60px;
}

.mil-p-90-30 {
  padding-top: 90px;
  padding-bottom: 30px;
}

.mil-p-0-30 {
  padding-bottom: 30px;
}

@media (max-width: 1200px) {
  .mil-1200-p-90-30 {
    padding-top: 90px;
    padding-bottom: 30px;
  }
  .mil-1200-p-30-90 {
    padding-top: 30px;
    padding-bottom: 90px;
  }
}
/* -------------------------------------------

preloader

------------------------------------------- */
.mil-preloader {
  width: 100vw;
  height: 100vh;
  background-color: #121212;
  position: fixed;
  z-index: 99999999;
  top: 0;
  right: 0;
  bottom: 0;
  padding-left: 10vw;
  padding-bottom: 90px;
  pointer-events: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
}
.mil-preloader .mil-curtain {
  background-color: #090909;
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
}
.mil-preloader .mil-load {
  background-color: #91E01A;
  height: 4px;
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
}

/* -------------------------------------------

top panel

------------------------------------------- */
.mil-top-panel {
  position: fixed;
  z-index: 9;
  top: 0;
  left: 0;
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
.mil-top-panel .mil-logo {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 90px;
  height: 90px;
  font-family: "Unbounded", sans-serif;
  font-weight: 400;
  font-size: 40px;
  background-color: #91E01A;
  color: #090909;
}
@media (max-width: 992px) {
  .mil-top-panel {
    background-color: #090909;
  }
}

.mil-menu-btn {
  margin: 28px 20px 0;
  padding: 18px 10px 20px;
  height: 24px;
  cursor: pointer;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: none;
  -webkit-transition: 0.4s cubic-bezier(0, 0, 0.3642, 1);
  transition: 0.4s cubic-bezier(0, 0, 0.3642, 1);
}
.mil-menu-btn span, .mil-menu-btn span:after, .mil-menu-btn span:before {
  content: "";
  pointer-events: none;
  display: block;
  width: 24px;
  height: 2px;
  background-color: #EFEFEF;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  -webkit-transition: inherit;
  transition: inherit;
}
.mil-menu-btn span {
  position: relative;
}
.mil-menu-btn span:after, .mil-menu-btn span:before {
  position: absolute;
}
.mil-menu-btn span:before {
  top: -8px;
}
.mil-menu-btn span:after {
  top: 8px;
}
.mil-menu-btn.mil-active span {
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
}
.mil-menu-btn.mil-active span:before {
  -webkit-transform: translate(0px, 8px) rotate(-90deg);
          transform: translate(0px, 8px) rotate(-90deg);
}
.mil-menu-btn.mil-active span:after {
  width: 24px;
  -webkit-transform: translate(0px, -8px) rotate(-90deg);
          transform: translate(0px, -8px) rotate(-90deg);
}
@media (max-width: 992px) {
  .mil-menu-btn {
    display: block;
  }
}

/* -------------------------------------------

sidebar

------------------------------------------- */
.mil-sidebar {
  background-color: #090909;
  width: 30%;
  height: 100vh;
  position: fixed;
  z-index: 999;
  top: 0;
  right: 0;
  padding-left: 5vw;
  padding-right: 10vw;
  padding-top: 60px;
  padding-bottom: 60px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-transition: 0.4s cubic-bezier(0, 0, 0.3642, 1);
  transition: 0.4s cubic-bezier(0, 0, 0.3642, 1);
}
@media (max-width: 1200px) {
  .mil-sidebar {
    padding: 30px;
  }
}
@media (max-width: 992px) {
  .mil-sidebar {
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    width: 100%;
    top: 90px;
    height: calc(100vh - 90px);
    -webkit-transform: translateX(100%);
            transform: translateX(100%);
    padding-top: 90px;
  }
  .mil-sidebar.mil-active {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  .mil-sidebar .mil-menu-frame {
    margin-bottom: 90px;
  }
  .mil-sidebar .mil-menu-frame h2 {
    display: none;
  }
}
@media (max-height: 550px) {
  .mil-sidebar {
    padding-top: 60px;
    height: auto;
    min-height: calc(100vh - 90px);
    overflow: scroll;
  }
  .mil-sidebar .mil-menu-frame {
    margin-bottom: 60px;
  }
}

/* -------------------------------------------

content

------------------------------------------- */
.mil-content-frame {
  min-height: 100vh;
  position: relative;
  width: 70%;
  background-color: #090909;
}
.mil-content-frame:before {
  content: "";
  background-color: #121212;
  width: calc(100% - 5vw);
  height: 100%;
  position: absolute;
  pointer-events: none;
}
@media (max-width: 1200px) {
  .mil-content-frame:before {
    width: 100%;
  }
}
@media (max-width: 992px) {
  .mil-content-frame {
    width: 100%;
    padding-top: 90px;
    min-height: calc(100vh - 180px);
  }
}

.mil-container {
  position: relative;
  padding-left: 10vw;
  padding-right: 15vw;
  pointer-events: all;
}
@media (max-width: 1200px) {
  .mil-container {
    padding-left: 30px;
    padding-right: 30px;
  }
}

.mil-container-out {
  position: relative;
}

.mil-container-out-right {
  position: relative;
  padding-left: 10vw;
  padding-right: 10vw;
}
@media (max-width: 1200px) {
  .mil-container-out-right {
    padding-left: 30px;
    padding-right: 30px;
  }
}

/* -------------------------------------------

menu

------------------------------------------- */
.mil-main-menu {
  position: relative;
}
.mil-main-menu li {
  list-style-type: none;
  margin-bottom: 15px;
}
.mil-main-menu li a {
  font-size: 18px;
  font-weight: 400;
  -webkit-transition: 0.4s cubic-bezier(0, 0, 0.3642, 1);
  transition: 0.4s cubic-bezier(0, 0, 0.3642, 1);
}
.mil-main-menu li a:hover {
  color: #91E01A;
  padding-left: 5px;
}
.mil-main-menu li ul {
  padding-left: 15px;
  max-height: 0;
  overflow: hidden;
  -webkit-transition: 0.4s cubic-bezier(0, 0, 0.3642, 1);
  transition: 0.4s cubic-bezier(0, 0, 0.3642, 1);
  -webkit-transition-delay: 0.2s;
          transition-delay: 0.2s;
}
.mil-main-menu li ul li {
  opacity: 0;
  margin-bottom: 10px;
  -webkit-transition: 0.4s cubic-bezier(0, 0, 0.3642, 1);
  transition: 0.4s cubic-bezier(0, 0, 0.3642, 1);
  -webkit-transition-delay: 0s;
          transition-delay: 0s;
}
.mil-main-menu li ul li:first-child {
  margin-top: 15px;
}
.mil-main-menu li ul li:last-child {
  margin-bottom: 0;
}
.mil-main-menu li ul li a {
  font-size: 16px;
  color: #EFEFEF;
  font-weight: 300;
  color: #949494;
}
.mil-main-menu li:hover {
  color: #91E01A;
}
.mil-main-menu li:hover ul {
  max-height: 150px;
  -webkit-transition-delay: 0s;
          transition-delay: 0s;
}
.mil-main-menu li:hover ul li {
  opacity: 1;
  -webkit-transition-delay: 0.2s;
          transition-delay: 0.2s;
}
@media (max-width: 992px) {
  .mil-main-menu {
    text-align: center;
  }
  .mil-main-menu li a:hover {
    padding: 0;
  }
  .mil-main-menu li ul {
    padding: 0;
  }
}

/* -------------------------------------------

banner

------------------------------------------- */
.mil-banner {
  height: 100vh;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.mil-banner .mil-banner-text {
  padding: 90px 0;
  position: relative;
  z-index: 2;
}
.mil-banner .mil-banner-text h1 {
  direction: inline;
  width: 200%;
}
.mil-banner .mil-banner-image {
  width: calc(100% + 10vw);
  height: calc(100vh - 290px);
  position: relative;
  overflow: hidden;
  z-index: 1;
  border-radius: 0 0 0 150px;
}
.mil-banner .mil-banner-image img {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}
@media (max-width: 1200px) {
  .mil-banner {
    height: auto;
    padding-bottom: 90px;
  }
  .mil-banner .mil-banner-text {
    text-align: center;
  }
  .mil-banner .mil-banner-text h1 {
    direction: inline;
    width: 100%;
  }
  .mil-banner .mil-banner-text .mil-shortened {
    margin-left: auto;
    margin-right: auto;
  }
  .mil-banner .mil-banner-image {
    width: 100%;
    height: auto;
    padding-bottom: 130%;
    border-radius: 0 0 0 100px;
  }
}

/* -------------------------------------------

services

------------------------------------------- */
.mil-service-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}
.mil-service-item .mil-icon-part {
  width: 40%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.mil-service-item .mil-icon-part img {
  width: 50px;
  margin-right: 30px;
}
.mil-service-item .mil-text-part {
  width: 60%;
}
.mil-service-item .mil-text-part ul li {
  list-style-type: none;
  margin-bottom: 5px;
}
.mil-service-item .mil-text-part ul li:before {
  content: "--";
  color: #EFEFEF;
  margin-right: 20px;
}
@media (max-width: 768px) {
  .mil-service-item {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .mil-service-item .mil-icon-part {
    width: 100%;
  }
  .mil-service-item .mil-text-part {
    width: 100%;
  }
}

/* -------------------------------------------

about

------------------------------------------- */
.mil-sign {
  font-family: "Sacramento", cursive;
  color: #91E01A;
  font-size: 100px;
  position: absolute;
  top: -80px;
  right: 15vw;
  -webkit-transform: rotate(-5deg);
          transform: rotate(-5deg);
}
@media (max-width: 768px) {
  .mil-sign {
    font-size: 60px;
    top: -40px;
  }
}

.mil-image-frame {
  position: relative;
  overflow: hidden;
  padding-bottom: 35%;
}
.mil-image-frame img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}
@media (max-width: 768px) {
  .mil-image-frame {
    padding-bottom: 50%;
  }
}

.mil-contact-info {
  border-top: solid 4px #91E01A;
  width: 100%;
  position: absolute;
  bottom: 0;
  background-color: #090909;
  padding: 60px;
}
.mil-contact-info li {
  list-style-type: none;
  margin-bottom: 30px;
}
.mil-contact-info li:last-child {
  margin-bottom: 0;
}
@media (max-width: 1200px) {
  .mil-contact-info {
    padding: 30px;
    position: static;
  }
}

/* -------------------------------------------

portfolio

------------------------------------------- */
.mil-portfolio-item {
  display: block;
  position: relative;
  overflow: hidden;
  padding-bottom: 35%;
}
.mil-portfolio-item img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}
.mil-portfolio-item .mil-overlay {
  -webkit-transform: rotate(-90deg);
          transform: rotate(-90deg);
  position: absolute;
  top: 40%;
  left: calc(50% + 2.5vw);
  background-color: #91E01A;
  height: 5vw;
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-transition: 0.4s cubic-bezier(0, 0, 0.3642, 1);
  transition: 0.4s cubic-bezier(0, 0, 0.3642, 1);
  color: #090909;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 2px;
}
.mil-portfolio-item.mil-long {
  padding-bottom: 45%;
}
.mil-portfolio-item.mil-square {
  padding-bottom: 100%;
}
.mil-portfolio-item:hover .mil-overlay {
  left: calc(50% - 2.5vw);
}
@media (max-width: 550px) {
  .mil-portfolio-item .mil-overlay {
    top: 30%;
    height: 90px;
    left: calc(50% + 45px);
  }
}
@media (max-width: 1200px) {
  .mil-portfolio-item {
    padding-bottom: 60%;
  }
  .mil-portfolio-item.mil-long {
    padding-bottom: 60%;
  }
  .mil-portfolio-item.mil-square {
    padding-bottom: 100%;
  }
  .mil-portfolio-item .mil-overlay {
    height: 90px;
    left: calc(50% + 45px);
  }
  .mil-portfolio-item:hover .mil-overlay {
    left: calc(50% - 45px);
  }
}

/* -------------------------------------------

blog

------------------------------------------- */
.mil-blog-item .mil-pub-cover {
  display: block;
  position: relative;
  overflow: hidden;
  padding-bottom: 60%;
  margin-bottom: 35px;
}
.mil-blog-item .mil-pub-cover img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}
.mil-blog-item .mil-pub-cover .mil-overlay {
  -webkit-transform: rotate(-90deg);
          transform: rotate(-90deg);
  position: absolute;
  top: 40%;
  left: calc(50% + 2.5vw);
  background-color: #91E01A;
  height: 5vw;
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-transition: 0.4s cubic-bezier(0, 0, 0.3642, 1);
  transition: 0.4s cubic-bezier(0, 0, 0.3642, 1);
  color: #090909;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 2px;
}
.mil-blog-item .mil-descr {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
.mil-blog-item .mil-descr .mil-left {
  padding-right: 30px;
  width: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
.mil-blog-item .mil-descr .mil-right {
  width: 50%;
}
.mil-blog-item:hover .mil-pub-cover .mil-overlay {
  left: calc(50% - 2.5vw);
}
@media (max-width: 550px) {
  .mil-blog-item .mil-pub-cover .mil-overlay {
    top: 29%;
    height: 90px;
    left: calc(50% + 45px);
  }
}
@media (max-width: 768px) {
  .mil-blog-item .mil-descr {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .mil-blog-item .mil-descr .mil-left {
    width: 100%;
    padding: 0;
    margin-bottom: 30px;
  }
  .mil-blog-item .mil-descr .mil-right {
    width: 100%;
  }
}
@media (max-width: 1200px) {
  .mil-blog-item .mil-pub-cover {
    padding-bottom: 60%;
  }
  .mil-blog-item .mil-pub-cover .mil-overlay {
    height: 90px;
    left: calc(50% + 45px);
  }
  .mil-blog-item .mil-pub-cover:hover .mil-overlay {
    left: calc(50% - 45px);
  }
}

.mil-pub-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.mil-pub-info li {
  font-size: 16px;
  list-style-type: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.mil-pub-info li:after {
  content: "";
  width: 7px;
  height: 7px;
  border-radius: 50%;
  background-color: #91E01A;
  margin: 0 10px;
}
.mil-pub-info li:last-child:after {
  display: none;
}
.mil-pub-info li a {
  color: #949494;
  -webkit-transition: 0.2s cubic-bezier(0, 0, 0.3642, 1);
  transition: 0.2s cubic-bezier(0, 0, 0.3642, 1);
}
.mil-pub-info li a:hover {
  color: #91E01A;
}

/* -------------------------------------------

skills

------------------------------------------- */
.mil-prog-track {
  height: 2px;
  background-color: rgba(148, 148, 148, 0.3);
}
.mil-prog-track .mil-prog {
  position: relative;
  height: 100%;
  background-color: #91E01A;
}
.mil-prog-track .mil-prog:before {
  content: attr(data-number);
  position: absolute;
  top: -30px;
  right: 21px;
  background-color: #91E01A;
  color: #090909;
  height: 30px;
  padding: 0 5px 0 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 16px;
  font-weight: 400;
}
.mil-prog-track .mil-prog:after {
  content: "%";
  position: absolute;
  top: -30px;
  right: 0;
  background-color: #91E01A;
  color: #090909;
  height: 30px;
  padding: 0 10px 0 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 16px;
  font-weight: 400;
  border-radius: 0 15px 0 0;
}

/* -------------------------------------------

experience

------------------------------------------- */
.mil-exp-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.mil-exp-item .mil-date {
  width: 40%;
}
@media (max-width: 330px) {
  .mil-exp-item {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .mil-exp-item .mil-date {
    width: 100%;
    margin-bottom: 10px;
  }
}

/* -------------------------------------------

404

------------------------------------------- */
.mil-404-frame {
  padding: 90px 0 120px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100vh;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
@media (max-width: 992px) {
  .mil-404-frame {
    height: calc(100vh - 180px);
  }
}

.mil-error {
  font-family: "Sacramento", cursive;
  font-size: calc(1rem + 5vw);
  color: #91E01A;
  margin-left: 15px;
  line-height: 50%;
}

.mil-404 {
  font-size: calc(1rem + 10vw);
}
@media (max-width: 768px) {
  .mil-404 {
    margin-top: 30px;
    margin-bottom: 20px;
  }
}

/* -------------------------------------------

dark mode settings

------------------------------------------- */
body {
  background-color: #121212;
}

img {
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}

.mil-icon-part img {
  -webkit-filter: grayscale(0) hue-rotate(90deg) brightness(130%);
          filter: grayscale(0) hue-rotate(90deg) brightness(130%);
}

.mil-light-active:after {
  color: #090909;
  -webkit-filter: invert(80%);
          filter: invert(80%);
}

form input, form textarea {
  border-color: rgba(148, 148, 148, 0.3);
}

.mil-cursor-follower:after {
  -webkit-filter: invert(0);
          filter: invert(0);
}