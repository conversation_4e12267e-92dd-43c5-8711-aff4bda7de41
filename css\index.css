.container-customers {
	background: url(../_img/bg-office.jpg) #313131 no-repeat 50% 50%;

	padding: 95px 0 75px;

	height: 17em;

	transition: height 700ms ease;
}

.icon-customers {
	margin-bottom: 0;
}

.customers-buttons {
	float: right;
}

.testimonials-button {
	float: left;
}

.customers-logos-expanded {
	height: auto;

	padding-bottom: 0.2em;
}

.icon-featuredin-container {
	color: #999;

	padding: 10px 0;
}

.icon-featuredin {
	position: absolute;
}

.icon-featuredin-text {
	font-size: 0.12em;

	font-weight: 700;

	position: relative;

	text-transform: uppercase;
}

.icon-featuredin-1 {
	top: -0.05em;

	left: 20%;

	-webkit-animation: customers-rotation 20s -0.5s infinite;

	animation: customers-rotation 20s -0.5s infinite;
}

.icon-featuredin-2 {
	top: -0.05em;

	left: 32%;

	-webkit-animation: customers-rotation 20s -0.4s infinite;

	animation: customers-rotation 20s -0.4s infinite;
}

.icon-featuredin-3 {
	top: -0.05em;

	left: 43.5%;

	-webkit-animation: customers-rotation 20s -0.3s infinite;

	animation: customers-rotation 20s -0.3s infinite;
}

.icon-featuredin-4 {
	top: -0.05em;

	left: 58.5%;

	-webkit-animation: customers-rotation 20s -0.2s infinite;

	animation: customers-rotation 20s -0.2s infinite;
}

.icon-featuredin-5 {
	top: -0.05em;

	left: 71%;

	-webkit-animation: customers-rotation 20s -0.1s infinite;

	animation: customers-rotation 20s -0.1s infinite;
}

.icon-featuredin-6 {
	top: -0.05em;

	left: 86.1%;

	-webkit-animation: customers-rotation 20s 0s infinite;

	animation: customers-rotation 20s 0s infinite;
}

.icon-featuredin-7 {
	top: -0.05em;

	left: 20%;

	-webkit-animation: customers-rotation 20s -10.5s infinite;

	animation: customers-rotation 20s -10.5s infinite;

	opacity: 0;
}

.icon-featuredin-8 {
	top: -0.05em;

	left: 38%;

	-webkit-animation: customers-rotation 20s -10.4s infinite;

	animation: customers-rotation 20s -10.4s infinite;

	opacity: 0;
}

.icon-featuredin-9 {
	top: -0.05em;

	left: 55%;

	-webkit-animation: customers-rotation 20s -10.3s infinite;

	animation: customers-rotation 20s -10.3s infinite;

	opacity: 0;
}

.icon-featuredin-10 {
	top: -0.05em;

	left: 74%;

	-webkit-animation: customers-rotation 20s -10.2s infinite;

	animation: customers-rotation 20s -10.2s infinite;

	opacity: 0;
}

.icon-featuredin-11 {
	top: -0.05em;

	left: 86.1%;

	-webkit-animation: customers-rotation 20s -10.1s infinite;

	animation: customers-rotation 20s -10.1s infinite;

	opacity: 0;
}

@-webkit-keyframes customers-rotation {
	0% {
		-webkit-transform: translateY(0);

		opacity: 1;
	}

	2% {
		-webkit-transform: translateY(-0.6em);

		opacity: 0;
	}

	50% {
		-webkit-transform: translateY(0.6em);

		opacity: 0;
	}

	100%,
	52% {
		-webkit-transform: translateY(0);

		opacity: 1;
	}
}

@keyframes customers-rotation {
	0% {
		transform: translateY(0);

		opacity: 1;
	}

	2% {
		transform: translateY(-0.6em);

		opacity: 0;
	}

	50% {
		transform: translateY(0.6em);

		opacity: 0;
	}

	100%,
	52% {
		transform: translateY(0);

		opacity: 1;
	}
}

.container-alerts {
	background: #fff;

	background: -moz-linear-gradient(top, #fff 0, #d6dbde 100%);

	background: -webkit-linear-gradient(top, #fff 0, #d6dbde 100%);

	background: linear-gradient(to bottom, #fff 0, #d6dbde 100%);

	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#d6dbde', GradientType=0);

	padding-bottom: 480px;
}

.container-alerts:after,
.container-alerts:before {
	content: '';

	display: block;

	position: absolute;

	width: 100%;
}

.container-alerts:before {
	background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAABXCAIAAABKoLg0AAAAR0lEQVQY062PMQrAMAAC9d6aoWv/P5ghIS0hbSlkEkUOVTlObCPpUowiAKKMHDePWfbNxHlQgCQA7/0P7n1P1Hk/dsw/N/+rrlAaetxba94AAAAASUVORK5CYII=);

	background: -moz-linear-gradient(top, #646871 0, #949ba1 100%);

	background: -webkit-linear-gradient(top, #646871 0, #949ba1 100%);

	background: linear-gradient(to bottom, #646871 0, #949ba1 100%);

	border-bottom: 1px solid #a6aebb;

	bottom: 4px;

	height: 87px;
}

.container-alerts:after {
	background-color: #6f737c;

	bottom: 0;

	height: 4px;
}

#alert-pic {
	background: url(../_img/sprite-alert.png?v=3) no-repeat;

	bottom: 5px;

	height: 471px;

	left: 50%;

	margin-left: -407px;

	position: absolute;

	width: 821px;

	z-index: 5;
}

.container-performance {
	border-bottom: 12px solid #000;

	color: #666;

	padding: 130px 0 188px;
}

.container-performance:after,
.container-performance:before {
	bottom: 0;

	content: '';

	position: absolute;

	width: 100%;

	z-index: 5;
}

.container-performance:before {
	background-color: #f7f7f7;

	border-top: 2px solid #ccc;

	height: 123px;
}

.container-performance:after {
	background-color: #b2deff;

	border-top: 2px solid #5eb7f9;

	height: 31px;
}

.container-performance-footer {
	background-image: url(../_img/img-graphs.svg);

	background-size: 940px 287px;

	bottom: -12px;

	height: 287px;

	left: 50%;

	margin-left: -470px;

	position: absolute;

	width: 940px;

	z-index: 10;
}

.performance-container {
	color: #000;

	border: 2px solid #e6e6e6;

	-webkit-border-radius: 50%;

	-moz-border-radius: 50%;

	border-radius: 50%;

	height: 122px;

	margin-left: 150px;

	position: relative;

	width: 122px;
}

.performance-man:before {
	content: '';

	font-family: 'LigatureSymbols';

	font-size: 60px;

	left: -84px;

	position: absolute;

	top: 3px;

	-webkit-font-smoothing: antialiased;
}

.performance-container:before {
	content: '';

	position: absolute;

	top: 50px;

	left: -16px;

	border-style: solid;

	border-width: 10px 16px 10px 0;

	border-color: transparent #e6e6e6;

	display: block;

	width: 0;

	z-index: 0;
}

.performance-container:after {
	content: '';

	position: absolute;

	top: 51px;

	left: -13px;

	border-style: solid;

	border-width: 9px 15px 9px 0;

	border-color: transparent #fff;

	display: block;

	width: 0;

	z-index: 1;
}

.performance-satisfied:before {
	content: '\e018';
}

.performance-tolerating:before {
	content: '\e019';
}

.performance-frustrated:before {
	content: '\e01a';
}

.gauge-container {
	margin: 7px auto;

	position: relative;

	text-align: center;

	width: 107px;
}

.gauge-container .gauge {
	display: block;

	height: 81px;

	width: 107px;
}

.gauge-container .value {
	color: #333;

	font-size: 24px;

	font-weight: 700;

	position: absolute;

	top: 40px;

	width: 100%;
}

.features-item {
	-webkit-box-sizing: border-box;

	-moz-box-sizing: border-box;

	box-sizing: border-box;

	color: #fff;

	cursor: pointer;

	float: left;

	font-size: 12px;

	min-height: 10.5em;

	padding: 0.7em 1% 0% 1%;

	position: relative;

	width: 33.333%;

	margin-bottom: 15px;

	height: 75px;

	text-decoration: none !important;
}

.features-item:before {
	display: block;

	font-size: 2em;

	left: 9px;

	position: absolute;

	top: 0.45em;
}

.features-item1 {
	-webkit-box-sizing: border-box;

	-moz-box-sizing: border-box;

	box-sizing: border-box;

	color: #fff;

	cursor: pointer;

	float: left;

	font-size: 12px;

	min-height: 12.5em;

	position: relative;

	width: 15%;

	margin-bottom: 15px;
}

.features-item1:before {
	display: block;

	font-size: 2em;

	left: 9px;

	position: absolute;

	top: 0.45em;
}

.features-item-asset:before {
	content: '\E048';
}

.features-item-hrm:before {
	content: '\E05c';
}

.features-item-status:before {
	content: '\E084';
}

.features-item-onlineform:before {
	content: '\E038';
}

.features-item-pharmacy:before {
	content: '\E108';
}

.features-item-crm:before {
	content: '\E082';
}

.features-item-mobile:before {
	content: '\E085';
}

.features-item-bpo:before {
	content: '\E100';
}

.features-item-healthcare:before {
	content: '\E06e';
}

.features-item-digital:before {
	content: '\E173';
}

.features-item-ecommerce:before {
	content: '\E119';
}

.features-item-mobapps:before {
	content: '\E138';
}

.features-item-testing:before {
	content: '\E13a';
}

.features-item-system:before {
	content: '\E147';
}

.features-item-noicon:before {
	display: none;
}

.features-item-marked {
	background-color: rgba(3, 41, 86, 0.5);

	-webkit-border-radius: 5px;

	-moz-border-radius: 5px;

	border-radius: 5px;
}

.features-item-marked1 {
	background-color: rgba(3, 41, 86, 0.5);

	-webkit-border-radius: 5px;

	-moz-border-radius: 5px;

	border-radius: 5px;
}

.features-item > h4 {
	color: #fff;

	font-size: 1.2em;

	font-weight: 700;

	margin: 0;
}

.features-item > p {
	color: #d8eaff;

	font-size: 1.2em;

	line-height: 1.5em;

	margin-top: 0.5em;

	text-align: left;
}

.features-item-viewall {
	font-size: 1.1em;

	font-weight: 700;

	line-height: 7em;

	color: #fff;
}

.features-item-viewall a:link,
a:visit {
	color: #fff;
}

.features-item-viewall a:hover {
	color: #fff;
}

.features-item-viewall > a {
	border-bottom: 1px solid #fff;

	margin-left: 1em;

	text-decoration: none;

	color: #fff;
}

.features-item-viewall .icon-pingdom {
	margin-left: 0.6em;
}

@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 168dpi) {
	#alert-pic {
		background-image: url(../_img/<EMAIL>?v=3);

		background-size: 821px auto;
	}

	#map-container {
		background-image: url(../_img/<EMAIL>);

		background-size: 740px auto;
	}
}

@media (max-width: 975px) {
	.hero-info {
		min-height: 400px;

		width: 67%;
	}

	#hero-container {
		right: -350px;

		top: 0;
	}

	h1 {
		margin: 1em 0 0.7em;
	}

	#introduction {
		min-height: 0;

		padding-top: 10px;
	}

	#alert-pic {
		margin-left: -387px;
	}

	.container-performance {
		padding-top: 100px;
	}

	.performance-container {
		margin: 0 auto;

		-webkit-transform: translateX(45px);

		-moz-transform: translateX(45px);

		transform: translateX(45px);
	}

	.features-item {
		padding: 1% 1% 1% 5%;

		width: 33.3333%;
	}
}

@media (max-width: 740px) {
	.hero-info {
		min-height: 0;

		width: auto;
	}

	h1 {
		margin: 0 0 0.7em;
	}

	#introduction {
		padding-top: 30px;
	}

	#introduction h2 {
		line-height: 1.5em;

		margin-bottom: 0.6em;
	}

	.button-introduction {
		display: block;

		margin: 1.5em auto 0;
	}

	.container-customers {
		padding: 40px 0 70px;

		height: auto;

		transition: none;
	}

	.customers-text {
		font-size: 1.2em;
	}

	#customers-logos {
		font-size: 4em;
	}

	.customers-buttons {
		display: block;

		float: none;

		margin-top: 1em;
	}

	.container-alerts {
		padding-bottom: 340px;
	}

	#alert-pic {
		background-size: 600px auto;

		background-position: 0 100%;

		left: 20px;

		margin: 0;
	}

	.container-performance {
		padding-top: 40px;
	}

	.container-features {
	}

	.features-list {
		margin-top: 2em;
	}

	.features-item {
		font-size: 1em;

		min-height: 0;

		padding: 0 0 1.6em 2.6em;

		width: 100%;
	}

	.features-item:before {
		left: 0;

		top: 0;
	}

	.features-item > h4,
	.features-item > p {
		font-size: 1em;
	}

	.features-item-marked {
		background: 0 0;

		border: none;
	}

	.features-item-marked1 {
		background: 0 0;

		border: none;
	}

	.features-item-noicon {
		margin: 0;
	}

	.features-item-viewall {
		line-height: 1em;

		padding-bottom: 2em;
	}

	.features-item-viewall > a {
		margin-left: 0;
	}
}

.header_rght {
	display: flex;
}
