@charset "utf-8";

/* CSS Document */

body {
	margin: 0;
	padding: 0;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 100%;
	color: #767676;
	font-size: 16px !important;
	line-height: 22px;
	text-align: left;
	background-position: center top;
}

[hidden] {
	display: none;
}

::-webkit-scrollbar {
	width: 8px;
}

::-webkit-scrollbar-track {
	background-color: #eaeaea;

	border-left: 1px solid #ccc;
}

::-webkit-scrollbar-thumb {
	background-color: #218ecd;
}

::-webkit-scrollbar-thumb:hover {
	background-color: #0f6ba0;
}

html {
	-ms-text-size-adjust: 100%;

	-webkit-text-size-adjust: 100%;
}

b,
strong {
	font-weight: 700;
}

p {
	padding-bottom: 2px;

	/* text-align: justify; */
}

* {
	margin: 0;
	padding: 0;
	outline: 0;
}

/* a {
  border: none;
  outline: none;
}

a:link {
  color: inherit;
  text-decoration: underline;
}

a:visited {
  color: inherit;
  text-decoration: underline;
}

a:hover,
a:active {
  color: #1e93d7;
  text-decoration: none;
} */

img {
	max-width: 100% !important;
	height: auto;
	width: auto\9; /* ie8 */
}

.clear {
	clear: both;
}

h1,
h2,
h3,
h4,
h5,
h6,
.heading-1,
.heading-2,
.heading-3,
.heading-4,
.heading-5,
.heading-6,
.heading-7 .heading-8 {
	font-family: Arial, Helvetica, sans-serif;
}

.wrapper {
	width: 100%;
	height: auto;
	margin: 0;
	padding: 0;
}

.wrapper-inner {
	width: 1140px;
	height: auto;
	margin: 0 auto;
}

.wrapper-inner2 {
	width: 100%;
	height: auto;
	background-color: #000;
	position: relative;
	background-position: bottom;
	z-index: 10;
	top: 102.8%;
	padding: 10px 0px;
}

@-moz-document url-prefix() {
	.wrapper-inner2 {
		top: 100.8%;
	}
}

/*Banner area start*/

.banner {
	width: 100%;
	height: auto;
	padding: 0;
	margin: 0;
}

/*Banner area end*/

/*Header area start*/

.header_area {
	width: 100%;
	height: auto;
	background-image: url(../images/header-b.png);
	background-repeat: repeat;
	background-position: left top;
	border-bottom: 5px solid rgba(0, 0, 0, 0.3);
	z-index: 999;
	position: absolute;
	top: 0;
}

header {
	width: 100%;
	height: auto;
	background-image: url(../images/header-b.png);
	background-repeat: repeat;
	background-position: left top;
	border-bottom: 5px solid rgba(0, 0, 0, 0.3);
	z-index: 999;
	position: absolute;
	top: 0px;
	line-height: 108px;
	/* set animation -webkit-transition: all 0.4s ease; */
	transition: all 0.4s ease;
	left: -1px;
}

header.sticky {
	position: fixed;
	line-height: 48px;
	height: 77px;
	width: 100%;
	background-color: #bae1f8;
}

.logo_small {
	width: 102px;
	height: 72px;
	margin: 0;
	padding-top: 7px;
}

.header_area_right_small {
	margin-top: 9px;
}

.header_area_left {
	float: left;
	width: 20%;
	height: auto;
}

.header_area_right {
	float: right;
	width: 75%;
	height: auto;
	margin-top: 20px;
}

.header_area_right_small {
	float: right;
	width: 75%;
	height: auto;
	margin-top: 8px;
}

.logo-quote {
	width: 178px;
	height: auto;
	float: right;
	margin-left: 10px;
}

.search {
	width: auto;
	height: auto;
	float: right;
	margin: -5px 3px 0px 0px;
}

#searchForm fieldset {
	width: 220px;
	position: relative;
	display: block;
	border: none;
}

div.input {
	width: 192px;
	padding: 5px 15px 7px 15px;
	border-radius: 6px;
	float: left;
	background: #fff;
	border: 1px solid #ececec;
}

div.input.focus {
	border: 1px solid #ececec;
}

input#s {
	width: 100%;
	border: 0;
	border-radius: 20px;
	background: transparent;
	height: 16px;
	color: #b4bdc4;
	text-shadow: 0 1px 1px #fff;
}

input#s:focus {
	outline: none;
}

input#searchSubmit {
	width: 19px;

	height: 19px;

	text-indent: -9999px;

	overflow: hidden;

	background: url(../images/search-icon.png) no-repeat;

	border: 0;

	position: absolute;

	top: 12px;

	right: 16px;

	z-index: 5;

	display: none;

	cursor: pointer;
}

.logo-area {
	width: 30%;

	height: auto;

	float: left;

	margin: 0;
}

.logo {
	width: 155px;

	height: 72px;

	margin: 0;

	padding-top: 20px;
}

.head-search {
	width: 1140px;

	height: auto;

	margin: 0 auto;
}

.head-search1 {
	width: auto;

	height: auto;

	position: absolute;

	z-index: 500;

	background-color: rgba(0, 0, 0, 0.6);

	line-height: 25px;

	-webkit-border-bottom-right-radius: 8px;

	-webkit-border-bottom-left-radius: 8px;

	-moz-border-radius-bottomright: 8px;

	-moz-border-radius-bottomleft: 8px;

	border-bottom-right-radius: 8px;

	border-bottom-left-radius: 8px;

	margin-top: 4px;
}

ul.sptext {
	width: auto;

	height: auto;

	list-style-type: none;
}

ul.sptext li {
	width: auto;

	height: auto;

	list-style-type: none;

	color: #fff;

	font-size: 16px;

	border-right: 1px solid #191919;

	padding: 10px 25px;

	line-height: 20px;

	float: left;
}

ul.sptext li:link {
	color: #fff;

	text-decoration: none;
}

ul.sptext li:visit {
	color: #fff;

	text-decoration: none;
}

ul.sptext li:hover {
	color: #057fd2;

	text-decoration: none;

	cursor: pointer;
}

ul.sptext li:last-child {
	border-right: 0px;
}

/*header area end*/

.banner_caption {
	margin-top: 210px;

	width: 650px;

	height: auto;

	float: left;
}

.banner_cap_heading {
	font-size: 24px;

	text-shadow: 0 1px 1px #000;

	color: #fff;

	padding: 2px 10px 5px 10px;

	border-radius: 10px;

	-moz-border-radius: 2px;

	-webkit-border-radius: 2px;

	-ms-border-radius: 2px;

	line-height: 28px;

	background-color: rgba(0, 0, 0, 0.5);

	font-weight: 600 !important;

	margin-bottom: 10px;
}

.banner_cap_heading span {
	font-size: 14px;

	color: #fff;

	display: block;
}
view_more .banner_cap_cont {
	font-size: 16px;

	color: #000;

	padding: 10px 25px 15px 10px;

	border-radius: 2px;

	-moz-border-radius: 2px;

	-webkit-border-radius: 2px;

	-ms-border-radius: 2px;

	line-height: 22px;

	background-color: rgba(255, 255, 255, 0.4);

	margin-bottom: 10px;
}
.view_more1 {
	width: auto;
	height: auto;
	background: #1e93d7;
	border-radius: 4px;
	text-align: center;
	padding: 7px 15px;
	font-size: 14px;
	font-weight: 700;
	color: #fff;
}
.view_more {
	float: left;
	width: auto;
	height: auto;
	background: #1e93d7;
	border-radius: 6px;
	-moz-border-radius: 6px;
	-webkit-border-radius: 6px;
	-ms-border-radius: 6px;
	text-align: center;
	padding: 7px 15px;
	font-size: 14px;
	font-weight: 700;
	color: #fff;
}

.view_more:hover {
	background: #014574;
	color: #fff;
	cursor: pointer;
}

.view_more_icon {
	width: 6px;

	height: 9px;

	float: left;

	padding: 12px 5px 0 12px;
}

.view_more_cont {
	width: auto;

	height: auto;

	float: left;

	padding: 7px 10px;

	font-size: 14px;

	font-weight: 700;
}

.view_more_cont a {
	text-decoration: none;

	color: #fff;
}

.strip-bg {
	width: 100%;

	height: auto;
}

.strip_section {
	float: left;

	width: 21.9%;

	border-right: 1px solid rgba(0, 0, 0, 0.25);

	border-left: 1px solid rgba(255, 255, 255, 0.1);

	min-height: 160px;

	height: auto !important;

	padding: 1em 1.2em;
}

.strip_section:last-child {
	border-left: 1px solid rgba(255, 255, 255, 0.1);

	border-right: 0px solid rgba(0, 0, 0, 0.25);
}

.strip_section:first-child {
	border-top: 0;

	border-left: 0px solid rgba(0, 0, 0, 0.25);
}

.strip_section h1 {
	color: #fff;

	font-size: 18px;

	font-weight: normal;
}

.strip_section p {
	color: #c9c9c9;

	font-size: 14px;

	line-height: 20px;

	margin-top: 12px;

	margin-bottom: 9px;
}

.strip_section span {
	color: #3c97e6;

	font-size: 18px;
}

.strip_section span a {
	color: #3c97e6;

	font-size: 18px;

	text-decoration: none;
}

.strip_section span a:hover {
	color: #005aa9;

	text-decoration: none;
}

.footer {
	width: 100%;

	height: auto;

	font-size: 14px;

	color: #fff;

	padding: 15px 0 17px 0;

	position: relative;

	z-index: 999;
}

.footer_left {
	float: left;

	width: 65%;

	height: auto;
}

.footer_right {
	float: right;

	width: 25%;

	height: auto;
}

.social_icon_text {
	width: 85px;

	height: auto;

	float: left;

	margin-right: 20px;

	padding-top: 5px;
}

.social_icon {
	width: 28px;

	height: 27px;

	float: left;

	margin-right: 10px;
}

.social_icon1 {
	width: 28px;

	height: 27px;

	float: left;

	margin-right: 0px;
}

#carousel_inner {
	float: left; /* important for inline positioning */

	width: 100%; /* important (this width = width of list item(including margin) * items shown */

	overflow: hidden; /* important (hide the items outside the div) */ /* non-important styling bellow */

	margin-bottom: 36px;

	word-wrap: break-word;

	color: #c9c9c9;

	margin-top: 12px;
}

#carousel_ul {
	position: relative;

	left: 0px; /* important (this should be negative number of list items width(including margin) */

	list-style-type: none; /* removing the default styling for unordered list items */

	margin: 0px;

	padding: 0px;

	width: 9999px; /* important */ /* non-important styling bellow */
}

#carousel_ul li {
	width: 286px;

	height: auto;

	float: left;

	word-wrap: break-word;
}

#left_scroll,
#right_scroll {
	float: left;

	height: 24px;

	width: 24px;

	margin-left: 0px;

	margin-right: 0px;
}

#left_scroll img,
#right_scroll img {
	/*styling*/

	cursor: pointer;

	cursor: hand;
}

#left_scroll img:hover,
#right_scroll img:hover {
	/*styling*/

	cursor: pointer;

	cursor: hand;

	opacity: 0.6;
}

.nav_area1 {
	width: auto;

	height: auto;

	float: right;
}

.facebook {
	content: '\E047';
}

.fa {
	padding-top: 11px;
}

/*products area start*/

/*iframe*/

iframe {
	width: 100%;

	margin: 0 0 1em;

	border: 0;
}

#external-frame {
	min-height: 892px;
}

/*iframe*/

/*inner page area css start*/

.innerpage_area {
	width: 100%;
	height: auto;
	float: left;
	background-color: #fff;
}

.innerpage_area1 {
	width: 100%;
	height: auto;
	float: left;
	margin-top: 107px;
	background-color: #fff;
}

.innerpage_area2 {
	width: 100%;
	height: auto;
	float: left;
	margin-top: 107px;
	background-color: #f1f2f2;
}

.innerban {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/about_us_inner.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.csrban {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/csr-banner\(1350x217\).jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.blog-innerbanner1 {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/blog-banner.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.blog-innerbanner2 {
	width: 100%;
	height: 768px;
	float: left;
	background-image: url(../images/blog-banner3.jpg);
	background-repeat: no-repeat;
	background-size: cover;
	position: relative;
}

.blog-innerbanner3 {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/blog-banner2.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.digital-signature-banner {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/digital-signature-banner.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.block-chain-banner {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/blog-block-chain-banner.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.clientss {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/banner-clients.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.partners {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/banner-partner.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.partners2 {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/banner-cmm3.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.digital-transbanner {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/digital-transformation-banner2.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.blockchain-services {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/blockchain-services-inner-banner2.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.cyber-security-services {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/cyber-sequrity-services-banner2.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.custom-enterprise-software-development {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/enterprise-app-development-banner2.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.resource-augmentation-staffing {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/resource-augmentation-staffing-inner-banner1.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.artificial-intelligence {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/ai-banner-top.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.global-impact {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/global-impact-banner2.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.state-of-the-art-infrastructure {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/state-of-the-art-infrastructure-banner2.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.legacy-modernization-services {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/legacy-modernization-services-banner2.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.quality-assurance {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/quality-assurance-banner2.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.careers {
	width: 100%;

	height: 217px;

	float: left;

	background-image: url(../images/banner-careers.jpg);

	background-repeat: no-repeat;

	background-size: cover;
}

.contact-us3 {
	width: 100%;

	height: 217px;

	float: left;

	background-image: url(../images/contact-us-banner.jpg);

	background-repeat: no-repeat;

	background-size: cover;
}

.services {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/it-service-banner.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.tatwa-state-of-the-art-infrastructure {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/tatwa-state-of-the-art-infrastructure-banner2.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.smart-governance {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/smart-governance-banner2.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.bpo-services {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/bpo_inner_ban.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.technologies {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/technology_inner.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.esign-emsigner {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/esign-emsigner-banner2.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.e-commerce-solutions {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/e-commerce-solutions-banner2.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.mobile-apps-development {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/mobile-apps-dev-banner2.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.products {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/technology_inner.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.software-testing-services {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/software-testing-services.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.ucpass-services {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/UCPaaS-inner-banner.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.data-analytics {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/data-analytics-banner.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.privacy-policy {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/privacy-policy-banner\(1350x217\).jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.disclaimer {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/disclaimer-banner\(1350x217\).jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.innerdiv {
	width: 1140px;
	height: auto;
	margin: 0 auto;
}

.footerdiv {
	width: 100%;
	height: auto;
	margin: 0 auto;
}

.ban_left {
	width: 482px;
	height: auto;
	float: left;
	margin-top: 60px;
}

.ban_left1 {
	width: 550px;
	height: auto;
	float: left;
	margin-top: 80px;
}

.block-chain-banner-heading {
	height: auto;
	float: left;
	margin-top: 80px;
}

.heading-1 {
	font-size: 28px;
	color: #fff;
}

.breadcrumb {
	list-style: none;

	color: #ffffff;

	float: left;

	margin-top: 20px;
}

.breadcrumb > li {
	display: inline-block;
}

media='screen' *,
:after,
:before {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}

.breadcrumb > .active {
	color: #fff;
}

.breadcrumb > li + li:before {
	content: '/\00a0';

	padding: 0 5px;

	color: #fff;
}

.ban_right {
	width: 550px;
	height: auto;
	float: right;
	margin-top: 66px;
	display: flex;
}

.ban_right1 {
	width: 550px;

	height: auto;

	float: right;

	margin-top: 86px;
}

.break {
	width: 15px;

	height: 46px;

	float: left;
}

.font14 {
	font-size: 14px;
	color: #fff;
	text-align: center;
	float: left;
	width: 500px;
	padding: 0px 10px;
}

.font18 {
	font-size: 18px;
	color: #fff;
	text-align: center;
	float: left;
	width: 410px;
	padding: 0px 10px;
}

.font20 {
	font-size: 20px;
	color: #fff;
	text-align: center;
	float: left;
	width: 410px;
	padding: 0px 10px;
}

.inner_body {
	width: 100%;
	height: auto;
	float: left;
	position: relative;
	z-index: 10;
	margin-top: -41px;
}

.inner_body1 {
	width: 100%;
	height: auto;
	float: left;
	position: relative;
	z-index: 10;
	margin-top: 32px;
}

.inner_body_left {
	width: 727px;
	height: auto;
	float: right;
	margin-bottom: 15px;
}

.inner_body_left12 {
	width: 727px;
	height: auto;
	float: left;
	margin-bottom: 15px;
}

.inner_body_left12 p {
	/* padding-bottom: 10px; */
	text-align: justify;
}

.inner_body_left2 {
	width: 727px;
	height: auto;
	float: left;
}
/*new demo start*/
.inner_body_leftdemo {
	width: 346px;
	height: auto;
	float: left;
	margin-left: 17px;
}
/*new demo end*/
.inner_body_left1 .left {
	width: 377px;
	height: auto;
	float: left;
	margin-right: 28px;
}

.inner_body_left1 .right {
	width: 319px;
	height: auto;
	float: right;
}

.heading-2 {
	padding-top: 25px;
	font-size: 32px;
	color: #606060;
	line-height: 42px;
}

.heading-2 span {
	font-size: 32px;
	color: #218ecd;
}

.inner_tab_area {
	background-image: url(../images/transparent.png);
	background-repeat: repeat;
	background-position: left top;
	width: 100%;
	height: 40px;
	float: left;
}

.blk_bg {
	width: 707px;
	height: auto;
	float: left;
	background-color: #000;
	padding: 17px 10px;
}

.arial33 {
	color: #fff;
	font-size: 33px;
	text-align: center;
	line-height: 50px;
}

.arial16 {
	color: #fff;
	font-size: 16px;
	text-align: center;
}

.inner_body_right {
	width: 368px;
	height: auto;
	float: left;
	margin-left: 40px;
}

.inner_body_right1 {
	width: 368px;
	height: auto;
	float: left;
	margin-left: 0px;
}

.strip_section1 {
	margin-top: 25px;
	float: left;
	width: 100%;
	min-height: 144px;
	height: auto !important;
	padding: 10px 12px;
	border: 1px solid #cccccc;
	border-radius: 6px;
}

.strip_section1 h1 {
	color: #151515;
	font-size: 22px;
	font-weight: normal;
}

.strip_section1 p {
	color: #606060;
	font-size: 14px;
	line-height: 20px;
	margin-top: 12px;
	margin-bottom: 9px;
}

.strip_section1 span {
	color: #606060;
	font-size: 18px;
}

.strip_section1 span a {
	color: #606060;
	font-size: 18px;
	text-decoration: none;
}

.strip_section span1 a:hover {
	color: #005aa9;
	text-decoration: none;
}

#carousel_inner1 {
	float: left; /* important for inline positioning */
	width: 100%; /* important (this width = width of list item(including margin) * items shown */
	overflow: hidden; /* important (hide the items outside the div) */ /* non-important styling bellow */
	margin-bottom: 7px;
	word-wrap: break-word;
	color: #c9c9c9;
	margin-top: 5px;
}

#carousel_inner1 ul {
	position: relative;
	left: 0px; /* important (this should be negative number of list items width(including margin) */
	list-style-type: none; /* removing the default styling for unordered list items */
	margin: 0px;
	padding: 0px;
	width: 9999px; /* important */ /* non-important styling bellow */
}

#carousel_inner1 ul li {
	width: 350px !important;
	height: auto;
	float: left;
	word-wrap: break-word;
	color: #606060;
}

#left_scroll1,
#right_scroll1 {
	float: left;
	height: 24px;
	width: 24px;
	margin-left: 0px;
	margin-right: 0px;
}

#left_scroll1 img,
#right_scroll1 img {
	/*styling*/

	cursor: pointer;

	cursor: hand;
}

#left_scroll1 img:hover,
#right_scroll1 img:hover {
	/*styling*/

	cursor: pointer;

	cursor: hand;

	opacity: 0.6;
}

.why_tatwa {
	float: left;

	width: 100%;

	min-height: 128px;

	height: auto !important;

	padding: 5px 12px;

	border: 1px solid #cccccc;

	border-radius: 6px;

	margin-top: 15px;
}

.why_tatwa h1 {
	color: #151515;

	font-size: 22px;

	font-weight: normal;
}

.why_tatwa_cont {
	color: #606060;

	font-size: 14px;

	font-weight: normal;

	text-align: justify;

	padding-top: 5px;
}

.block-secondary {
	float: right;

	width: 100%;

	margin-top: 15px;
}

._block.-gold-black,
.-gold-black._site {
	color: #666666;

	background-color: #f6f2f2;
}

._quote.-gold-black blockquote {
	background-color: #f6f2f2;
}

._quote blockquote {
	font-size: 18px;

	color: #666666;

	line-height: 30px;

	letter-spacing: -0.03333em;

	background-color: #ccc;

	-webkit-border-radius: 0.3em;

	-moz-border-radius: 0.3em;

	border-radius: 0.3em;

	background-clip: padding-box;

	position: relative;

	font-style: italic;

	margin: 0;

	padding: 0;
}

._quote blockquote h1 {
	color: #151515;

	font-size: 22px;

	font-weight: normal;

	font-style: normal;

	padding: 10px 10px 0px 10px;
}

._quote blockquote p {
	margin: 0;

	padding: 5px 20px 38px 20px;

	text-align: center;
}

._quote.-gold-black blockquote {
	background-color: #f6f2f2;
}

._quote.-gold-black .quote-footer {
	color: #606060;

	padding-top: 5px;

	font-size: 14px;
}

._quote.-gold-black .quote-footer strong {
	color: #3390c5;
}

._quote .quote-image {
	float: left;

	display: inline;

	margin-right: 3em;

	margin-left: 10px;

	margin-top: -2.94118em;

	position: relative;

	-webkit-border-radius: 50%;

	-moz-border-radius: 50%;

	border-radius: 50%;

	width: 100px;

	height: 100px;

	background-color: #fff;

	overflow: hidden;

	border: 4px solid #fff;
}

.history_area {
	background-color: #484b4d;

	width: 100%;

	height: auto;

	float: left;

	max-height: 2200px;
}

.history_area1 {
	background-color: #484b4d;

	width: 100%;

	height: auto;

	float: left;

	min-height: 1031px;
}

.history_area2 {
	background-color: #fff;

	width: 100%;

	height: auto;

	float: left;

	min-height: 1031px;
}

.heading-3 {
	font-size: 36px;

	color: #fff;

	font-weight: 700;

	text-align: center;
}

.heading-3 span {
	font-size: 14px;

	color: #cbcbcb;

	font-weight: normal;

	text-align: center;
}

.timeline_section_inner {
	width: 1071px;

	height: auto;

	margin: 0 auto;
}

.timeline_section {
	width: 1071px;

	height: auto;

	float: left;
}

.timeline_section .left {
	width: 450px;

	height: auto;

	float: left;

	margin-top: 93px;
}

.timeline_section .left .sec {
	width: 450px;

	height: auto;

	float: left;

	margin-bottom: 116px;
}

.circles_big {
	width: 158px;

	height: 158px;

	float: left;

	background-color: #02669b;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-ms-border-radius: 50%;

	-webkit-border-radius: 50%;

	overflow: hidden;

	border: 8px solid #000;

	margin-right: 20px;
}

.circles_big img {
	width: 145px;

	height: 145px;
}

.arial18_yellow {
	color: #ffc600;

	font-size: 18px;

	font-weight: 700;
}

.arial18_yellow span {
	color: #fff;

	font-size: 14px;

	line-height: 20px;

	font-weight: normal;
}

.circles_small {
	width: 117px;

	height: 117px;

	float: left;

	background-color: #02669b;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-ms-border-radius: 50%;

	-webkit-border-radius: 50%;

	overflow: hidden;

	border: 8px solid #000;

	margin-left: 20px;
}

.circles_small img {
	width: 117px;

	height: 117px;
}

.circles_big1 {
	width: 158px;

	height: 158px;

	float: right;

	background-color: #02669b;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-ms-border-radius: 50%;

	-webkit-border-radius: 50%;

	overflow: hidden;

	border: 8px solid #000;

	margin-left: 20px;
}

.circles_big1 img {
	width: 145px;

	height: 145px;
}

.circles_small1 {
	width: 117px;

	height: 117px;

	float: left;

	background-color: #02669b;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-ms-border-radius: 50%;

	-webkit-border-radius: 50%;

	overflow: hidden;

	border: 8px solid #000;

	margin-right: 20px;
}

.circles_small1 img {
	width: 117px;

	height: 117px;
}

.timeline_section .middle {
	width: 170px;

	height: auto;

	float: left;
}

.timeline_section .middle_inner {
	margin: 15px;
}

.yello_circle {
	background-color: #ffc600;

	width: 100px;

	height: 100px;

	margin: 0 auto;

	text-align: center;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-webkit-border-radius: 50%;

	-ms-border-radius: 50%;

	-o-border-radius: 50%;

	text-align: center;

	overflow: hidden;
}

.yello_circle_heading {
	font-size: 15px;

	font-weight: 700;

	color: #634d02;

	line-height: 18px;

	padding-top: 10px;

	padding-bottom: 10px;

	border-bottom: 1px solid #dead02;
}

.yello_circle_heading_starting {
	font-size: 17px;

	font-weight: 700;

	color: #000;

	padding-top: 10px;
}

.timeline_section .middle_line {
	width: 8px;

	height: auto;

	margin: 0 auto;

	padding-bottom: 40px;

	background-color: #ffffff;
}

.middle_line1 {
	padding: 8px 0px;

	text-align: center;
}

.middle_line1_icon {
	width: 100px;

	height: auto;

	position: relative;

	z-index: 101;

	font-size: 18px;

	color: #ffc600;

	padding: 10px 0px;

	text-align: center;

	margin-left: -46px;

	margin-top: 100px;

	background-color: #484b4d;

	background-image: url(../images/icon_line.png);

	background-repeat: no-repeat;

	background-position: left 13px;
}

.middle_line1_icon1 {
	width: 100px;

	height: auto;

	position: relative;

	z-index: 101;

	font-size: 18px;

	color: #ffc600;

	padding: 10px 0px;

	text-align: center;

	margin-left: -46px;

	background-color: #484b4d;

	background-image: url(../images/icon_line.png);

	background-repeat: no-repeat;

	background-position: right 13px;

	margin-top: 100px;
}

.timeline_section .right {
	width: 450px;

	height: auto;

	float: left;

	margin-top: 230px;
}

.timeline_section .right .sec {
	width: 450px;

	height: auto;

	float: left;

	margin-bottom: 116px;
}

.footer_top {
	background-image: url(../images/footer_top.jpg);

	background-repeat: no-repeat;

	background-size: cover;

	width: 100%;

	height: 288px;

	margin-top: 0px;
}

.arial38 {
	font-size: 38px;

	color: #fff;

	text-align: center;

	padding-top: 83px;
}

.button_area {
	width: fit-content;
	display: flex;

	height: auto;

	margin: 0 auto;
}

.button_blue {
	width: auto;

	height: auto;

	float: left;

	text-align: center;

	padding: 14px 20px;

	margin-right: 20px;

	border: none;

	border-radius: 6px;

	-moz-border-radius: 6px;

	-webkit-border-radius: 6px;

	-ms-border-radius: 6px;

	-o-border-radius: 6px;

	color: #fff !important;

	font-size: 16px;

	text-transform: uppercase;

	background-color: #097ec1;

	text-decoration: none !important;
}

.button_blue:hover {
	cursor: pointer;

	background-color: #ffc600;

	color: #191300 !important;

	text-decoration: none !important;
}

.button_border {
	width: auto;

	height: auto;

	float: left;

	text-align: center;

	padding: 12px 18px;

	border: 2px solid #fff;

	border-radius: 6px;

	-moz-border-radius: 6px;

	-webkit-border-radius: 6px;

	-ms-border-radius: 6px;

	-o-border-radius: 6px;

	color: #fff !important;

	font-size: 16px;

	text-transform: uppercase;

	text-decoration: none !important;
}

.button_border:hover {
	cursor: pointer;

	background-color: #ffc600;

	color: #191300 !important;

	border: 2px solid #ffc600;
}

.mt20 {
	margin-top: 20px;
}

.mt50 {
	margin-top: 30px;
	margin-bottom: 30px;
}

/* leadership area start*/

.leadership_area {
	width: 1140px;
	height: auto;
	float: left;
	margin: 10px 0px;
}

.leadership {
	width: 380px;
	height: auto;
	float: left;
	margin: 20px 0px;
}

.col-md-4.col-sm-6 {
	width: 25%;
}

.leadership:hover {
	width: 380px;
	height: auto;
	float: left;
	cursor: pointer;
}

.leadership .leadership_circle {
	width: 192px;
	height: 192px;
	margin: 0 auto;
	overflow: hidden;
	background-color: #fff;
	border: 6px solid #ccc;
	margin-bottom: 25px;
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
	-webkit-transition: all 0.2s;
	-moz-transition: all 0.2s;
	-o-transition: all 0.2s;
	transition: all 0.2s;
}

.leadership:hover .leadership_circle:first-child {
	border: 6px solid #097ec1;
}

.leadership .heading {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 25px;
	text-align: center;
	color: #444;
}

.leadership .heading_sub1 {
	font-size: 16px;
	text-align: center;
	color: #787878;
	display: block;
	font-weight: normal;
	line-height: 35px;
}

.leadership .heading_sub {
	font-size: 16px;
	text-align: center;
	color: #787878;
	display: block;
	font-weight: normal;
	line-height: 35px;
}

.leadership .heading:last-child {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 25px;
	text-align: center;
	color: #444;
	-webkit-transition: all 0.2s;
	-moz-transition: all 0.2s;
	-o-transition: all 0.2s;
	transition: all 0.2s;
	font-weight: 700;
}

.leadership:hover .heading:last-child {
	color: #097ec1;
}

.popup_inner {
	margin: 15px;
}

.leader_heading {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 25px;
	text-align: center;
	color: #097ec1;
	padding-bottom: 20px;
	font-weight: 700;
}

/* leadership area end*/

.leadership3 {
	width: 285px;
	height: auto;
	float: left;
	margin: 20px 0px;
}

.leadership3:hover {
	width: 285px;
	height: auto;
	float: left;
	cursor: pointer;
}

.leaders3 {
	width: 380px;
	height: auto;
	float: left;
	margin: 20px 0px;
}

.leaders3:hover {
	width: 380px;
	height: auto;
	float: left;
	cursor: pointer;
}

.leadership3 .leadership_circle3 {
	width: 192px;
	height: 192px;
	margin: 0 auto;
	overflow: hidden;
	background-color: #fff;
	border: 6px solid #ccc;
	margin-bottom: 25px;
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
	-webkit-transition: all 0.2s;
	-moz-transition: all 0.2s;
	-o-transition: all 0.2s;
	transition: all 0.2s;
}

.leadership3:hover .leadership_circle3:first-child {
	border: 6px solid #097ec1;
}

.leadership3 .heading3 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 25px;
	text-align: center;
	color: #444;
}

.leadership3 .heading_sub3 {
	font-size: 16px;
	text-align: center;
	color: #787878;
	display: block;
	font-weight: normal;
	line-height: 35px;
}

.leadership3 .heading_sub3 {
	font-size: 16px;
	text-align: center;
	color: #787878;
	display: block;
	font-weight: normal;
	line-height: 35px;
}

.heading3:last-child {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 25px;
	text-align: center;
	color: #444;
	-webkit-transition: all 0.2s;
	-moz-transition: all 0.2s;
	-o-transition: all 0.2s;
	transition: all 0.2s;
	/* font-weight: 700; */
}

/* .leadership3:hover .heading3:last-child {
  color: #097ec1;
} */

/*global advisory area start*/

.global_advisory {
	width: 727px;

	height: auto;

	float: left;

	margin: 0px 0px 15px 0px;
}

.global_advisory_inner {
	width: 727px;

	height: auto;

	float: left;

	border-bottom: 1px solid #e1e2e2;

	text-align: justify;

	padding-bottom: 15px;
}

.mt15 {
	margin-top: 15px;
}

.global_advisory_inner .heading {
	font-size: 18px;

	font-weight: 700;

	color: #097ec1;

	line-height: 26px;
}

.heading_blue {
	font-size: 17px;

	font-weight: 700;

	color: #097ec1;

	line-height: 26px;
}

.mt30 {
	margin-top: 30px;
}

/*global advisory area end*/

/*quality policy start*/

ul.quality_policy {
	width: auto;
	height: auto;
	float: left;
	list-style-type: none;
	margin-bottom: 40px;
}

ul.quality_policy li {
	width: 680px;
	height: auto;
	float: left;
	background-image: url(../images/bullet.png);
	background-repeat: no-repeat;
	background-position: left 9px;
	padding-left: 15px;
	line-height: 26px;
}

.heading-4 {
	font-size: 20px;
	font-weight: 700;
	color: #097ec1;
	margin-bottom: 10px;
}

/*quality policy end*/

.learnmore {
	font-size: 14px;

	color: #097ec1;

	text-align: center;

	text-transform: uppercase;

	font-weight: 700;
}

.learnmore1 {
	font-size: 14px;
	color: #097ec1;
	text-align: center;
	text-transform: uppercase;
	font-weight: 700;
}

.leaders2 {
	width: 760px;
	height: auto;
	margin: 0 auto;
}

.leaders4 {
	width: 283px;
	height: auto;
	margin: 0 auto;
}

.leaders3 {
	width: 380px;
	height: auto;
	margin: 0 auto;
}

.leadership_inn {
	width: 380px;

	height: auto;

	float: left;
}

/*inner page area css end*/

/*tooltips  abouts us area start*/

a.tooltips {
	position: relative;

	display: inline;
}

a.tooltips span {
	position: absolute;

	width: 400px;

	color: #ffffff;

	background: #439ccf;

	height: auto;

	line-height: 20px;

	/*  text-align: center;*/

	visibility: hidden;

	border-radius: 8px;

	text-align: justify;

	padding: 14px;
}

a.tooltips span:after {
	content: '';

	position: absolute;

	bottom: 100%;

	left: 48%;

	margin-left: -8px;

	width: 0;

	height: 0;

	border-bottom: 8px solid #439ccf;

	border-right: 8px solid transparent;

	border-left: 8px solid transparent;
}

a:hover.tooltips span {
	visibility: visible;

	opacity: 1;

	/*left: 100%;*/

	top: 50%;

	margin-left: 40%;

	z-index: 999;

	margin-top: 250px;
}

a.tooltips1 {
	position: relative;

	display: inline;
}

a.tooltips1 span {
	position: absolute;

	width: 500px;

	color: #ffffff;

	background: #439ccf;

	height: auto;

	line-height: 20px;

	text-align: justify;

	/*  text-align: center;*/

	visibility: hidden;

	border-radius: 8px;

	padding: 14px;

	z-index: 99999;
}

a.tooltips1 span:after {
	content: '';

	position: absolute;

	bottom: 100%;

	left: 38%;

	margin-left: -8px;

	width: 0;

	height: 0;

	border-bottom: 8px solid #439ccf;

	border-right: 8px solid transparent;

	border-left: 8px solid transparent;
}

a:hover.tooltips1 span {
	visibility: visible;

	opacity: 1;

	/*left: 100%;*/

	top: 50%;

	z-index: 999;

	margin-top: 250px;
}

a.tooltips2 {
	position: relative;

	display: inline;
}

a.tooltips2 span {
	position: absolute;

	width: 400px;

	color: #ffffff;

	background: #439ccf;

	height: auto;

	line-height: 20px;

	text-align: justify;

	/*  text-align: center;*/

	visibility: hidden;

	border-radius: 8px;

	padding: 14px;
}

a.tooltips2 span:after {
	content: '';

	position: absolute;

	bottom: 100%;

	left: 48%;

	margin-left: -8px;

	width: 0;

	height: 0;

	border-bottom: 8px solid #439ccf;

	border-right: 8px solid transparent;

	border-left: 8px solid transparent;
}

a:hover.tooltips2 span {
	visibility: visible;

	opacity: 1;

	/*left: 100%;*/

	top: 50%;

	z-index: 999;

	margin-top: 250px;
}

a.tooltips3 {
	position: relative;

	display: inline;
}

a.tooltips3 span {
	position: absolute;

	width: 500px;

	color: #ffffff;

	background: #439ccf;

	height: auto;

	line-height: 20px;

	text-align: justify;

	/*  text-align: center;*/

	visibility: hidden;

	border-radius: 8px;

	padding: 14px;

	right: 500px;
}

a.tooltips3 span:after {
	content: '';

	position: absolute;

	top: 100%;

	left: 50%;

	margin-left: -8px;

	width: 0;

	height: 0;

	border-top: 8px solid #439ccf;

	border-right: 8px solid transparent;

	border-left: 8px solid transparent;
}

a:hover.tooltips3 span {
	visibility: visible;

	opacity: 1;

	/*left: 100%;*/

	top: 50%;

	z-index: 999;

	margin-top: 170px;
}

a.tooltips4 {
	position: relative;

	display: inline;
}

a.tooltips4 span {
	position: absolute;

	width: 500px;

	color: #ffffff;

	background: #439ccf;

	height: auto;

	line-height: 20px;

	text-align: justify;

	/*  text-align: center;*/

	visibility: hidden;

	border-radius: 8px;

	padding: 14px;

	right: 120px;
}

a.tooltips4 span:after {
	content: '';

	position: absolute;

	top: 100%;

	left: 50%;

	margin-left: -8px;

	width: 0;

	height: 0;

	border-top: 8px solid #439ccf;

	border-right: 8px solid transparent;

	border-left: 8px solid transparent;
}

a:hover.tooltips4 span {
	visibility: visible;

	opacity: 1;

	/*left: 100%;*/

	top: 50%;

	z-index: 999;

	margin-top: 70px;
}

/*tooltips  abouts us area end*/

/*services_area start*/

.row {
	/* width: 1140px; */

	margin: 0 auto;

	zoom: 1;
}

.grid_3 {
	width: 569px;

	display: table-cell;

	float: left;

	text-align: center;

	vertical-align: middle;

	border-bottom: 1px solid #cecfcf;

	border-right: 1px solid #cecfcf;

	background-color: #fff;

	height: auto;

	height: auto;

	padding: 40px 0px !important;
}

.grid_3:hover {
	background-color: #f6f6f6;

	cursor: pointer;
}

.nbr {
	border-right: 0px solid #cecfcf;
}

.grid_3.full {
	margin-left: 0;

	margin-right: 0;

	width: 569px;
}

.row:after {
	display: block;

	visibility: hidden;

	height: 0;

	clear: both;

	content: '.';
}

.row [class^='grid_']:last-child {
	border-right: 0;
}

.service_head {
	font-family: Arial, Helvetica, sans-serif;

	font-size: 25px;

	color: #097ec1;

	padding-bottom: 10px;

	text-decoration: none !important;

	font-weight: 700;

	margin-top: 10px;

	text-overflow: ellipsis;

	white-space: nowrap;

	overflow: hidden;

	padding: 0px 80px 10px 80px;
}

.sub_cont {
	font-size: 14px;

	color: #767676;

	text-align: center;

	line-height: 24px;

	padding: 0px 80px;
}

ul.services {
	width: 368px;

	height: auto;

	float: left;

	list-style-type: none;
}

ul.services li {
	width: 346px;

	height: 70px;

	float: left;

	border: 1px dashed #e6e6e6;

	list-style-type: none;

	border-radius: 8px;

	-moz-border-radius: 8px;

	-ms-border-radius: 8px;

	-webkit-border-radius: 8px;

	margin-bottom: 12px;

	padding: 10px;

	background-color: #fff;
}

ul.services li:hover {
	border: 1px dashed #097ec1;

	background-color: #ffffff;

	cursor: pointer;
}

ul.services li.active {
	border: 1px dashed #097ec1;

	background-color: #ffffff;

	cursor: pointer;
}

.hvr-pulse,
.hvr-pulse:hover,
.hvr-pulse:focus,
.hvr-pulse:active {
	display: inline-block;

	vertical-align: middle;

	-webkit-transform: translateZ(0);

	transform: translateZ(0);

	box-shadow: 0 0 1px rgba(0, 0, 0, 0);

	-webkit-backface-visibility: hidden;

	backface-visibility: hidden;

	-moz-osx-font-smoothing: grayscale;

	-webkit-animation-name: hvr-pulse;

	animation-name: hvr-pulse;

	-webkit-animation-duration: 1s;

	animation-duration: 1s;

	-webkit-animation-timing-function: linear;

	animation-timing-function: linear;

	-webkit-animation-iteration-count: infinite;

	animation-iteration-count: infinite;
}

ul.services1 {
	width: 368px;

	height: auto;

	float: left;

	list-style-type: none;
}

ul.services1 li {
	width: 346px;

	height: auto;

	float: left;

	border: 1px dashed #e6e6e6;

	list-style-type: none;

	border-radius: 8px;

	-moz-border-radius: 8px;

	-ms-border-radius: 8px;

	-webkit-border-radius: 8px;

	margin-bottom: 12px;

	padding: 10px;

	background-color: #fff;

	font-size: 20px;

	color: #606060;
}

ul.services1 li:hover {
	border: 1px dashed #097ec1;

	background-color: #097ec1;

	cursor: pointer;

	color: #fff;
}

ul.services1 li.active {
	border: 1px dashed #097ec1;

	background-color: #ffffff;

	cursor: pointer;
}

.icon_area {
	width: 70px;

	height: auto;

	float: left;

	font-size: 22px;

	color: #097ec1;

	margin-right: 10px;
}

.icon_area1 {
	width: 25px;

	height: auto;

	float: left;

	font-size: 20px;

	color: #097ec1;
}

.service_inner_right {
	width: 266px;

	height: auto;

	float: left;

	text-align: left;
}

.service_inner_right1 {
	width: 345px;

	height: auto;

	float: left;

	text-align: left;
}

.service_inner_right1 p {
	text-align: justify;
}

.service_inner_right p {
	text-align: left;
}

.service_inner_right1 .heading {
	font-size: 15px;

	color: #097ec1;

	font-weight: 700;

	text-align: left;
}

.service_inner_right1 .heading span {
	font-size: 14px;

	color: #7e7e7e;

	font-weight: normal;

	text-align: left;

	margin-bottom: 8px;

	display: block;
}

.service_inner_right .heading {
	font-size: 15px;

	color: #097ec1;

	font-weight: 700;

	text-overflow: ellipsis;

	white-space: nowrap;

	overflow: hidden;

	text-align: left;
}

.service_inner_right .heading p {
	font-size: 14px;

	text-align: left !important;
}

.pt {
	padding-top: 0px !important;
}

.pt1 {
	padding-top: 0px !important;

	padding-left: 14px !important;

	margin-top: -10px;
}

.pt2 {
	padding-top: 10px !important;

	padding-left: 14px !important;

	margin-top: -10px;
}

.innerban1 {
	width: 100%;

	height: 200px;

	float: left;

	background-image: url(../images/about_us_inner.jpg);

	background-repeat: no-repeat;

	background-size: cover;
}

ul.quality_policy1 {
	width: 100%;
	height: auto;
	float: left;
	list-style-type: none;
	margin-bottom: 20px;
}

ul.quality_policy1 li {
	width: 100%;
	height: auto;
	float: left;
	background-image: url(../images/bullet.png);
	background-repeat: no-repeat;
	background-position: left 9px;
	padding-left: 25px;
	line-height: 26px;
	display: inline-block;
}

ul.quality_policy3 {
	width: 40%;
	height: auto;
	float: left;
	list-style-type: none;
	margin-bottom: 20px;
}

ul.quality_policy3 li {
	width: 40%;
	height: auto;
	float: left;
	background-image: url(../images/bullet.png);
	background-repeat: no-repeat;
	background-position: left 9px;
	padding-left: 15px;
	line-height: 26px;
	display: inline-block;
	min-width: 400px;
}

ul.quality_policy4 {
	width: 100%;
	height: auto;
	float: left;
	list-style-type: none;
	margin-bottom: 20px;
}

ul.quality_policy4 li {
	width: 100%;
	height: auto;
	float: left;
	background-image: url(../images/bullet.png);
	background-repeat: no-repeat;
	background-position: left 9px;
	padding-left: 15px;
	line-height: 26px;
	display: inline-block;
	max-width: 200px;
}

ul.quality_policy5 {
	width: 100%;
	height: auto;
	float: left;
	list-style-type: none;
	margin-bottom: 20px;
	font-size: 14px;
}

ul.quality_policy5 li {
	width: 100%;
	height: auto;
	float: left;
	background-image: url(../images/bullet-white2.png);
	background-repeat: no-repeat;
	background-position: left 9px;
	padding-left: 15px;
	line-height: 26px;
	display: inline-block;
	max-width: 200px;
}
ul.quality_policy5 li {
	border-bottom: solid 0.2px #ffffff3d;
	border-width: 1px;
	/* text-decoration: underline; */
}

ul.quality_policy7 {
	width: 100%;
	height: auto;
	float: left;
	list-style-type: none;
	margin-bottom: 20px;
}

ul.quality_policy7 li {
	width: 100%;
	height: auto;
	float: left;
	background-image: url(../images/bullet3.png);
	background-repeat: no-repeat;
	background-position: left 9px;
	padding-left: 25px;
	line-height: 26px;
	display: inline-block;
	/* max-width: 200px; */
}

.custom-card {
	background-color: #218ecd !important;
	color: white;
	min-height: 510px;
}

#show-more1 {
	cursor: pointer;
	text-transform: uppercase;
	color: #00aeef;
	display: inline;
}

#show-less1 {
	cursor: pointer;
	text-transform: uppercase;
	color: #00aeef;
	display: none;
}

#show-more-content1 {
	display: none;
}

#show-less2 {
	cursor: pointer;
	text-transform: uppercase;
	color: #00aeef;
	display: none;
}

#show-more2 {
	cursor: pointer;
	text-transform: uppercase;
	color: #00aeef;
	display: inline;
}

#show-more-content2 {
	display: none;
}

#show-less3 {
	cursor: pointer;
	text-transform: uppercase;
	color: #00aeef;
	display: none;
}

#show-more3 {
	cursor: pointer;
	text-transform: uppercase;
	color: #00aeef;
	display: inline;
}

#show-more-content3 {
	display: none;
}

.professional-security-services {
	width: 211px;
	float: left;
	padding: 10px;
	color: #ffffff;
	background-color: #005aa9;
	margin-right: 10px;
	min-height: 365px;
}

.heading-5 {
	font-size: 20px;

	font-weight: 700;

	color: #097ec1;

	margin-bottom: 5px;
}

.arial17 {
	font-size: 16px;
	color: #404040;
	font-weight: 700;
}

.arial18 {
	font-size: 16px;
	color: #ffffff;
	font-weight: 700;
}

.arial19 {
	font-size: 16px;
	color: #ffffff;
	font-weight: 700;
}

.arialeducation {
	font-size: 16px;

	color: #404040;

	font-weight: 700;

	list-style-type: disc;
}

.arial14 {
	font-size: 14px;

	color: #747474;
}

.arial14_1 {
	font-size: 12px;
	color: #747474;
	text-align: center;
	padding: 15px 0px;
	font-weight: 700;
	text-transform: uppercase;
}

.arial14_2 {
	font-size: 12px;
	color: #ffffff;
	padding: 5px 15px;
	font-weight: 700;
	text-transform: uppercase;
}

.arial14_3 {
	font-size: 12px;
	color: #747474;
	text-align: center;
	padding: 15px 0px;
	font-weight: 700;
	text-transform: uppercase;
	margin-left: 10px;
}

.arial14_4 {
	font-size: 12px;
	color: #747474;
	text-align: center;
	padding: 15px 0px;
	font-weight: 700;
	text-transform: uppercase;
	margin-left: 10px;
}

.case_studeies {
	width: 100%;

	height: auto;

	float: left;
}

.coolfieldset,
.coolfieldset.expanded {
	border: 1px solid #e6e6e6;

	border-radius: 8px;
}

.coolfieldset.collapsed {
	border: 0;

	border-top: 1px solid #e6e6e6;

	border-radius: 8px;
}

.coolfieldset legend {
	padding-left: 15px;

	padding-right: 10px;

	cursor: pointer;

	border-radius: 8px;

	font-size: 18px;

	font-weight: 700;

	color: #097ec1;

	width: 97% !important;
}

.content_pad {
	padding: 15px 12px 15px 12px;
}

.coolfieldset legend,
.coolfieldset.expanded legend {
	background: transparent url(../images/expanded.gif) no-repeat center left;
}

.coolfieldset.collapsed legend {
	background: transparent url(../images/collapsed.gif) no-repeat center left;
}

.clients {
	width: 120px;

	height: 121px;

	float: left;

	margin-right: 20px;
}

.hvr-pulse,
.hvr-pulse:hover,
.hvr-pulse:focus,
.hvr-pulse:active {
	display: inline-block;

	vertical-align: middle;

	-webkit-transform: translateZ(0);

	transform: translateZ(0);

	box-shadow: 0 0 1px rgba(0, 0, 0, 0);

	-webkit-backface-visibility: hidden;

	backface-visibility: hidden;

	-moz-osx-font-smoothing: grayscale;

	-webkit-animation-name: hvr-pulse;

	animation-name: hvr-pulse;

	-webkit-animation-duration: 1s;

	animation-duration: 1s;

	-webkit-animation-timing-function: linear;

	animation-timing-function: linear;

	-webkit-animation-iteration-count: infinite;

	animation-iteration-count: infinite;
}

.clients1 {
	width: 120px;

	height: 121px;

	float: right;

	margin-left: 20px;
}

.clients_cont {
	width: 560px;

	height: auto;

	float: left;

	font-size: 16px;

	text-align: justify;

	line-height: 22px;
}

.clients_cont .clients_cont_heading {
	font-size: 17px;

	color: #404040;

	margin-bottom: 5px;

	font-weight: 700;
}

/*services area end*/

#nt-example1 {
	width: 368px;

	height: 472px !important;

	float: left;

	list-style-type: none;

	margin: auto;

	overflow: hidden;
}

.modal-body {
	position: relative;

	padding: 0px 30px !important;
}

.modal-header {
	border-bottom: 0px solid #114888 !important;
}

#nt-example1-container {
	text-align: right;
}

#nt-example1-container i {
	margin: 0px 4px 14px 4px;

	cursor: pointer;

	-webkit-transition: all 0.1s ease-in-out;

	-moz-transition: all 0.1s ease-in-out;

	-ms-transition: all 0.1s ease-in-out;

	-o-transition: all 0.1s ease-in-out;

	transition: all 0.1s ease-in-out;
}

#nt-example1-container i:hover {
	color: #333;
}

#nt-example1 li {
	overflow: hidden;

	list-style: none;

	width: auto;

	height: auto;

	float: left;

	border: 1px dashed #999999;

	text-align: left;

	list-style-type: none;

	border-radius: 8px;

	-moz-border-radius: 8px;

	-ms-border-radius: 8px;

	-webkit-border-radius: 8px;

	margin-bottom: 12px;

	padding: 8px;

	background-color: #fff;
}

#nt-example1 li:hover {
	border: 1px dashed #097ec1;

	background-color: #ffffff;

	cursor: pointer;
}

#nt-example1 li.active {
	border: 1px dashed #097ec1;

	background-color: #ffffff;

	cursor: pointer;
}

.heading-6 {
	font-size: 29px;

	color: #606060;

	float: left;

	padding-top: 16px;
}

.testi_inner {
	width: 1002px;

	height: auto;

	margin: 0 auto;
}

.arrow_box_white {
	position: relative;

	background: #37393a;

	border: 1px solid #37393a;

	padding: 15px;

	-webkit-border-radius: 6px;

	-moz-border-radius: 6px;

	border-radius: 6px;

	font-size: 14px;

	color: #f0f0f0;

	line-height: 21px;

	top: 67px;
}

.arrow_box_white:after,
.arrow_box_white:before {
	left: 100%;

	top: 50%;

	border: solid transparent;

	content: ' ';

	height: 0;

	width: 0;

	position: absolute;

	pointer-events: none;
}

.arrow_box_white:after {
	border-left-color: #37393a;

	border-width: 10px;

	margin-top: -10px;
}

.arrow_box_white:before {
	border-left-color: #37393a;

	border-width: 11px;

	margin-top: -11px;
}

.arrow_box_white_left {
	position: relative;

	background: #37393a;

	border: 1px solid #37393a;

	position: relative;

	padding: 15px;

	-webkit-border-radius: 6px;

	-moz-border-radius: 6px;

	border-radius: 6px;

	font-size: 14px;

	color: #f0f0f0;

	line-height: 21px;

	top: 238px;
}

.arrow_box_white_left:after,
.arrow_box_white_left:before {
	right: 100%;

	top: 50%;

	border: solid transparent;

	content: ' ';

	height: 0;

	width: 0;

	position: absolute;

	pointer-events: none;
}

.arrow_box_white_left:after {
	border-color: rgba(255, 255, 255, 0);

	border-right-color: #37393a;

	border-width: 10px;

	margin-top: -10px;
}

.arrow_box_white_left:before {
	border-color: rgba(232, 232, 232, 0);

	border-right-color: #37393a;

	border-width: 11px;

	margin-top: -11px;
}

.arrow_box_left {
	position: relative;

	background: #37393a;

	border: 1px solid #37393a;

	padding: 10px;

	-webkit-border-radius: 6px;

	-moz-border-radius: 6px;

	border-radius: 6px;

	font-size: 21px;

	color: #ffc600;

	line-height: 23px;

	top: 83px;

	text-align: left;
}

.arrow_box_left:after,
.arrow_box_left:before {
	right: 100%;

	top: 50%;

	border: solid transparent;

	content: ' ';

	height: 0;

	width: 0;

	position: absolute;

	pointer-events: none;
}

.arrow_box_left:after {
	border-color: rgba(248, 248, 248, 0);

	border-right-color: #37393a;

	border-width: 10px;

	margin-top: -10px;
}

.arrow_box_left:before {
	border-color: rgba(232, 232, 232, 0);

	border-right-color: #37393a;

	border-width: 11px;

	margin-top: -11px;
}

.arrow_box_right {
	position: relative;

	background: #37393a;

	border: 1px solid #37393a;

	padding: 10px;

	-webkit-border-radius: 6px;

	-moz-border-radius: 6px;

	border-radius: 6px;

	font-size: 21px;

	color: #ffc600;

	line-height: 23px;

	top: 128px;

	margin-top: 58px;

	text-align: right;
}

.arrow_box_right:after,
.arrow_box_right:before {
	left: 100%;

	top: 50%;

	border: solid transparent;

	content: ' ';

	height: 0;

	width: 0;

	position: absolute;

	pointer-events: none;
}

.arrow_box_right:after {
	border-color: rgba(248, 248, 248, 0);

	border-left-color: #37393a;

	border-width: 10px;

	margin-top: -10px;
}

.arrow_box_right:before {
	border-color: rgba(232, 232, 232, 0);

	border-left-color: #37393a;

	border-width: 11px;

	margin-top: -11px;
}

.testi_left_cont {
	width: 430px;

	height: auto;

	float: left;
}

.testi_middle_cont {
	width: 140px;

	height: auto;

	float: left;

	background-image: url(../images/testi_line.png);

	background-position: center top;

	background-repeat: repeat-y;

	min-height: 802px;
}

.testi_middle_cont5 {
	width: 140px;

	height: auto;

	float: left;

	background-image: url(../images/testi_line1.png);

	background-position: center top;

	background-repeat: repeat-y;

	min-height: 802px;
}

.testi_middle_cont .one {
	width: 100px;

	height: 100px;

	position: relative;

	top: 65px;

	left: 18px;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-ms-border-radius: 50%;

	-webkit-border-radius: 50%;

	background-color: #fff;

	border: 2px solid #fff;

	overflow: hidden;
}

.testi_middle_cont .two {
	width: 100px;

	height: 100px;

	position: relative;

	top: 177px;

	left: 18px;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-ms-border-radius: 50%;

	-webkit-border-radius: 50%;

	background-color: #fff;

	border: 2px solid #fff;

	overflow: hidden;
}

.testi_middle_cont .three {
	width: 100px;

	height: 100px;

	position: relative;

	top: 287px;

	left: 18px;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-ms-border-radius: 50%;

	-webkit-border-radius: 50%;

	background-color: #fff;

	border: 2px solid #fff;

	overflow: hidden;
}

.testi_middle_cont .four {
	width: 100px;

	height: 100px;

	position: relative;

	top: 399px;

	left: 18px;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-ms-border-radius: 50%;

	-webkit-border-radius: 50%;

	background-color: #fff;

	border: 2px solid #fff;

	overflow: hidden;
}

.one {
	width: 100px;

	height: 100px;

	position: relative;

	top: 65px;

	left: 18px;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-ms-border-radius: 50%;

	-webkit-border-radius: 50%;

	background-color: #fff;

	border: 2px solid #ccc;

	overflow: hidden;
}

.two {
	width: 100px;

	height: 100px;

	position: relative;

	top: 177px;

	left: 18px;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-ms-border-radius: 50%;

	-webkit-border-radius: 50%;

	background-color: #fff;

	border: 2px solid #ccc;

	overflow: hidden;
}

.three {
	width: 100px;

	height: 100px;

	position: relative;

	top: 287px;

	left: 18px;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-ms-border-radius: 50%;

	-webkit-border-radius: 50%;

	background-color: #fff;

	border: 2px solid #ccc;

	overflow: hidden;
}

.four {
	width: 100px;

	height: 100px;

	position: relative;

	top: 399px;

	left: 18px;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-ms-border-radius: 50%;

	-webkit-border-radius: 50%;

	background-color: #fff;

	border: 2px solid #ccc;

	overflow: hidden;
}

.five {
	width: 100px;

	height: 100px;

	position: relative;

	top: 399px;

	left: 18px;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-ms-border-radius: 50%;

	-webkit-border-radius: 50%;

	background-color: #fff;

	border: 2px solid #ccc;

	overflow: hidden;
}

.six {
	width: 100px;

	height: 100px;

	position: relative;

	top: 399px;

	left: 18px;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-ms-border-radius: 50%;

	-webkit-border-radius: 50%;

	background-color: #fff;

	border: 2px solid #ccc;

	overflow: hidden;
}

.seven {
	width: 100px;

	height: 100px;

	position: relative;

	top: 399px;

	left: 18px;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-ms-border-radius: 50%;

	-webkit-border-radius: 50%;

	background-color: #fff;

	border: 2px solid #ccc;

	overflow: hidden;
}

.testi_right_cont {
	width: 430px;

	height: auto;

	float: left;
}

.arial18 {
	font-size: 16px;

	color: #f0f0f0;

	text-align: right;
}

.arial18_r {
	font-size: 16px;

	color: #f0f0f0;

	text-align: left;
}

.registered-office {
	width: 727px;
	height: auto;
	float: left;
	display: flex;
}

.registered-office .icon {
	width: 25px;
	height: auto;
	float: left;
	font-size: 16px;
	/* padding-top: 3px; */
}

.registered-office .icon1 {
	width: 20px;

	height: auto;

	float: left;

	font-size: 16px;
}

.pt3 {
	padding-top: 3px !important;
}

.registered-office .content {
	width: auto;

	height: auto;

	float: left;
}

.registered-office .sec {
	float: left;

	width: auto;

	padding-right: 35px;
}

.contact_form_areabg {
	width: 725px;

	height: auto;

	float: left;

	-webkit-border-radius: 4px;

	-moz-border-radius: 4px;

	border-radius: 4px;

	background-color: #f3f3f3;

	border: 1px solid #e8e7e8;

	margin: 30px 0px;
}

.contact_form_areabg .inner {
	margin: 10px;
}

ul.form_area {
	width: 704px;

	height: auto;

	float: left;

	list-style-type: none;
}

ul.form_area li {
	width: 220px;

	height: auto;

	float: left;

	margin-right: 18px;
}

ul.form_area li:last-child {
	margin-right: 0px;
}

.generalDropDown {
	background: none repeat scroll 0 0 #fff;

	border: 1px solid #dadada;

	border-radius: 4px;

	color: #62717a;

	font-family: Arial, Helvetica, sans-serif;

	font-size: 14px;

	font-weight: normal;

	height: 40px !important;

	padding: 3px;

	vertical-align: middle;

	width: 345px;

	font-style: italic;
}

.generalTextBox {
	background: none repeat scroll 0 0 #fff;

	border: 1px solid #dadada;

	border-radius: 4px;

	color: #62717a;

	font-family: Arial, Helvetica, sans-serif;

	font-size: 14px;

	font-weight: normal;

	height: 27px;

	padding: 3px;

	vertical-align: middle;

	width: 100%;

	font-style: italic;
}

.submit_bot {
	background-color: #097ec1;

	display: block;

	height: auto;

	width: auto;

	border: none;

	font-size: 17px;

	text-align: center;

	color: #fff;

	text-transform: uppercase;

	cursor: pointer;

	padding: 6px 15px;

	-webkit-border-top-left-radius: 8px;

	-webkit-border-top-right-radius: 8px;

	-moz-border-radius-topleft: 8px;

	-moz-border-radius-topright: 8px;

	border-top-left-radius: 8px;

	border-top-right-radius: 8px;
}

.submit_bot:hover {
	background-color: #ffc600;

	color: #191300;

	cursor: pointer;
}

.button_green1 {
	width: auto;

	height: auto;

	padding: 8px 15px;

	float: left;

	background-color: #097ec1;

	-webkit-border-radius: 8px;

	-moz-border-radius: 8px;

	border-radius: 8px;

	font-family: 'Calibri';

	color: #fff;

	text-transform: uppercase;

	text-align: center;

	margin-right: 20px;

	font-size: 17px;

	border: none;
}

.button_green1:hover {
	background-color: #ffc600;

	color: #191300;

	cursor: pointer;

	border: none;
}

.map_area {
	width: 724px;

	height: auto;

	float: left;

	border: 1px solid #e8e7e8;

	background-color: #fff;

	border-radius: 4px;

	-ms-border-radius: 4px;

	-moz-border-radius: 4px;

	-webkit-border-radius: 4px;
}

.map_area .inner {
	padding: 10px;
}

.testi_left_cont1 {
	width: 576px;

	height: auto;

	float: left;

	margin-right: 10px;
}

.arrow_box_white1 {
	position: relative;

	background: #ffffff;

	padding: 15px;

	-webkit-border-radius: 6px;

	-moz-border-radius: 6px;

	border-radius: 6px;

	font-size: 14px;

	color: #5b5b5b;

	line-height: 20px;

	top: 19px;

	border: 1px solid #dee2e6;

	margin: 20px;

	box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}

.arrow_box_white1:after,
.arrow_box_white1:before {
	left: 100%;

	top: 11%;

	border: solid transparent;

	content: ' ';

	height: 0;

	width: 0;

	position: absolute;

	pointer-events: none;
}

.arrow_box_white1:after {
	border-left-color: #f3f3f3;

	border-width: 10px;

	margin-top: -10px;
}

.arrow_box_white1:before {
	border-left-color: #f3f3f3;

	border-width: 11px;

	margin-top: -11px;
}

.arrow_box_right1 {
	position: relative;

	background: #f3f3f3;

	padding: 10px;

	-webkit-border-radius: 6px;

	-moz-border-radius: 6px;

	border-radius: 6px;

	font-size: 21px;

	color: #5b5b5b;

	line-height: 23px;

	top: 128px;

	margin-top: 58px;

	text-align: right;
}

.arrow_box_right1:after,
.arrow_box_right1:before {
	left: 100%;

	top: 50%;

	border: solid transparent;

	content: ' ';

	height: 0;

	width: 0;

	position: absolute;

	pointer-events: none;
}

.arrow_box_right1:after {
	border-left-color: #f3f3f3;

	border-width: 10px;

	margin-top: -10px;
}

.arrow_box_right1:before {
	border-left-color: #f3f3f3;

	border-width: 11px;

	margin-top: -11px;
}

.testi_middle_cont6 {
	width: 140px;

	height: auto;

	float: left;

	margin-top: 19px;
}

.testi_middle_cont6 .one {
	width: 100px;

	height: 100px;

	position: relative;

	top: 0px;

	left: 21px;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-ms-border-radius: 50%;

	-webkit-border-radius: 50%;

	background-color: #218ecd;

	text-align: center;
}

.testi_middle_cont1 {
	width: 140px;

	height: auto;

	float: left;

	background-image: url(../images/testi_line-grey.png);

	background-position: center top;

	background-repeat: repeat-y;

	min-height: 431px;

	margin-top: 19px;
}

.testi_middle_cont1 .one {
	width: 100px;

	height: 100px;

	position: relative;

	top: 0px;

	left: 21px;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-ms-border-radius: 50%;

	-webkit-border-radius: 50%;

	background-color: #218ecd;

	text-align: center;
}

.icon_career {
	padding-top: 1px;

	text-align: center;

	font-size: 24px;

	color: #ffffff;
}

.testi_middle_cont1 .two {
	width: 100px;

	height: 100px;

	position: relative;

	top: 234px;

	left: 21px;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-ms-border-radius: 50%;

	-webkit-border-radius: 50%;

	background-color: #218ecd;
}

.testi_middle_cont1 .three {
	width: 100px;

	height: 100px;

	position: relative;

	top: 287px;

	left: 21px;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-ms-border-radius: 50%;

	-webkit-border-radius: 50%;

	background-color: #218ecd;
}

.testi_middle_cont1 .four {
	width: 100px;

	height: 100px;

	position: relative;

	top: 399px;

	left: 21px;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-ms-border-radius: 50%;

	-webkit-border-radius: 50%;

	background-color: #218ecd;
}

.testi_middle_cont1 .five {
	width: 100px;

	height: 100px;

	position: relative;

	top: 399px;

	left: 21px;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-ms-border-radius: 50%;

	-webkit-border-radius: 50%;

	background-color: #218ecd;
}

.testi_middle_cont1 .sixth {
	width: 100px;

	height: 100px;

	position: relative;

	top: 399px;

	left: 21px;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-ms-border-radius: 50%;

	-webkit-border-radius: 50%;

	background-color: #218ecd;
}

ul.quality_policy2 {
	width: 100%;
	height: auto;
	float: left;
	list-style-type: none;
	padding-left: 0rem !important;
}

ul.quality_policy2 li {
	width: 100%;
	height: auto;
	float: left;
	background-image: url(../images/bullet.png);
	background-repeat: no-repeat;
	background-position: left 9px;
	padding-left: 15px;
	line-height: 26px;
}

.pt30 {
	padding-top: 30px;
}

.testi_left_cont2 {
	width: 576px;

	height: auto;

	float: left;

	margin-right: 10px;
}

.arrow_box_white2 {
	position: relative;

	background: #f3f3f3;

	padding: 15px;

	-webkit-border-radius: 6px;

	-moz-border-radius: 6px;

	border-radius: 6px;

	font-size: 14px;

	color: #5b5b5b;

	line-height: 21px;

	top: 19px;
}

.arrow_box_white2:after,
.arrow_box_white2:before {
	left: 100%;

	top: 19%;

	border: solid transparent;

	content: ' ';

	height: 0;

	width: 0;

	position: absolute;

	pointer-events: none;
}

.arrow_box_white2:after {
	border-left-color: #f3f3f3;

	border-width: 10px;

	margin-top: -10px;
}

.arrow_box_white2:before {
	border-left-color: #f3f3f3;

	border-width: 11px;

	margin-top: -11px;
}

.arrow_box_right2 {
	position: relative;

	background: #f3f3f3;

	padding: 10px;

	-webkit-border-radius: 6px;

	-moz-border-radius: 6px;

	border-radius: 6px;

	font-size: 21px;

	color: #5b5b5b;

	line-height: 23px;

	top: 128px;

	margin-top: 58px;

	text-align: right;
}

.arrow_box_right2:after,
.arrow_box_right2:before {
	left: 100%;

	top: 50%;

	border: solid transparent;

	content: ' ';

	height: 0;

	width: 0;

	position: absolute;

	pointer-events: none;
}

.arrow_box_right2:after {
	border-left-color: #f3f3f3;

	border-width: 10px;

	margin-top: -10px;
}

.arrow_box_right2:before {
	border-left-color: #f3f3f3;

	border-width: 11px;

	margin-top: -11px;
}

.testi_middle_cont2 {
	width: 140px;

	height: auto;

	float: left;

	background-image: url(../images/careers_line_blue.png);

	background-position: center top;

	background-repeat: repeat-y;

	min-height: 567px;

	margin-top: 19px;
}

.testi_middle_cont2 .employee {
	width: 100px;

	height: 100px;

	position: relative;

	top: 0px;

	left: 21px;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-ms-border-radius: 50%;

	-webkit-border-radius: 50%;

	background-color: #dbdbdb;

	border: 2px solid #dbdbdb;

	overflow: hidden;
}

.testi_middle_cont2 .employee1 {
	width: 100px;

	height: 100px;

	position: relative;

	top: 148px;

	left: 21px;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-ms-border-radius: 50%;

	-webkit-border-radius: 50%;

	background-color: #dbdbdb;

	border: 2px solid #dbdbdb;

	overflow: hidden;
}

.testi_middle_cont2 .employee2 {
	width: 100px;

	height: 100px;

	position: relative;

	top: 287px;

	left: 21px;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-ms-border-radius: 50%;

	-webkit-border-radius: 50%;

	background-color: #dbdbdb;

	border: 2px solid #dbdbdb;

	overflow: hidden;
}

.testi_middle_cont2 .employee3 {
	width: 100px;

	height: 100px;

	position: relative;

	top: 399px;

	left: 21px;

	border-radius: 50%;

	-moz-border-radius: 50%;

	-ms-border-radius: 50%;

	-webkit-border-radius: 50%;

	background-color: #dbdbdb;

	border: 2px solid #dbdbdb;

	overflow: hidden;
}

.construction {
	font-size: 30px;

	color: #cccccc;

	font-weight: 600;

	text-align: center;

	padding-top: 30px;

	font-family: 'Open Sans', sans-serif;
}

.ptf {
	padding-top: 0px !important;

	font-size: 60px;
}

.pt1f {
	padding-top: 0px !important;

	margin-top: -10px !important;
}

.container {
	width: 100% !important;
}

.modal-header {
	padding: 0px 15px !important;
}

.modal-header .close {
	margin-top: 4px !important;

	color: #fff !important;
}

.close {
	font-size: 25px !important;

	color: #fff !important;

	opacity: 1 !important;
}

.close:hover,
.close:focus {
	color: #fff !important;

	text-decoration: none !important;

	cursor: pointer !important;
}

h1,
.h1,
h2,
.h2,
h3,
.h3 {
	margin-top: 5px !important;

	margin-bottom: 5px !important;
}

.video {
	height: auto;
}

.full {
	width: 98.6%;
}

.serviceimg {
	float: right;
}

.productimg {
	float: right;
}

.servicecont {
	float: left;

	width: 400px;
}

/*full width pop up area start*/

/*full width pop up area end*/

/*product listing area start*/

.inner_body_right_grey {
	width: 338px;
	height: auto;
	float: right;
	margin-left: 40px;
	background-color: #f6f6f6;
	border: 1px solid #f6f6f6;
	padding: 10px;
	box-shadow: -6px 0px 10px #ccc;
}

.inner_body_right_grey h1 {
	width: 328px;
	height: auto;
	float: left;
	font-size: 18px;
	text-transform: uppercase;
	color: #fff;
	text-align: left;
	padding: 14px 5px;
	background-color: #404040;
	margin-top: 0px !important;
	margin-bottom: 0px !important;
}

ul.product-listing {
	width: 340px;
	height: auto;
	float: left;
	list-style-type: none;
	margin: 0;
	padding: 0;
}

ul.product-listing li {
	width: 328px;
	height: auto;
	float: left;
	list-style-type: none;
	font-size: 15px;
	color: #737373;
	font-family: Arial, Helvetica, sans-serif;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
	text-align: left;
	border-bottom: 1px solid #e0e0e0;
	padding: 10px 5px;
}

ul.product-listing li:last-child {
	border-bottom: 0px solid #e7e7e7;
}

ul.product-listing li.active {
	background-color: #1e93d7;
	color: #fff;
	cursor: pointer;
}

ul.product-listing li:hover {
	background-color: #1e93d7;
	color: #fff;
	cursor: pointer;
}

/*product listing area end*/

ul.services_list {
	width: 727px;

	height: auto;

	float: left;

	margin-bottom: 15px;

	list-style-type: none;

	margin-top: 20px;

	padding: 0;
}

ul.services_list li {
	width: 100%;
	height: auto;
	float: left;
	margin: 0 40px 20px 0;
	border: 2px solid #ccc;
	-webkit-border-radius: 6px;
	-moz-border-radius: 6px;
	border-radius: 6px;
	padding: 10px;
	font-weight: 700;
	font-size: 26px;
	color: #1e93d7;
	text-align: center;
}

ul.services_list li:hover {
	background-color: #f7f7f7;

	cursor: pointer;
}

ul.services_list li:last-child {
	margin-right: 0px;
}

.vlightbox1 {
	margin: 9px !important;
	clear: both;
}

.vlightbox1_img {
	padding-bottom: 9px;
}

.heading-7 {
	font-size: 36px;
	color: #404040;
	font-weight: 700;
	text-align: center;
}

/* 

Generic Styling, for Desktops/Laptops 

*/

table {
	width: 100%;

	border-collapse: collapse;
}

/* Zebra striping */

tr:nth-of-type(odd) {
	background: #fff;
}

th {
	background: #333;

	color: white;

	font-weight: bold;
}

td,
th {
	padding: 6px !important;

	border: 1px solid #eaeaea;

	text-align: left;
}

.clients-skill {
	width: 727px;

	height: auto;

	float: left;
}

ul.clients-skill2 {
	width: 727px;

	height: auto;

	float: left;

	list-style-type: none;
}

ul.clients-skill2 li {
	width: 160px;

	height: auto;

	float: left;

	list-style-type: none;

	margin-right: 10px;

	border: 1px solid #eaeaea;

	border-radius: 4px;

	padding: 6px;
}

ul.clients-skill2 li:last-child {
	margin-right: 0px;
}

.leadership-extra {
	width: 285px;

	height: auto;

	margin: 0 auto;

	float: none;
}

.content_pad2 {
	padding: 5px 0px 15px 12px;
}

.clients_cont2 {
	width: 585px;

	height: auto;

	float: left;

	font-size: 16px;

	text-align: justify;

	line-height: 22px;
}

.clients_cont2 .clients_cont_heading {
	font-size: 17px;

	color: #404040;

	margin-bottom: 5px;

	font-weight: 700;
}

.icon_career1 {
	padding-top: 12px;

	text-align: center;

	font-size: 20px;

	color: #ffffff;
}

.apply-now {
	padding: 10px 18px;

	outline: none;

	background-color: #a3a3a3;

	color: #ffffff;

	border: none;

	cursor: pointer;

	font-size: 16px;

	font-weight: 700;

	text-transform: uppercase;

	-webkit-border-radius: 4px;

	-moz-border-radius: 4px;

	border-radius: 4px;
}

.apply-now:hover {
	background-color: #218ecd;

	color: #fff;
}

.apply-now-area {
	width: 725px;

	height: auto;

	float: left;

	-webkit-border-radius: 4px;

	-moz-border-radius: 4px;

	border-radius: 4px;

	border: 1px solid #e8e7e8;

	margin: 20px 0px;
}

ul.apply2 {
	width: 725px;

	height: auto;

	float: left;

	list-style-type: none;
}

ul.apply2 li {
	width: 338px;

	height: auto;

	float: left;

	margin: 7px 10px;

	list-style-type: none;
}

ul.apply2 li.dropdown_full {
	width: 100% !important;

	height: auto;

	float: left;

	margin: 7px 10px;

	list-style-type: none;
}

.generalDropDown1 {
	background: none repeat scroll 0 0 #fff;

	border: 1px solid #dadada;

	border-radius: 4px;

	color: #62717a;

	font-family: Arial, Helvetica, sans-serif;

	font-size: 14px;

	font-weight: normal;

	height: 40px !important;

	padding: 3px;

	vertical-align: middle;

	max-width: 300px;

	font-style: italic;
}

.generalTextBox1 {
	background: none repeat scroll 0 0 #fff;

	border: 1px solid #dadada;

	border-radius: 4px;

	color: #62717a;

	font-family: Arial, Helvetica, sans-serif;

	font-size: 14px;

	font-weight: normal;

	height: 32px;

	padding: 3px;

	vertical-align: middle;

	width: 100%;

	font-style: italic;
}

.blue-bg {
	background-color: #218ecd;

	margin: 7px 10px;

	color: #fff;

	font-size: 14px;

	padding: 10px;

	text-align: center;
}

.request-quote {
	float: left;

	width: 341px;

	height: auto;
}

.request-quote h1 {
	color: #151515;

	font-size: 22px;

	font-weight: normal;
}

ul.request-quote {
	max-width: 300px;

	height: auto;

	float: left;

	list-style-type: none;
}

ul.request-quote li {
	width: 300px;

	height: auto;

	float: left;

	margin: 5px 0px;

	list-style-type: none;
}

ul.request-quote li.dropdown_full {
	width: 100% !important;

	height: auto;

	float: left;

	margin: 7px 10px;

	list-style-type: none;
}

.blue-bg1 {
	background-color: #218ecd;

	margin: 7px 0px;

	color: #fff;

	font-size: 14px;

	line-height: 22px;

	padding: 10px;

	text-align: center;
}

.g-advisory {
	width: 150px;

	height: 150px;

	float: left;

	background-color: #000;

	margin-right: 15px;

	margin-top: 7px;

	border-radius: 4px;

	-moz-border-radius: 4px;

	-webkit-border-radius: 4px;

	-ms-border-radius: 4px;

	overflow: hidden;
}

.g-advisory-content {
	width: 562px;

	height: auto;

	float: left;
}
/*education section*/

.our_client_right_section2 {
	float: left;
	width: 570px;
	background-color: #fff;
	margin: 50px 0px;
}

/*services section*/

.our_client_right_section {
	float: left;

	width: 1140px;

	background-color: #fff;

	margin: 50px 0px;
}
.system_integration_section {
	float: left;

	width: 1140px;

	background-color: #fff;

	margin: 1px 0px;
}
.our_client_logo {
	border-right: 1px solid #e1e1e1;
	float: left;
	width: 254px;
	min-height: 200px;
	padding: 25px 15px;
	text-align: center;
	vertical-align: middle;
	font-size: 14px;
	color: #686868;
	line-height: 20px;
}

.our_client_logo .heading {
	font-size: 17px;
	color: #494949;
	font-weight: 700;
	text-align: center;
	margin-bottom: 7px;
}

.our_client_logo1 {
	float: left;
	width: 210px;
	height: 200px;
	padding: 25px 15px;
	text-align: center;
	vertical-align: middle;
	font-size: 14px;
	color: #686868;
	line-height: auto;
}
.our_client_logo1 p {
	text-align: center;
}

.our_client_logo1:hover {
	background-color: #f1f1f1;
	cursor: pointer;
}

.our_client_logo2 {
	float: left;
	width: 210px;
	height: 250px;
	padding: 25px 15px;
	text-align: center;
	vertical-align: middle;
	font-size: 14px;
	color: #686868;
	line-height: auto;
}
.our_client_logo2 p {
	text-align: center;
}

.our_client_logo2:hover {
	background-color: #f1f1f1;
	cursor: pointer;
	height: 250px;
}

.our_client_logo3 {
	float: left;
	width: 210px;
	height: 280px;
	padding: 25px 15px;
	text-align: center;
	vertical-align: middle;
	font-size: 14px;
	color: #686868;
	line-height: auto;
}
.our_client_logo3 p {
	text-align: center;
}

.our_client_logo3:hover {
	background-color: #f1f1f1;
	cursor: pointer;
	height: 280px;
}

.our_client_logo4 {
	float: left;
	width: 250px;
	height: 270px;
	padding: 25px;
	text-align: center;
	vertical-align: middle;
	font-size: 14px;
	color: #686868;
	line-height: auto;
	border: 1px solid #f1f2f2;
	margin: 25px;
}
.our_client_logo4 p {
	text-align: center;
}

.our_client_logo4:hover {
	background-color: #fff;
	cursor: pointer;
	height: 270px;
	transform: scale(1.2, 1.2);
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
	border: 1px solid #1e93d7;
}

/* case study */

.casestudy-osfdc {
	float: left;
	width: 205px;
	height: 450px;
	vertical-align: middle;
	line-height: auto;
	margin: 0px 10px;
	background-color: #3f4020;
}
.case-study-cus p {
	margin-bottom: 0px !important;
}
.casestudy-rwss {
	float: left;
	width: 205px;
	height: 450px;
	vertical-align: middle;
	line-height: auto;
	margin: 0px 10px;
	background-color: #0f4c88;
}

.casestudy-sanjoghelpline {
	float: left;
	width: 205px;
	height: 450px;
	vertical-align: middle;
	line-height: auto;
	margin: 0px 10px;
	background-color: #553f39;
}

.dhe {
	float: left;
	width: 205px;
	height: 450px;
	vertical-align: middle;
	line-height: auto;
	margin: 0px 10px;
	background-color: #6c3e10;
}

.psp {
	float: left;
	width: 205px;
	height: 450px;
	vertical-align: middle;
	line-height: auto;
	margin: 0px 10px;
	background-color: #91814d;
}
.fard {
	float: left;
	width: 205px;
	height: 450px;
	vertical-align: middle;
	line-height: auto;
	margin: 0px 10px;
	background-color: #659f48;
}

.basudha-helpline {
	float: left;
	width: 205px;
	height: 450px;
	vertical-align: middle;
	line-height: auto;
	margin: 0px 10px;
	background-color: #025b83;
}

.casestudy-ramboll {
	float: left;
	width: 205px !important;
	height: 450px;
	vertical-align: middle;
	line-height: auto;
	margin: 0px 10px;
	background-color: #162b49;
}

.casestudy-e-procurement {
	float: left;
	width: 205px !important;
	height: 450px;
	vertical-align: middle;
	line-height: auto;
	margin: 0px 10px;
	background-color: #0e3345;
}

.casestudy-medisponsor {
	float: left;
	width: 205px !important;
	height: 450px;
	vertical-align: middle;
	line-height: auto;
	margin: 0px 10px;
	background-color: #023665;
}

.case-study-tms {
	float: left;
	width: 205px !important;
	height: 450px;
	vertical-align: middle;
	line-height: auto;
	margin: 0px 10px;
	background-color: #93795f;
}

.case-study-hrms {
	float: left;
	width: 205px !important;
	height: 450px;
	vertical-align: middle;
	line-height: auto;
	margin: 0px 10px;
	background-color: #07333e;
}

.case-study-cms {
	float: left;
	width: 205px !important;
	height: 450px;
	vertical-align: middle;
	line-height: auto;
	margin: 0px 10px;
	background-color: #642224;
}

.casestudy-oswas {
	float: left;
	width: 205px !important;
	height: 450px;
	vertical-align: middle;
	line-height: auto;
	margin: 0px 10px;
	background-color: #202d3d;
}

.casestudy-dlm {
	float: left;
	width: 205px !important;
	height: 450px;
	vertical-align: middle;
	line-height: auto;
	margin: 0px 10px;
	background-color: #5f1000;
}

.casestudy-truemd {
	float: left;
	width: 205px !important;
	height: 450px;
	vertical-align: middle;
	line-height: auto;
	margin: 0px 10px;
	background-color: #65499d;
}

.casestudy-employee-tracker {
	float: left;
	width: 205px !important;
	height: 450px;
	vertical-align: middle;
	line-height: auto;
	margin: 0px 10px;
	background-color: #006caa;
}

.casestudy-heading {
	padding: 15px 15px 0px 15px;
	font-size: 14px;
	font-weight: bold;
	color: #ffffff;
	height: fit-content;
}
.hr-line {
	padding: 0px;
	margin: 0 15px;
}

.hr-line2 {
	padding: 0px;
	margin: 0 5px;
}

.casestudy-text {
	padding: 15px 15px 5px 15px;
	font-size: 12px;
	color: #ffffff;
	line-height: 17px;
}

/* case study */

.mobile-apps-columns {
	float: left;
	width: 205px;
	height: 400px;
	vertical-align: middle;
	line-height: auto;
	margin: 0px 15px;
}

.arrow-right1 {
	border-top: 19px solid transparent;

	border-bottom: 19px solid transparent;

	border-left: 20px solid #008a8c;

	position: relative;

	z-index: 10;

	left: 403px;

	top: 90px;

	width: 100px;

	height: auto;

	margin-top: -40px;
}

.our_client_logo:hover {
	background-color: #f1f1f1;
	cursor: pointer;
}

.our_client_logo_separator {
	border-top: 1px solid #e1e1e1;

	clear: both;
}

.our_client_logo_separator:last-child {
	display: none;
}

.our_client_logo.border1 {
	border-right: none;
}

/*services section*/

/*testimonial css start*/

.col6 {
	width: 540px;

	margin: 20px 15px;

	float: left;

	border-radius: 6px;
}

.quote-box {
	border: 1px solid #e5e5e5;

	padding: 30px;

	border-radius: 6px;

	min-height: 100px;

	background-color: #fff;

	height: auto;
}

.quote-box .quote-left {
	position: absolute;

	margin: -12px 0 0 -10px;

	background: url(../images/qleft.png) no-repeat;

	width: 33px;

	height: 28px;
}

.quote-box q {
	display: block;

	font-size: 16px;

	line-height: 24px;

	margin: 0 0 0 30px;

	color: #404040;
}

.quote-box .quote-right {
	position: absolute;

	margin: 4px -10px 0 10px;

	background: url(../images/qright.png) no-repeat;

	width: 33px;

	height: 28px;
}

.quote-arrow {
	display: block;

	margin: -1px 0 0 39px;

	background: url(../images/qarrow.png) no-repeat;

	width: 99px;

	height: 13px;
}

cite {
	display: block;

	margin: 20px 0 0 20px;
}

#pg-home cite .photo.tina {
	background-position: -705px -191px;
}

cite .photo {
	float: left;

	display: block;

	margin: 0 15px 0 0;

	width: 70px;

	height: 70px;

	background-color: #fff;

	border-radius: 50%;

	overflow: hidden;

	border: 2px solid #e6e6e6;
}

cite .author {
	color: #479ccf;

	font-size: 22px;

	line-height: 40px;
}

.inner_courses3 {
	width: 1100px;

	height: auto;

	float: left;
}

/*testimonial css start*/

.blue {
	color: #1e8bcb;
}

.slider_caption {
	position: absolute;

	bottom: 0px;

	left: 0px;

	width: 100%;

	height: 40px;

	z-index: 0;

	font-size: 16px;

	line-height: 40px;

	background-color: rgba(0, 0, 0, 0.8);

	color: #fff;

	text-align: center;
}

.jssorb05 {
	position: absolute;
}

.jssorb05 div,
.jssorb05 div:hover,
.jssorb05 .av {
	position: absolute;

	width: 16px;

	height: 16px;

	background: url(../images/b05.png) no-repeat;

	overflow: hidden;

	cursor: pointer;

	display: none;
}

.jssorb05 div {
	background-position: -7px -7px;
}

.jssorb05 div:hover,
.jssorb05 .av:hover {
	background-position: -37px -7px;
}

.jssorb05 .av {
	background-position: -67px -7px;
}

.jssorb05 .dn,
.jssorb05 .dn:hover {
	background-position: -97px -7px;
}

.jssora12l,
.jssora12r {
	display: block;

	position: absolute;

	width: 30px;

	height: 46px;

	cursor: pointer;

	background: url('../images/a12.png') no-repeat;

	overflow: hidden;
}

.jssora12l {
	background-position: -16px -37px;
}

.jssora12r {
	background-position: -75px -37px;
}

.jssora12l:hover {
	background-position: -136px -37px;
}

.jssora12r:hover {
	background-position: -195px -37px;
}

.jssora12l.jssora12ldn {
	background-position: -256px -37px;
}

.jssora12r.jssora12rdn {
	background-position: -315px -37px;
}

.jssora12l.jssora12lds {
	background-position: -16px -37px;

	opacity: 0.3;

	pointer-events: none;
}

.jssora12r.jssora12rds {
	background-position: -75px -37px;

	opacity: 0.3;

	pointer-events: none;
}

/*System integration*/
.system_integration_logo {
	border-right: 1px solid #e1e1e1;
	float: left;
	width: 344px;
	height: auto;
	padding: 25px 15px;
	text-align: center;
	vertical-align: middle;
	font-size: 14px;
	color: #686868;
	line-height: 20px;
}

.system_integration_logo .heading {
	font-size: 17px;

	color: #494949;

	font-weight: 700;

	text-align: center;

	margin-bottom: 7px;
}
.system_integration_logo:hover {
	background-color: #f1f1f1;

	cursor: pointer;
}

.system_integration_logo_separator {
	border-top: 1px solid #e1e1e1;

	clear: both;
}

.system_integration_logo_separator:last-child {
	display: none;
}

.system_integration_logo.border1 {
	border-right: none;
}

.rightimage {
	width: 270px;
	height: 270px;
	float: right;
}

.rightimage2 {
	float: right;
}

.rightimage-virtualization-services {
	width: 335px;
	height: 270px;
	float: right;
	padding: 5px 0 5px 10px;
}

/* ==================================== */

.art-infrastructure {
	/* border-right: 1px solid #e1e1e1; */
	float: left;
	width: 344px;
	height: 500px;
	padding: 25px 15px;
	text-align: center;
	vertical-align: middle;
	font-size: 14px;
	color: #686868;
	line-height: 20px;
}

.art-infrastructure .heading {
	font-size: 17px;
	color: #494949;
	font-weight: 700;
	text-align: center;
	margin-bottom: 7px;
}

.art-infrastructure:hover {
	/* border: 1px solid #494949;
	border-radius: 10px;	 */
	background-color: #f1f1f1;
	cursor: pointer;
}

/* .art-infrastructure_separator {
	border-top: 1px solid #e1e1e1;
	clear: both;
} */

.art-infrastructure_separator:last-child {
	display: none;
}

/* .art-infrastructure.border1 {
	border-right:none;
} */

.sap-services {
	font-weight: bold;
	color: #218ecd;
	font-size: 24px;
}

.salesforce-heading {
	font-size: 24px;
	font-weight: bold;
	color: #218ecd;
}

.staffing-heading {
	font-size: 24px;
	font-weight: bold;
	color: #218ecd;
}

.tatwa-centers-left {
	float: left;
	width: 49%;
	vertical-align: middle;
	line-height: auto;
	margin: 0px 7px 0px 0px;
}

.tatwa-centers-right {
	float: left;
	width: 49%;
	vertical-align: middle;
	line-height: auto;
	margin: 0px 0px 0px 7px;
}

/* ======================================================== */

p.card-text {
	padding: 10px 0px;
	text-align: left;
	font-size: 12px;
}
.text-muted {
	color: #d6ffff;
}
.btn.btn-primary2 {
	background-color: #1959a2 !important;
}
.cards:hover {
	transition: 0.5s ease;
	transform: scale(1.05);
	/* box-shadow: 0px 2px 40px var(--clr-gray-light); */
}

/* .img.card-img-top:hover {
	transition: .5s ease;
  }
   */
.blog-inner_body_right {
	width: 338px;
	height: auto;
	float: right;
	margin-left: 40px;
	background-color: #f6f6f6;
	border: 1px solid #f6f6f6;
	padding: 10px;
	box-shadow: -6px 0px 10px #ccc;
}

.blog-inner_body_right h1 {
	width: 328px;
	height: auto;
	float: left;
	font-size: 18px;
	text-transform: uppercase;
	color: #fff;
	text-align: left;
	padding: 14px 5px;
	background-color: #404040;
	margin-top: 0px !important;
	margin-bottom: 0px !important;
}

ul.product-listing2 li.active {
	color: #fff;
	cursor: pointer;
}

ul.product-listing2 {
	width: 340px;
	height: auto;
	float: left;
	list-style-type: none;
	margin: 0;
	padding: 0;
}

ul.product-listing2 li {
	width: 328px;
	height: auto;
	float: left;
	list-style-type: none;
	font-size: 15px;
	color: #737373;
	font-family: Arial, Helvetica, sans-serif;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
	text-align: left;
	border-bottom: 1px solid #e0e0e0;
	padding: 10px 5px;
}

ul.product-listing2 li:last-child {
	border-bottom: 0px solid #e7e7e7;
}

ul.product-listing2 li.active {
	background-color: #f8f8f8;
	color: #737373;
	cursor: pointer;
}

ul.product-listing2 li:hover {
	background-color: #f8f8f8;
	color: #737373;
	cursor: pointer;
}

.inner_body_right_grey2 {
	width: 338px;
	height: auto;
	float: right;
	margin-left: 40px;
	border: 1px solid #f6f6f6;
	padding: 10px;
	background-color: #ffffff;
	margin-top: 68px;
}

.inner_body_right_grey2 h1 {
	font-weight: bold;
	width: 328px;
	height: auto;
	float: left;
	font-size: 18px;
	text-transform: uppercase;
	color: #737373;
	text-align: left;
	padding: 14px 5px;
	background-color: #ffffff;
	margin-top: 0px !important;
	margin-bottom: 0px !important;
}
.inner_body2 {
	width: 100%;
	height: auto;
	float: left;
	position: relative;
	z-index: 10;
	margin-top: 32px;
}

.btn-primary {
	text-decoration: none !important;
	color: white !important;
}

.btn-primary2 {
	text-decoration: none !important;
	color: white !important;
	background-color: #218ecd;
	border-color: #218ecd;
}

/* img.card-img-top {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  z-index: -1;
} */

h3.card-title {
	font-size: 20px;
}

/* ==================================================================================================================================== */

h1.card-title {
	font-size: 30px;
}

.blog-innerpage_area {
	width: 100%;
	height: auto;
	float: left;
	margin-top: 125px;
	background-color: #ffff;
}

.inner_body_left13 {
	color: white;
}

.blog-card-body {
	padding: 45px;
	margin-top: -153px;
	margin-right: 117px;
	background-color: #198dc9;
	height: 253px;
}

/*
 CSS for the main interaction
*/
.tabset > input[type='radio'] {
	position: absolute;
	left: -200vw;
}

.tabset .tab-panel {
	display: none;
}

.tabset > input:first-child:checked ~ .tab-panels > .tab-panel:first-child,
.tabset > input:nth-child(3):checked ~ .tab-panels > .tab-panel:nth-child(2),
.tabset > input:nth-child(5):checked ~ .tab-panels > .tab-panel:nth-child(3),
.tabset > input:nth-child(7):checked ~ .tab-panels > .tab-panel:nth-child(4),
.tabset > input:nth-child(9):checked ~ .tab-panels > .tab-panel:nth-child(5),
.tabset > input:nth-child(11):checked ~ .tab-panels > .tab-panel:nth-child(6) {
	display: block;
}

/*
   Styling
  */
body {
	color: #333;
}

.tabset > label {
	position: relative;
	display: inline-block;
	padding: 15px 15px 25px;
	border: 1px solid transparent;
	border-bottom: 0;
	cursor: pointer;
	font-weight: 600;
}

.tabset > label::after {
	content: '';
	position: absolute;
	left: 15px;
	bottom: 10px;
	width: 22px;
	height: 4px;
	background: #8d8d8d;
}

.tabset > label:hover,
.tabset > input:focus + label {
	color: #06c;
}

.tabset > label:hover::after,
.tabset > input:focus + label::after,
.tabset > input:checked + label::after {
	background: #06c;
}

.tabset > input:checked + label {
	margin-bottom: -1px;
}

.tab-panel {
	padding: 30px 0;
	border-top: 1px solid #ccc;
}

*,
*:before,
*:after {
	box-sizing: border-box;
}

.tabset {
	max-width: 65em;
}

/* =================================================== */

/* .dropdowns {
  float: left;
  overflow: hidden;
  padding-left: 10px;
} */

/* .dropdowns .dropbtn {
  cursor: pointer;
  font-size: 16px;
  border: none;
  outline: none;
  color: #22549e;
  padding: 14px 16px;
  background-color: inherit;
  font-family: inherit;
  margin: 0;
} */
.card-display-flex.abc {
	margin: 10px 10px 10px 10px;
}
.card-display-flex {
	display: flex;
	margin: 10px 11px 40px 11px;
}
.card.margin-mm {
	margin: 2px 21px 0px 10px;
}

.dropdowns-content {
	display: none;
	position: absolute;
	/* background-color: #f9f9f9; */
	min-width: 160px;
	box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
	z-index: 1;
}

.dropdowns-content a {
	float: none;
	color: black;
	padding: 12px 16px;
	text-decoration: none;
	display: block;
	text-align: left;
}

.dropdowns-content a:hover {
	background-color: #ddd;
}

.show {
	display: block;
}

.heading13 {
	font-size: 28px;
	color: #22549e;
}

select.dropbtn.paddd1 {
	padding: 5px;
	width: 115px;
}
select.dropbtn.paddd1 option {
	color: #342e39;
}

select.dropbtn.paddd2 {
	padding: 5px;
	width: 133px;
}
select.dropbtn.paddd2 option {
	color: #342e39;
}

select.dropbtn.paddd3 {
	padding: 5px;
	width: 137px;
}
select.dropbtn.paddd3 option {
	color: #342e39;
}

.slider-bannercontent1 {
	/* display: none; */
	position: absolute;
	/* background-color: #342e39; */
	/* opacity: 0.6; */
	min-width: 150px;
	/* box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2); */
	/* z-index: 999; */
	color: white;
	padding: 50px 50px 50px 120px;
	/* width: 50%; */
	margin-top: -216px;
}

/* ======================================================== */

/* slider */

.carousel-inner > .item > img,
.carousel-inner > .item > a > img {
	width: 100%;
	margin: auto;
	/* height: 750px !important; */
}

.carousel-indicators {
	bottom: 20px;
	padding-left: 50% !important;
}

.blog-header {
	font-size: 20px;
	color: #fff;
	line-height: 24px;
}

.blog-digital-signature {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/blog-banner11.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.blog-medical-data {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/blog-banner12.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.blog-web-application-security-audit {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/blog-web-application-security-audit.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.blog-data-analytics-healthcare {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/banner-data-analytics-healthcare-bfsi.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.blog-business-process {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/bpaas-banner.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.blog-cyber-security-best-practices {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/Cybersecurity-inner-pg-banner.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.blog-customer-life-cycle {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/customer-life-cycle-inner-page-banner.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.blog-it-solutions-roi {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images/roi-banner.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.blog-tatwa-loyality {
	width: 100%;
	height: 217px;
	float: left;
	background-image: url(../images//loyality.jpg);
	background-repeat: no-repeat;
	background-size: cover;
}

.hairline {
	margin-top: 0px !important;
	margin-bottom: 20px;
	border: 0;
	border-top: 1px solid #2b75bd;
	opacity: 0.5;
}

.navspace {
	margin-bottom: 0px !important;
}

.mask15 {
	height: 15px;
}

.mask25 {
	height: 25px;
}

.mask30 {
	height: 30px;
}

.digital-signature-img {
	float: left;
	width: 100%;
	border: 1px solid#e6e7e8;
}

.digital-signature-img2 {
	float: left;
	width: 100%;
}

.digital-signature-heading {
	font-weight: bold;
	font-size: 20px;
}

.blogchain-heading {
	font-weight: bold;
	font-size: 20px;
}

.blogchain-img {
	float: left;
	width: 100%;
	border: 1px solid#e6e7e8;
}

.medical-data-heading {
	font-weight: bold;
	font-size: 20px;
}

.medical-data-img {
	float: left;
	width: 100%;
	border: 1px solid#e6e7e8;
}

.web-application-heading {
	font-weight: bold;
	font-size: 20px;
}

.data-analytics-heading {
	font-weight: bold;
	font-size: 20px;
}

.digital-signature-2col {
	width: 40%;
	float: left;
	padding: 0 5%;
}

.maxrowwidth {
	max-width: 900px;
}

.container-fluid2 {
	padding-right: 15px;
	margin-right: auto;
	margin-left: auto;
	width: 100%;
}

.container-fluid3 {
	margin-right: auto;
	margin-left: auto;
	width: 100%;
}

ul.quality_policy6 {
	width: 100%;
	height: auto;
	float: left;
	list-style-type: none;
	margin-bottom: 20px;
}

ul.quality_policy6 li {
	width: 100%;
	height: auto;
	float: left;
	background-image: url(../images/bullet.png);
	background-repeat: no-repeat;
	background-position: left 9px;
	padding-left: 25px;
	line-height: 26px;
	display: inline-block;
	/* max-width: 200px; */
}

.margin-top {
	margin-top: 20px;
}
/* 
.row.maxrowwidth {
  width: 100%;
} */

.heading-8 {
	color: #218ecd;
	font-weight: bold;
}

/* UCPASS SECTION */
.custom_banner {
	width: 100%;
	float: left;
	background-color: #0c4da2;
	background-repeat: no-repeat;
	background-size: cover;
	padding: 50px;
	color: #ffffff;
	border-radius: 20px;
}

.my-form {
	color: #305896;
}
.my-form .btn-default {
	background-color: #0c4da2;
	color: #fff;
	border-radius: 0;
}
.my-form .btn-default:hover {
	background-color: #4498c6;
	color: #fff;
}
.my-form .form-control {
	border-radius: 0;
}

.ucpaas-heading {
	font-weight: bold;
	font-size: 44px;
}

.ucpaas-heading2 {
	text-align: left;
	font-weight: bold;
	font-size: 34px;
	width: 50%;
}

.ucpaas-heading3 {
	text-align: center;
	font-weight: bold;
	font-size: 34px;
}
.ucpaas-heading4 {
	text-align: left;
	font-weight: bold;
	font-size: 32px;
}
.ucpaas-heading5 {
	text-align: bold;
	font-weight: bold;
	font-size: 34px;
	color: white;
	padding-top: 10%;
}

.communication-channels-icons {
	width: 50px;
	height: 50px;
}

.why-ucpaas-colright {
	padding-top: 80px;
}

.trusted-telecom {
	text-align: center;
	padding-top: 30px;
}

.trusted-telecom-connectivity {
	font-weight: bold;
	font-size: 24px;
	color: #0c4da2;
}

.ucpass-testimonials {
	text-align: center;
	padding: 20px;
	border: 1px solid #e6e7e8;
}

.ucpass-testimonials-toppadding {
	padding-top: 20px;
}

.name-surname {
	font-weight: bold;
	color: #0c4da2;
}

.designation {
	color: #939598;
}
.ucpass-testimonials-clients {
	width: 30%;
}
.ucpaas-img {
	width: 80%;
	transition: 1s ease;
}

.ucpaas-img:hover {
	-webkit-transform: scale(1.2);
	-ms-transform: scale(1.2);
	transform: scale(1.2);
	transition: 1s ease;
}

.world-class-customer-experience-img1 {
	width: 100%;
	transition: 1s ease;
}

.world-class-customer-experience-img1:hover {
	-webkit-transform: scale(1.2);
	-ms-transform: scale(1.2);
	transform: scale(1.2);
	transition: 1s ease;
}

.sign-up-free-demo-account {
	width: 80%;
	transition: 1s ease;
}

.sign-up-free-demo-account:hover {
	-webkit-transform: scale(1.2);
	-ms-transform: scale(1.2);
	transform: scale(1.2);
	transition: 1s ease;
}

.sign-up-section {
	background: #1e7eec;
	box-shadow: 10px 10px 10px rgba(0, 0, 0, 0.03);
	border-radius: 15px;
	padding: 5%;
	color: white;
	text-align: center;
	width: auto;
}

.view_more2 {
	width: 120px;
	height: auto;
	background: #1e93d7;
	border-radius: 6px;
	-moz-border-radius: 6px;
	-webkit-border-radius: 6px;
	-ms-border-radius: 6px;
	text-align: center;
	padding: 7px 15px;
	font-size: 14px;
	font-weight: 700;
	color: #fff;
	margin-left: auto;
	margin-right: auto;
}

.view_more2:hover {
	background: #014574;
	color: #fff;
	cursor: pointer;
}

.signup-try-to-free {
	width: 125px;
	height: auto;
	background: #ffffff;
	border-radius: 6px;
	-moz-border-radius: 6px;
	-webkit-border-radius: 6px;
	-ms-border-radius: 6px;
	text-align: center;
	padding: 7px 15px;
	font-size: 14px;
	font-weight: 700;
	color: #1e7eec;
	margin-left: auto;
	margin-right: auto;
}

.signup-try-to-free:hover {
	background: #d7eeff;
	color: #014574;
	cursor: pointer;
}

.multiple-customer-padding {
	padding-top: 30px;
	width: 100%;
}

.ucpass-section-padding {
	padding-top: 50px;
	width: 100%;
}

/* =============================== */
.inner_body3 {
	width: 100%;
	height: auto;
	float: left;
	position: relative;
	z-index: 10;
	margin-top: 32px;
	background-color: #f8f9fd;
}

.innerpage_area-ucpaas {
	width: 100%;
	height: auto;
	float: left;
	background-color: #f8f9fd;
}

.ucpass-section-padding2 {
	padding: 5% 10%;
	width: 100%;
	background-color: #ffffff;
	border-radius: 15px;
	box-shadow: 10px 10px 10px rgba(0, 0, 0, 0.03);
}

.view_more3 {
	width: 150px;
	height: auto;
	background: rgba(30, 147, 215, 0.8);
	border-radius: 6px;
	-moz-border-radius: 6px;
	-webkit-border-radius: 6px;
	-ms-border-radius: 6px;
	text-align: center;
	padding: 7px 15px;
	font-size: 14px;
	font-weight: 700;
	color: #fff;
	margin-left: auto;
	margin-right: auto;
}

.view_more3:hover {
	background: #014574;
	color: #fff;
	cursor: pointer;
}

.component-badge {
	background-color: #ffffff;
	border: 1px solid #89bfe1;
	border-radius: 2.5rem;
	color: var(--global-colors-orange);
	font-size: 0.875rem;
	font-weight: 600;
	line-height: 1.5;
	margin-left: 0.75rem;
	padding: 0.188rem 0.625rem;
}

.communication-channels-icons2 {
	width: 100%;
	height: 85px;
	padding: 23px 6px 11px 40px;
	font-size: 14px;
}

.communication-channels-icons2:hover {
	box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
}

.explore21years {
	background-color: #000000;
	color: white;
	padding: 10%;
	border-radius: 15px;
	box-shadow: 10px 10px 10px rgba(0, 0, 0, 0.03);
	width: 100%;
	margin: 0px !important;
}

.vl {
	border-left: 1px solid rgb(136, 136, 136);
	height: 100px;
}

.vl:hover {
	border-left: 1px solid #1e7eec;
	height: 100px;
}

.row.explore21years-width {
	width: 100%;
	color: #7f7f7f;
}

.row.explore21years-width:hover {
	color: #ffffff;
}

/* ===================================== */

.our_product {
	float: left;
	width: 285px;
	min-height: 200px;
	padding: 25px 25px;
	text-align: center;
	vertical-align: middle;
	font-size: 14px;
	color: #686868;
	line-height: 20px;
}

.our_product .heading {
	font-size: 17px;
	color: #494949;
	font-weight: 700;
	text-align: center;
	margin-bottom: 7px;
}

.our_product:hover img {
	/* background-color: #f1f1f1; */
	-webkit-transform: scale(1.1);
	transform: scale(1.1);
	-webkit-transition: all 0.4s ease-in-out;
	transition: all 0.4s ease-in-out;
}

/* .our_product:hover img {
  transform: scale(1.1);
  filter: (100%);
} */

.width50 {
	width: 50%;
	/* border-right: 1px solid #e1e1e1; */
	padding-left: 20px;
	padding: 10px;
	box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 8px;
	border-radius: 6px;
}
.width50right {
	width: 50%;
	border-right: 1px solid #e1e1e1;
	padding: 10px;
	box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 8px;
	border-radius: 6px;
}
.why_tatwa ul {
	padding-left: 0px !important;
}
@media (max-width: 768px) {
	.ban_right1 {
		display: none;
	}
}

img.d-block.w-100.h-100.object-fit-cover {
	padding-top: 58px;
}

.impact-button a {
	margin: 5px;
	display: inline-block;
}

/* Leadership Section Responsive Styles */
.leadership-section {
	padding: 10px 0;
}

.leadership-card {
	transition: all 0.3s ease;
	background: transparent;
	margin-bottom: 20px !important;
}

.leadership-card:hover {
	transform: translateY(-5px);
}

.leadership-img-container {
	width: 180px;
	height: 180px;
	margin: 0 auto 15px;
	border: 3px solid #f8f9fa;
	transition: all 0.3s ease;
}

.leadership-img-container img {
	width: 100%;
	height: 100%;
	object-fit: cover;
	border-radius: 50%;
}

.leadership-card:hover .leadership-img-container {
	border-color: #0d6efd;
}

.leadership-card .card-title {
	color: #333;
	font-weight: 600;
	margin-bottom: 5px;
}

.leadership-card .text-muted {
	font-size: 0.9rem;
	margin-bottom: 10px;
}

/* Desktop (≥1200px) */
@media (min-width: 1200px) {
	.col-md-4 {
		flex: 0 0 25%;
		max-width: 25%;
	}
	.leadership-img-container {
		width: 180px;
		height: 180px;
	}
}

/* Tablet (768px-1199px) */
@media (min-width: 768px) and (max-width: 1199px) {
	.col-md-4 {
		flex: 0 0 33.333333%;
		max-width: 33.333333%;
	}
	.leadership-img-container {
		width: 160px;
		height: 160px;
	}
}

/* Large phones (576px-767px) */
@media (min-width: 576px) and (max-width: 767px) {
	.col-sm-6 {
		flex: 0 0 50%;
		max-width: 50%;
	}
	.leadership-img-container {
		width: 150px;
		height: 150px;
	}
	.leadership-card .card-title {
		font-size: 1.1rem;
	}
	.leadership-card .text-muted {
		font-size: 0.8rem;
	}
}

/* Mobile (<576px) */
@media (max-width: 575px) {
	.col-sm-6 {
		flex: 0 0 100%;
		max-width: 100%;
	}
	.leadership-img-container {
		width: 140px;
		height: 140px;
	}
	.leadership-card {
		margin-bottom: 30px !important;
	}
	.leadership-card .card-title {
		font-size: 1.2rem;
	}
}

/* Container adjustments */
@media (max-width: 1199px) {
	.container {
		padding-right: 15px !important;
		padding-left: 15px !important;
	}
}

/* Row spacing */
.row.justify-content-center {
	margin-bottom: 20px;
}

/* Fix for card body spacing */
.card-body {
	padding: 1rem 0.5rem;
}

/* Modal Section Responsive Styles */
.modal-dialog {
	width: 100%;
	height: 100%;
	padding: 0;
	margin: 0;
}

.modal-content {
	height: 100%;
	border: 1px solid rgba(0, 0, 0, 0.2) !important;
	border-radius: 6px !important;
	color: white;
	overflow: auto;
	box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5) !important;
	background-clip: padding-box !important;
	outline: 0 !important;
}

.modal-header {
	padding: 15px !important;
	border-bottom: 1px solid #e5e5e5 !important;
	display: flex !important;
	align-items: center !important;
	justify-content: space-between !important;
}

.modal-title {
	font-size: 1.5em;
	font-weight: 300;
	margin: 8px;
}

.modal-header .close {
	margin: 0 !important;
	padding: 0 !important;
	background: transparent !important;
	border: 0 !important;
	font-size: 21px !important;
	font-weight: 700 !important;
	opacity: 0.5 !important;
	cursor: pointer !important;
	position: relative !important;
	z-index: 1000 !important;
	color: #000 !important;
}

.modal-header .close:hover {
	opacity: 1 !important;
}

.close {
	float: right !important;
	font-size: 21px !important;
	font-weight: 700 !important;
	line-height: 1 !important;
	color: #000 !important;
	text-shadow: 0 1px 0 #fff !important;
	opacity: 0.5 !important;
}

.close:hover,
.close:focus {
	color: #000 !important;
	text-decoration: none !important;
	cursor: pointer !important;
	opacity: 1 !important;
}

.modal-content-one {
	background-image: url(../images/BG_Services.jpg);
	background-position: center top;
	background-repeat: no-repeat;
	background-size: cover;
	width: 100%;
	height: 100%;
}

.modal-content-two {
	background-color: #484b4d;
}

.modal-content-three {
	background-color: #484b4d;
}

.modal-content-four {
	background-color: #484b4d;
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
	.modal-dialog {
		max-width: 800px;
		margin: 30px auto;
	}
}

/* Medium devices (tablets, 768px to 991px) */
@media (min-width: 768px) and (max-width: 991px) {
	.modal-dialog {
		max-width: 90%;
		margin: 20px auto;
	}

	.modal-content {
		height: auto;
		min-height: 80vh;
	}
}

/* Small devices (landscape phones, 576px to 767px) */
@media (min-width: 576px) and (max-width: 767px) {
	.modal-dialog {
		max-width: 95%;
		margin: 10px auto;
	}

	.modal-content {
		height: auto;
		min-height: 85vh;
	}

	.modal-title {
		font-size: 1.2em;
	}
}

/* Extra small devices (phones, less than 576px) */
@media (max-width: 575px) {
	.modal-dialog {
		margin: 0;
		padding: 10px;
	}

	.modal-content {
		height: auto;
		min-height: 90vh;
	}

	.modal-header {
		padding: 10px !important;
	}

	.modal-title {
		font-size: 1.1em;
		margin: 5px;
	}

	.modal-header .close {
		font-size: 18px !important;
		padding: 5px !important;
	}
}

/* Case Study Styles */

/* Hide right sidebar and make left content full width - ONLY on case studies page */
.case-studies-page .inner_body_right_grey {
	display: none;
}

.case-studies-page .inner_body_left {
	width: 100% !important;
	float: none !important;
	margin-left: 0 !important;
	margin-right: 0 !important;
}

.case-study-container {
	background-color: #f6f6f6;
	padding: 25px;
	margin-bottom: 30px;
	border-radius: 8px;
	border: 1px solid #e8e7e8;
}

.case-study-title {
	color: #218ecd;
	font-size: 22px;
	font-weight: 700;
	margin-bottom: 15px;
}

.case-study-title i {
	margin-right: 10px;
}

.client-profile-box {
	background: #fff;
	padding: 20px;
	border-radius: 8px;
	margin-bottom: 20px;
	border: 1px solid #e8e7e8;
}

.client-profile-title {
	color: #218ecd;
	font-size: 16px;
	font-weight: 700;
	margin-bottom: 10px;
}

.section-heading-challenge {
	color: #218ecd;
	font-size: 18px;
	font-weight: 700;
	margin-bottom: 15px;
	border-bottom: 2px solid #e8e7e8;
	padding-bottom: 8px;
}

.section-heading-solution {
	color: #218ecd;
	font-size: 18px;
	font-weight: 700;
	margin-bottom: 15px;
	border-bottom: 2px solid #e8e7e8;
	padding-bottom: 8px;
}

.section-heading-results {
	color: #218ecd;
	font-size: 18px;
	font-weight: 700;
	margin-bottom: 15px;
	border-bottom: 2px solid #e8e7e8;
	padding-bottom: 8px;
}

.section-heading i {
	margin-right: 8px;
}

.case-study-list {
	margin-left: 20px;
	margin-top: 10px;
}

.results-container {
	display: flex;
	flex-wrap: wrap;
	gap: 15px;
	margin-top: 20px;
}

.result-metric {
	background: #fff;
	border: 2px solid #218ecd;
	border-radius: 8px;
	padding: 20px;
	text-align: center;
	flex: 1;
	min-width: 180px;
}

.metric-value {
	font-size: 28px;
	font-weight: 700;
	color: #218ecd;
	margin-bottom: 8px;
}

.metric-label {
	color: #767676;
	font-size: 14px;
}

.cta-section {
	background: linear-gradient(135deg, #218ecd, #1e93d7);
	color: white;
	padding: 30px;
	border-radius: 8px;
	text-align: center;
	margin-bottom: 30px;
}

.cta-title {
	color: #fff;
	font-size: 24px;
	font-weight: 700;
	margin-bottom: 15px;
}

.cta-text {
	font-size: 16px;
	margin-bottom: 20px;
	opacity: 0.9;
}

.cta-button {
	background: #ffc600;
	color: #191300;
	text-decoration: none;
	padding: 12px 25px;
	border-radius: 6px;
	font-weight: 700;
	display: inline-block;
}

.float-left {
	float: left;
}

.text-justify {
	text-align: justify;
}

.mask-15 {
	height: 15px;
}

.mask-20 {
	height: 20px;
}

.mask-25 {
	height: 25px;
}

.mask-30 {
	height: 30px;
}

.clearfix-10 {
	height: 10px;
}

.clearfix-20 {
	height: 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
	.results-container {
		flex-direction: column;
	}

	.result-metric {
		min-width: auto;
	}

	.case-study-container {
		padding: 15px;
	}
}
