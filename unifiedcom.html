﻿<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-Y36RRN9KDY"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());

        gtag('config', 'G-Y36RRN9KDY');
    </script>

    <head>

        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">

        <meta name="viewport"
            content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1, user-scalable=0">

        <meta name="description"
            content="TATWA Technologies’ BPM and IT Services Unit is Appraised at Maturity Level 3 of CMMI">

        <meta name="keywords" content="Maturity Level 3 of CMMI">

        <meta name="author" content="Tatwa Technologies">

        <link rel="icon" type="image/ico" href="images/tatwa.ico">

        <title>TATWA Technologies - Business Technology Services | Mobility & m Commerce Solutions | Business Process
            Outsourcing</title>

        <!--css area start-->

        <link rel="stylesheet" href="font-awesome/4.3.0/css/font-awesome.min.css">

        <link href="css/style.css" rel="stylesheet" type="text/css">

        <link href="css/responsive.css" rel="stylesheet" type="text/css">

        <link href="css/flexslider1.css" rel="stylesheet" type="text/css">

        <link rel="stylesheet" href="css/base.css" type="text/css">

        <link rel="stylesheet" href="css/index.css" type="text/css">

        <link href="SpryAssets/SpryTabbedPanels.css" rel="stylesheet" type="text/css">

        <!--css area end-->

        <!-- links for new navbar -->

        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/css/bootstrap.min.css" rel="stylesheet">

        <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.1/dist/js/bootstrap.min.js"
            integrity="sha384-Atwg2Pkwv9vp0ygtn1JAojH0nYbwNJLPhwyoVbhoPwBhjQPR5VtM2+xf0Uwh9KtT"
            crossorigin="anonymous"></script>

        <link rel="stylesheet" href="./css/style-for-nav.css">

        <link rel="stylesheet"
            href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

        <!-- links for new navbar -->

        <!--javascripr area start-->

        <script src="ajax/libs/jquery/1.11.1/jquery.min.js"></script>

        <!-- Include all compiled plugins (below), or include individual files as needed -->

        <script src="bootstrap/js/bootstrap.min.js"></script>

        <script type="text/javascript" src="js/jquery-1.7.1.min.js"></script>

        <script type="text/javascript" src="lightbox/jquery.min.js"></script>

        <script language="javascript" type="text/javascript" src="js/jquery.coolfieldset.js"></script>

        <link href="jqueryscripttop.css" rel="stylesheet" type="text/css">

        <script src="js/script.js"></script>

        <script>

            $(function () {

                var pull = $('#pull');

                menu = $('nav ul');

                menuHeight = menu.height();

                $(pull).on('click', function (e) {

                    e.preventDefault();

                    menu.slideToggle();

                });

                $(window).resize(function () {

                    var w = $(window).width();

                    if (w > 320 && menu.is(':hidden')) {

                        menu.removeAttr('style');

                    }

                });

            });

        </script>

        <script type="text/javascript">

            $(document).ready(function () {

                //move he last list item before the first item. The purpose of this is if the user clicks to slide left he will be able to see the last item.

                $('#carousel_ul li:first').before($('#carousel_ul li:last'));

                //when user clicks the image for sliding right        

                $('#right_scroll img').click(function () {

                    //get the width of the items ( i like making the jquery part dynamic, so if you change the width in the css you won't have o change it here too ) '

                    var item_width = $('#carousel_ul li').outerWidth() - 10;

                    //calculae the new left indent of the unordered list

                    var left_indent = parseInt($('#carousel_ul').css('left')) - item_width;

                    //make the sliding effect using jquery's anumate function '

                    $('#carousel_ul:not(:animated)').animate({

                        'left': left_indent

                    }, 400, function () {

                        //get the first list item and put it after the last list item (that's how the infinite effects is made) '

                        $('#carousel_ul li:last').after($('#carousel_ul li:first'));

                        //and get the left indent to the default -210px

                        $('#carousel_ul').css({

                            'left': '0px'

                        });

                    });

                });

                //when user clicks the image for sliding left

                $('#left_scroll img').click(function () {

                    var item_width = $('#carousel_ul li').outerWidth() - 10;

                    /* same as for sliding right except that it's current left indent + the item width (for the sliding right it's - item_width) */

                    var left_indent = parseInt($('#carousel_ul').css('left')) + item_width;

                    $('#carousel_ul:not(:animated)').animate({

                        'left': left_indent

                    }, 400, function () {

                        /* when sliding to left we are moving the last item before the first list item */

                        $('#carousel_ul li:first').before($('#carousel_ul li:last'));

                        /* and again, when we make that change we are setting the left indent of our unordered list to the default -210px */

                        $('#carousel_ul').css({

                            'left': '0px'

                        });

                    });

                });

                setInterval(function () {

                    $("#right_scroll img").trigger("click")

                }, 8000)

            });

            $(document).ready(function () {

                //move he last list item before the first item. The purpose of this is if the user clicks to slide left he will be able to see the last item.

                $('#carousel_ul1 li:first').before($('#carousel_ul1 li:last'));

                //when user clicks the image for sliding right        

                $('#right_scroll1 img').click(function () {

                    //get the width of the items ( i like making the jquery part dynamic, so if you change the width in the css you won't have o change it here too ) '

                    var item_width = $('#carousel_ul1 li').outerWidth() - 10;

                    //calculae the new left indent of the unordered list

                    var left_indent = parseInt($('#carousel_ul1').css('left')) - item_width;

                    //make the sliding effect using jquery's anumate function '

                    $('#carousel_ul1:not(:animated)').animate({

                        'left': left_indent

                    }, 400, function () {

                        //get the first list item and put it after the last list item (that's how the infinite effects is made) '

                        $('#carousel_ul1 li:last').after($('#carousel_ul1 li:first'));

                        //and get the left indent to the default -210px

                        $('#carousel_ul1').css({

                            'left': '0px'

                        });

                    });

                });

                //when user clicks the image for sliding left

                $('#left_scroll1 img').click(function () {

                    var item_width = $('#carousel_ul1 li').outerWidth() - 10;

                    /* same as for sliding right except that it's current left indent + the item width (for the sliding right it's - item_width) */

                    var left_indent = parseInt($('#carousel_ul1').css('left')) + item_width;

                    $('#carousel_ul1:not(:animated)').animate({

                        'left': left_indent

                    }, 400, function () {

                        /* when sliding to left we are moving the last item before the first list item */

                        $('#carousel_ul1 li:first').before($('#carousel_ul1 li:last'));

                        /* and again, when we make that change we are setting the left indent of our unordered list to the default -210px */

                        $('#carousel_ul1').css({

                            'left': '0px'

                        });

                    });

                });

                setInterval(function () {

                    $("#right_scroll1 img").trigger("click")

                }, 8000)

            });

        </script>

        <script>

            $(window).scroll(function () {

                if ($(this).scrollTop() > 1) {

                    $('header').addClass("sticky");

                    $('.logo-quote').css('display', 'none');

                    $('.search').css('display', 'none');

                    $('.logo').css('width', '102px');

                    $('.logo').css('height', '72px');

                    $('.logo').css('padding-top', '12px');

                    $('.header_area_right').css('margin-top', '9px');

                    $('.head-search1').css('margin-top', '-2px');

                } else {

                    $('header').removeClass("sticky");

                    $('.logo-quote').css('display', 'block');

                    $('.search').css('display', 'block');

                    $('.logo').css('width', '155px');

                    $('.logo').css('height', '72px');

                    $('.logo').css('padding-top', '20px');

                    $('.header_area_right').css('margin-top', '20px');

                    $('.head-search1').css('margin-top', '4px');

                }

            });

        </script>

        <!--from java script area start-->

        <script language="javascript">

            $(function () {

                $('#contact').submit(function (e) {

                    e.preventDefault();

                    var form = $(this);

                    var post_data = form.serialize();

                    var post_url = form.attr('action');

                    if (validates()) {

                        $.ajax({

                            type: "POST",

                            url: post_url,

                            data: post_data,

                            success: function (msg) {

                                $('#load')(msg);

                            }

                        });

                        //contact.submit();

                    } else {

                        //$('#load')("Please Fill All The form Detail");

                    }

                });

            });

            ////////

            ///////

            function validates() {

                var name = document.contact.name.value;

                var email = document.contact.email.value;

                var org_name = document.contact.org_name.value;

                var service = document.contact.service.value;

                var error = 0;

                var str = document.contact.name.value;

                var stringSplitArray = str.split(" ");

                var j = 0;

                for (j = 0; j < stringSplitArray.length; j++) {

                    //alert(stringSplitArray[j]);

                    if (!/^[A-Za-z ]{3,20}$/.test(stringSplitArray[ j ])) // name validation

                    {

                        alert("Please Provide your name!");

                        document.contact.name.focus();

                        error = 1;

                        return false;

                    }

                }

                if (!/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,4})+$/.test(email)) // validation for email

                {

                    alert("Please provide your mail Id!");

                    document.contact.email.focus();

                    error = 1;

                    return false;

                }



                if (org_name.replace(/(^[\s]+|[\s]+$)/g, '') == "") // name validation

                {

                    alert("Please provide your company name!");

                    document.contact.org_name.focus();

                    error = 1;

                    return false;

                }

                if (service == 0) {

                    alert("Please choose your service");

                    document.contact.service.focus();

                    error = 1;

                    return false;

                }

                return true;

            }

        </script>

        <!--from java script area end-->

        <style>
            .popup {

                display: none;

                position: absolute;

                z-index: 1;

                background-color: #f9f9f9;

                border: 1px solid #ccc;

                padding: 10px;

                max-width: 400px;

            }

            .component-badge:hover+.popup {

                display: block;

            }

            #first-child {

                top: 0;

                left: 0;

                cursor: pointer;

            }

            #second-child {

                top: 0;

                left: 50%;

                color: black;

            }

            #third-child {

                top: 50%;

                left: 0;

                background-color: crimson;

            }

            #fourth-child {

                top: 50%;

                left: 50%;

                background-color: lightseagreen;

                color: black;

            }

            .big-box {

                position: absolute;

            }

            .big-box h2 {

                text-align: center;

                margin-top: 20%;

                padding: 20px;

                width: 100%;

                font-size: 1.8em;

                letter-spacing: 2px;

                font-weight: 700;

                text-transform: uppercase;

                cursor: pointer;

            }

            @media screen and (max-width: 46.5em) {

                .big-box h2 {

                    font-size: 16px;

                    padding-left: 0px;

                }

            }

            @media screen and (max-width: 20.5em) {

                .big-box h2 {

                    font-size: 12px;

                    padding-left: 0px;

                    margin-top: 30%;

                }

            }

            .modal-dialog {

                width: 100%;

                height: 100%;

                padding: 0;

                margin: 0;

            }

            .modal-content {

                height: 100%;

                border-radius: 0;

                color: white;

                overflow: auto;

            }

            .modal-title {

                font-size: 1.5em;

                font-weight: 300;

                margin: 8px;

            }

            .modal-content-one {

                background-image: url(images/BG_Services.jpg);

                background-position: center top;

                background-repeat: no-repeat;

                background-size: cover;

                width: 100%;

                height: 100%;

            }

            .modal-content-two {

                background-color: #484b4d;

            }

            .modal-content-three {

                background-color: #484b4d;

            }

            .modal-content-four {

                background-color: #484b4d;

            }

            close {

                color: white ! important;

                opacity: 1.0;

            }

            .social-item {

                list-style: none !important;

            }

            .social-icon .fa {

                padding-top: 11px !important;

            }

            .form-group {

                margin-top: 10px;

                margin-bottom: 10px;

            }

            .form-group label {

                color: #0c4da2;

                font-weight: 600;

            }

            .form-group input {

                border-radius: 4px !important;

            }

            .form-group textarea {

                border-radius: 4px;

            }

            .my-form {

                padding: 20px;

                box-shadow: rgba(17, 17, 26, 0.05) 0px 1px 0px, rgba(17, 17, 26, 0.1) 0px 0px 8px;

                border-radius: 4px;

            }
        </style>

    </head>

<body style="background-color:#fff; overflow-x: hidden;">

    <nav class="navbar navbar-expand-lg navbar-light bg-light">

        <div class="container-fluid">

            <a class="navbar-brand" href="index"><img class="img-fluid" src="./images/logo.png" alt=""></a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false"
                aria-label="Toggle navigation">

                <span class="navbar-toggler-icon"></span>

            </button>

            <div class="collapse navbar-collapse" id="navbarSupportedContent">

                <ul class="navbar-nav ms-auto mb-2 mb-lg-0">

                    <li class="nav-item dropdown">

                        <a class="nav-link nav-a-style dropdown-toggle" href="#" id="navbarDropdown" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false">

                            About Us

                        </a>

                        <ul class="dropdown-menu dropdown-for-about" aria-labelledby="navbarDropdown">

                            <div class="row">
                                <div class="dropdown-column">
                                    <li class="list"><a class="dropdown-item" href="overview">Overview</a></li>
                                    <li class="list"><a class="dropdown-item" href="leadership">Leadership</a></li>
                                    <li class="list"><a class="dropdown-item" href="global_advisory">Global
                                            Advisory</a></li>
                                    <li class="list"><a class="dropdown-item" href="accolades">Accolades</a></li>
                                    <li class="list"><a class="dropdown-item" href="infrastructure">Infrastructure</a>
                                    </li>
                                    <li class="list"><a class="dropdown-item" href="privacy_policy">Privacy
                                            Policy</a>
                                    </li>
                                    <li class="list"><a class="dropdown-item" href="quality_policy">Quality
                                            Policy</a></li>
                                    <li class="list"><a class="dropdown-item" href="life-at-tatwa">Life At Tatwa</a>
                                    </li>
                                    <li class="list"><a class="dropdown-item" href="awards">Award</a></li>
                                </div>
                            </div>

                        </ul>

                    </li>

                    <li class="nav-item dropdown">

                        <a class="nav-link nav-a-style dropdown-toggle" href="#" id="navbarDropdown" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false">

                            Services

                        </a>

                        <ul class="dropdown-menu dropdown-for-service" aria-labelledby="navbarDropdown">

                            <div class="row">

                                <div class="col-lg-4 col-md-4 col-sm-12">

                                    <div class="dropdown-column">

                                        <h6>Cx Automation & Operations</h6>

                                        <li>

                                            <hr class="dropdown-divider">

                                        </li>

                                        <li class="list"><a class="dropdown-item" href="inbound-process">Inbound
                                                Process</a></li>

                                        <li class="list"><a class="dropdown-item" href="outbound-process">Outbound
                                                Process</a></li>

                                        <li class="list"><a class="dropdown-item"
                                                href="technical-help-desk-data-support">Technical Help Desk & <br>

                                                Data Support</a>

                                        </li>

                                        <li class="list"><a class="dropdown-item"
                                                href="customer-life-cycle-management">Customer Life Cycle</a></li>

                                        <li class="list"><a class="dropdown-item" href="premium-support-desk">Premium
                                                Support Desk</a></li>

                                        <li class="list"><a class="dropdown-item"
                                                href="customer-acquisition-retention">Customer Acquisition <br> &
                                                Retention</a></li>

                                        <li class="list"><a class="dropdown-item" href="speech-analytics">Speech
                                                Analytics</a></li>

                                        <li class="list"><a class="dropdown-item"
                                                href="contact-center-assessment">Contact Center Assessment</a></li>

                                        <li class="list"><a class="dropdown-item"
                                                href="customer-behavioral-study">Customer Behavioral Study</a></li>

                                        <li class="list"><a class="dropdown-item"
                                                href="staffing-pay-roll-processing">Staffing & Pay Roll <br>
                                                Processing</a></li>

                                        <li class="list"><a class="dropdown-item" href="kpo-data-analytics">KPO &
                                                Data Analysis</a></li>

                                        <li class="list"><a class="dropdown-item" href="scanning-digitization">Scanning
                                                & Digitization</a></li>

                                        <li class="list"><a class="dropdown-item"
                                                href="transaction-processing">Transaction Processing</a></li>

                                        <li class="list"><a class="dropdown-item" href="form-processing">Form
                                                Processing</a></li>

                                        <li class="list"><a class="dropdown-item"
                                                href="survey-and-market-research">Survey & Market Research</a></li>

                                    </div>

                                </div>

                                <div class="col-lg-4 col-md-4 col-sm-12" style="padding-left: 36px;">

                                    <div class="dropdown-column dropdown-column-cus">

                                        <h6>AI, Data Analytics,<br>
                                            Application Development,<br>
                                            Implementation & Maintenance
                                        </h6>

                                        <li>

                                            <hr class="dropdown-divider">

                                        </li>

                                        <li class="list"><a class="dropdown-item" href="digital-transformation">Digital
                                                Transformation</a></li>

                                        <li class="list"><a class="dropdown-item" href="blockchain-services">Blockchain
                                                Services</a></li>

                                        <li class="list"><a class="dropdown-item" href="cyber-security-services">Cyber
                                                Security Services</a></li>

                                        <li class="list"><a class="dropdown-item"
                                                href="custom-enterprise-software-development">Custom Enterprise
                                                Software <br>

                                                Development</a>

                                        </li>

                                        <li class="list"><a class="dropdown-item"
                                                href="quality-control-assurance-services">Quality Assurance</a></li>

                                        <li class="list"><a class="dropdown-item" href="web-design">Web Design &
                                                Development</a></li>

                                        <li class="list"><a class="dropdown-item" href="mobile-apps-development">Mobile
                                                Apps Development</a></li>

                                        <li class="list"><a class="dropdown-item" href="esign-emSigner">eSign &
                                                emSigner Integration</a></li>

                                        <li class="list"><a class="dropdown-item"
                                                href="resource-augmentation-staffing">Resource Augmentation and
                                                Staffing</a></li>

                                        <li class="list"><a class="dropdown-item"
                                                href="artificial-intelligence">Artificial Intelligence (Al)</a></li>

                                        <li class="list"><a class="dropdown-item" href="data-analytics">Data
                                                Analytics</a></li>

                                        <li class="list"><a class="dropdown-item"
                                                href="digital_marketing_services">Digital Marketing Services</a>
                                        </li>

                                    </div>

                                </div>

                                <div class="col-lg-4 col-md-4 col-sm-6" style="padding-left: 36px;">

                                    <div class="dropdown-column dropdown-column-cus bor">

                                        <h6>IT & Cloud <br>Operations</h6>

                                        <li>

                                            <hr class="dropdown-divider">

                                        </li>

                                        <li class="list"><a class="dropdown-item" href="data-center">Data Center</a>
                                        </li>

                                        <li class="list"><a class="dropdown-item" href="cloud-computing">Cloud
                                                Computing</a></li>

                                        <li class="list"><a class="dropdown-item"
                                                href="Virtualization-Services">Virtualization Services</a></li>

                                        <li class="list"><a class="dropdown-item" href="Server-Storage-Solutions">Server
                                                & Storage Solutions</a></li>

                                        <li class="list"><a class="dropdown-item" href="Network-Infrastructure">Network
                                                Infrastructure</a></li>

                                        <li class="list"><a class="dropdown-item"
                                                href="Security-and-Safety-Systems">Security & Safety Systems</a>
                                        </li>

                                        <br>

                                        <h6><a href="">Cx Digital Services</a></h6>
                                        <li>
                                            <hr class="dropdown-divider" />
                                        </li>

                                    </div>

                                </div>

                            </div>

                        </ul>

                    </li>

                    <li class="nav-item">

                        <a class="nav-link nav-a-style" href="products">Products</a>

                    </li>

                    <li class="nav-item">

                        <a class="nav-link nav-a-style" href="partners">Partners</a>

                    </li>

                    <li class="nav-item">

                        <a class="nav-link nav-a-style" href="clients">Clients</a>

                    </li>

                    <li class="nav-item">

                        <a class="nav-link nav-a-style" href="csr">Impact</a>

                    </li>

                    <li class="nav-item">

                        <a class="nav-link nav-a-style" href="careers">Careers</a>

                    </li>

                    <li class="nav-item">

                        <a class="nav-link nav-a-style" href="blog">Blogs</a>

                    </li>

                    <li class="nav-item">

                        <a class="nav-link nav-a-style" href="contacts">Contact</a>

                    </li>

                </ul>

            </div>

        </div>

    </nav>

    <!--wrapper area start-->

    <div class="wrapper">

        <!--header area srart-->

        <!--header area end-->

        <div class="clear"></div>

        <!--inner page area start-->

        <div class="innerpage_area-ucpaas">

            <!--inner ban area start-->

            <div class="ucpass-services">

                <div class="innerdiv">

                    <div class="ban_left1">

                        <!-- <div class="heading-1">Unified Communication Platform <br> as a Service (UCPaaS) </div> -->

                        <img class="tatwa-ucpaas-logo-white" src="images/tatwa-ucpaas-logo-white.svg">

                        <div class="clear"></div>

                        <div style="float:left;"></div>

                    </div>

                    <div class="ban_right1">

                        <div class="break"></div>

                    </div>

                </div>

            </div>

            <!--inner ban area end-->

            <div class="clear"></div>

            <!--body area start-->

            <div class="inner_body3">

                <!--tab menu area start-->

                <div class="innerdiv">

                    <div class="container-fluid3">

                        <div class="row ucpass-section-padding">

                            <div class="col-md-7 ucpass-section-padding">

                                <p style="color: #0c4da2; letter-spacing: 3px;">AUTHENTIC UNIFIED COMMUNICATION
                                    PLATFORM</p>

                                <h1 class="ucpaas-heading">Omnichannel business <br> communications </h1>

                                <div class="mask15"></div>

                                <p>UCPaaS is the API-based platform to engage our clients with personalized
                                    messages, chatbots, programmable voice services, and more.</p>

                                <div class="mask" style="height:35px;"></div>

                                <!-- <div class="view_more" onclick="document.location.href = '#Contact_Us'">Contact Us</div> -->

                            </div>

                            <div class="col-md-5">

                                <div>

                                    <img class="ucpaas-img" src="images/customer.svg" alt="ucpaas-img"
                                        title="ucpaas-img">

                                </div>

                            </div>

                        </div>

                        <div class="clear"></div>

                        <div class="mask" style="height:50px;"></div>

                        <div class="row ucpass-section-padding">

                            <div class="col-md-6">

                                <div>

                                    <img class="world-class-customer-experience-img1"
                                        src="images/world-class-customer-experience-img1.jpg" alt="ucpaas-img"
                                        title="ucpaas-img">

                                </div>

                            </div>

                            <div class="col-md-6 ucpass-section-padding">

                                <h1 class="ucpaas-heading4">World Class Customer Experience for your Customers with
                                    Tatwa UCPaaS Communication Channels</h1>

                                <div class="mask15"></div>

                                <p>UCPaaS is the API-based platform to engage our clients with personalized
                                    messages, chatbots, programmable voice services, and more.</p>

                                <div class="mask" style="height:35px;"></div>

                                <!-- <div class="view_more" onclick="document.location.href = '#Contact_Us'">Contact an expert</div> -->

                            </div>

                        </div>

                        <div class="clear"></div>

                        <div class="mask" style="height:40px;"></div>

                        <div class="row ucpass-section-padding">

                            <div class="col-md-12">

                                <h3 class="ucpaas-heading2">Always Deliver Best in Class Omnichannel Experience on
                                    TATWA UCPaaS Platform</h3>

                                <div>

                                    <div class="clear"></div>

                                    <div class="mask" style="height:25px;"></div>

                                </div>

                                <div class="container">

                                    <div class="row multiple-customer">

                                        <div class="col-sm-4">

                                            <div class="communication-channels-icons2">

                                                <img class="#" src="images/sms.svg" alt=""> &nbsp;&nbsp;&nbsp; <span
                                                    style="font-weight: bold;">SMS</span>&nbsp;&nbsp;&nbsp; <span
                                                    class="component-badge" onmouseover="openPopup(event)"
                                                    onmouseout="closePopup()">API</span>

                                                <div class="popup" onmouseover="cancelClose()"
                                                    onmouseout="closePopup(this)">

                                                    <p>SMS API CPaaS stands for Short Message Service Application
                                                        Programming Interface Communication Platform as a Service.
                                                        It is a technology that allows developers to integrate SMS
                                                        (Short Message Service) functionality into their
                                                        applications or systems using APIs (Application Programming
                                                        Interfaces) provided by CPaaS providers.</p>

                                                    <h6>Key Components and Functionalities of SMS API</h6>

                                                    <li>Messaging Infrastructure</li>

                                                    <li>APIs</li>

                                                    <li>Integration Options</li>

                                                    <li>Scalability and Reliability</li>

                                                    <li>Security</li>

                                                    <li>Analytics and Reporting</li>

                                                </div>

                                                <hr>

                                            </div>

                                        </div>

                                        <div class="col-sm-4">

                                            <div class="communication-channels-icons2">

                                                <img class="#" src="images/mms.svg" alt=""> &nbsp;&nbsp;&nbsp; <span
                                                    style="font-weight: bold;">MMS</span>&nbsp;&nbsp;&nbsp; <span
                                                    class="component-badge" onmouseover="openPopup(event)"
                                                    onmouseout="closePopup()">API</span>

                                                <div class="popup" onmouseover="cancelClose()"
                                                    onmouseout="closePopup(this)">

                                                    <p>MMS API CPaaS, which stands for Multimedia Messaging Service
                                                        Application Programming Interface Communication Platform as
                                                        a Service, allows developers to integrate multimedia
                                                        messaging functionality, such as sending and receiving
                                                        images, videos, and audio files, into their applications or
                                                        systems using APIs provided by CPaaS providers</p>

                                                    <h6>Key Components and Functionalities of MMS API</h6>

                                                    <li>Multimedia Messaging Infrastructure</li>

                                                    <li>APIs</li>

                                                    <li>Integration Options</li>

                                                    <li>Scalability and Reliability</li>

                                                    <li>Security</li>

                                                    <li>Analytics and Reporting</li>

                                                    <li>Customization & Personalization</li>

                                                </div>

                                                <hr>

                                            </div>

                                        </div>

                                        <div class="col-sm-4">

                                            <div class="communication-channels-icons2">

                                                <img class="#" src="images/voice.svg" alt=""> &nbsp;&nbsp;&nbsp;
                                                <span style="font-weight: bold;">Voice</span>&nbsp;&nbsp;&nbsp;
                                                <span class="component-badge" onmouseover="openPopup(event)"
                                                    onmouseout="closePopup()">API</span>

                                                <div class="popup" onmouseover="cancelClose()"
                                                    onmouseout="closePopup(this)">

                                                    <p>Voice API CPaaS (Communication Platform as a Service) enables
                                                        developers to integrate voice communication functionality
                                                        into their applications or systems using APIs provided by
                                                        CPaaS providers</p>

                                                    <h6>Key Components and Functionalities of Voice API</h6>

                                                    <li>Voice Infrastructure</li>

                                                    <li>APIs</li>

                                                    <li>Integration Options</li>

                                                    <li>Scalability and Reliability</li>

                                                    <li>Security</li>

                                                    <li>Analytics and Reporting</li>

                                                    <li>Customization & Personalization</li>

                                                </div>

                                                <hr>

                                            </div>

                                        </div>

                                    </div>

                                </div>

                                <div class="container">

                                    <div class="row multiple-customer">

                                        <div class="col-sm-4">

                                            <div class="communication-channels-icons2">

                                                <img class="#" src="images/email.svg" alt=""> &nbsp;&nbsp;&nbsp;
                                                <span style="font-weight: bold;">Email</span>&nbsp;&nbsp;&nbsp;
                                                <span class="component-badge" onmouseover="openPopup(event)"
                                                    onmouseout="closePopup()">API</span>

                                                <div class="popup" onmouseover="cancelClose()"
                                                    onmouseout="closePopup(this)">

                                                    <p>Email API CPaaS, which stands for Email Application
                                                        Programming Interface Communication Platform as a Service,
                                                        is a technology that enables developers to integrate email
                                                        functionality into their applications or systems using APIs
                                                        provided by CPaaS providers</p>

                                                    <h6>Key Components and Functionalities of Email API</h6>

                                                    <li>Messaging Infrastructure</li>

                                                    <li>APIs</li>

                                                    <li>Integration Options</li>

                                                    <li>Scalability and Reliability</li>

                                                    <li>Security</li>

                                                    <li>Analytics and Reporting</li>

                                                    <li>Customization & Personalization</li>

                                                </div>

                                                <hr>

                                            </div>

                                        </div>

                                        <div class="col-sm-4">

                                            <div class="communication-channels-icons2">

                                                <img class="#" src="images/rcs.svg" alt=""> &nbsp;&nbsp;&nbsp; <span
                                                    style="font-weight: bold;">RCS</span>&nbsp;&nbsp;&nbsp; <span
                                                    class="component-badge" onmouseover="openPopup(event)"
                                                    onmouseout="closePopup()">API</span>

                                                <div class="popup" onmouseover="cancelClose()"
                                                    onmouseout="closePopup(this)">

                                                    <p>RCS API CPaaS, which stands for Rich Communication Services
                                                        Application Programming Interface Communication Platform as
                                                        a Service, allows developers to integrate advanced messaging
                                                        features into their applications or systems using APIs
                                                        provided by CPaaS providers. RCS represents the evolution of
                                                        traditional SMS/MMS messaging by adding interactive and
                                                        multimedia-rich capabilities similar to those found in
                                                        messaging apps like WhatsApp or Facebook Messenger.</p>

                                                    <h6>Key Components and Functionalities of RCS API</h6>

                                                    <li>Rich Messaging Service</li>

                                                    <li>APIs</li>

                                                    <li>Integration Options</li>

                                                    <li>Scalability and Reliability</li>

                                                    <li>Security</li>

                                                    <li>Analytics and Reporting</li>

                                                    <li>Customization & Personalization</li>

                                                </div>

                                                <hr>

                                            </div>

                                        </div>

                                        <div class="col-sm-4">

                                            <div class="communication-channels-icons2">

                                                <img class="#" src="images/live-chat-agent-transfer.svg" alt="">
                                                &nbsp;&nbsp;&nbsp; <span style="font-weight: bold;">Live
                                                    Chat</span>&nbsp;&nbsp;&nbsp; <span class="component-badge"
                                                    onmouseover="openPopup(event)" onmouseout="closePopup()">API</span>

                                                <div class="popup" onmouseover="cancelClose()"
                                                    onmouseout="closePopup(this)">

                                                    <p>LiveChat API CPaaS (Communication Platform as a Service)
                                                        enables developers to integrate live chat functionality into
                                                        their applications or systems using APIs provided by CPaaS
                                                        providers.</p>

                                                    <h6>Key Components and Functionalities of Live Chat API</h6>

                                                    <li>Chat Infrastructure</li>

                                                    <li>APIs</li>

                                                    <li>Integration Options</li>

                                                    <li>Scalability and Reliability</li>

                                                    <li>Security</li>

                                                    <li>Analytics and Reporting</li>

                                                    <li>Customization & Personalization</li>

                                                </div>

                                                <hr>

                                            </div>

                                        </div>

                                    </div>

                                </div>

                                <div class="container">

                                    <div class="row multiple-customer">

                                        <div class="col-sm-4">

                                            <div class="communication-channels-icons2">

                                                <img class="#" src="images/video-streaming.svg" alt="">
                                                &nbsp;&nbsp;&nbsp; <span style="font-weight: bold;">Video
                                                    Streaming</span>&nbsp;&nbsp;&nbsp; <span class="component-badge"
                                                    onmouseover="openPopup(event)" onmouseout="closePopup()">API</span>

                                                <div class="popup" onmouseover="cancelClose()"
                                                    onmouseout="closePopup(this)">

                                                    <p>Video Streaming API CPaaS (Communication Platform as a
                                                        Service) enables developers to integrate video streaming
                                                        functionality into their applications or systems using APIs
                                                        provided by CPaaS providers.</p>

                                                    <h6>Key Components and Functionalities of Video Streaming API
                                                    </h6>

                                                    <li>Video Streaming Infrastructure</li>

                                                    <li>APIs</li>

                                                    <li>Integration Options</li>

                                                    <li>Scalability and Reliability</li>

                                                    <li>Security</li>

                                                    <li>Analytics and Reporting</li>

                                                    <li>Customization & Personalization</li>

                                                </div>

                                                <hr>

                                            </div>

                                        </div>

                                        <div class="col-sm-4">

                                            <div class="communication-channels-icons2">

                                                <img class="#" src="images/whatsapp.svg" alt=""> &nbsp;&nbsp;&nbsp;
                                                <span style="font-weight: bold;">Whatsapp
                                                    Business</span>&nbsp;&nbsp;&nbsp; <span class="component-badge"
                                                    onmouseover="openPopup(event)" onmouseout="closePopup()">API</span>

                                                <div class="popup" onmouseover="cancelClose()"
                                                    onmouseout="closePopup(this)">

                                                    <p>WhatsApp Business API CPaaS (Communication Platform as a
                                                        Service) allows businesses to communicate with their
                                                        customers on WhatsApp, leveraging the messaging platform's
                                                        vast user base and rich features</p>

                                                    <h6>Key Components and Functionalities of WhatsApp Business API
                                                    </h6>

                                                    <li>Access to Whatsapp Business</li>

                                                    <li>API Integration</li>

                                                    <li>Rich Messaging Features </li>

                                                    <li>Automatic Messaging</li>

                                                    <li>Security & Compliance</li>

                                                    <li>Analytics and Reporting</li>

                                                    <li>Customer Support Integration</li>

                                                </div>

                                                <hr>

                                            </div>

                                        </div>

                                        <div class="col-sm-4">

                                            <div class="communication-channels-icons2">

                                                <img class="#" src="images/explore-all-channels.svg" alt="">
                                                &nbsp;&nbsp;&nbsp; <span style="font-weight: bold;">Explore all
                                                    Channels</span>&nbsp;&nbsp;&nbsp; <span class="component-badge"
                                                    onmouseover="openPopup(event)" onmouseout="closePopup()">API</span>

                                                <div class="popup" onmouseover="cancelClose()"
                                                    onmouseout="closePopup(this)">

                                                    <p>"Explore all channels" API CPaaS (Communication Platform as a
                                                        Service) refers to a comprehensive suite of APIs provided by
                                                        CPaaS providers that enable developers to integrate various
                                                        communication channels into their applications or systems.
                                                    </p>

                                                    <h6>Key Components and Functionalities of Explore All Channels
                                                        Chat API</h6>

                                                    <li>Multichannel Communication</li>

                                                    <li>Unified API</li>

                                                    <li>Integration Options</li>

                                                    <li>Scalability and Reliability</li>

                                                    <li>Security</li>

                                                    <li>Analytics and Reporting</li>

                                                    <li>Customization & Personalization</li>

                                                </div>

                                                <hr>

                                            </div>

                                        </div>

                                    </div>

                                </div>

                                <div class="clear"></div>

                                <div class="mask" style="height:40px;"></div>

                                <div class="row explore21years">

                                    <div class="col-md-6 ucpass-section-padding">

                                        <h1 class="ucpaas-heading4">Explore 21 years of performance with the best IT
                                            & communication services</h1>

                                        <div class="mask15"></div>

                                        <div class="mask" style="height:35px;"></div>

                                        <!-- <div class="view_more" onclick="document.location.href = '#Contact_Us'">Contact an expert</div> -->

                                    </div>

                                    <div class="col-md-6">

                                        <div class="row explore21years-width">

                                            <div class="col-md-1">

                                                <div class="vl"></div>

                                            </div>

                                            <div class="col-md-11">

                                                <h3>10 billion +</h3>

                                                <p>Interactions per year on various communication channels</p>

                                            </div>

                                        </div>

                                        <br>

                                        <br>

                                        <div class="row explore21years-width">

                                            <div class="col-md-1">

                                                <div class="vl"></div>

                                            </div>

                                            <br>

                                            <div class="col-md-11">

                                                <h3>500 +</h3>

                                                <p>Connections Worldwide</p>

                                            </div>

                                        </div>

                                        <br>

                                        <br>

                                        <div class="row explore21years-width">

                                            <div class="col-md-1">

                                                <div class="vl"></div>

                                            </div>

                                            <br>

                                            <div class="col-md-11">

                                                <h3>Strategic Operational</h3>

                                                <p>Presence in India and Abroad</p>

                                            </div>

                                        </div>

                                    </div>

                                </div>

                                <div class="clear"></div>

                                <div class="mask" style="height:40px;"></div>

                                <div class="row ucpass-section-padding">

                                    <div class="col-md-6 ucpass-section-padding">

                                        <h1 class="ucpaas-heading4">Tailormade TATWA UCPaaS Communication Parks for
                                            STARTUP’s & Entrepreneurs</h1>

                                        <div class="mask15"></div>

                                        <p>TATWA Understands Every Need of Customer Communication. Achieve your
                                            professional Goals with Unique UCPaaS Products</p>

                                        <div class="mask" style="height:35px;"></div>

                                        <!-- <div class="view_more" onclick="document.location.href = '#Contact_Us'">Contact an expert</div> -->

                                    </div>

                                    <div class="col-md-6">

                                        <div>

                                            <img class="ucpaas-img" src="images/communication-parks-img.svg"
                                                alt="ucpaas-img" title="ucpaas-img">

                                        </div>

                                    </div>

                                </div>

                                <div class="clear"></div>

                                <div class="mask" style="height:50px;"></div>

                                <hr>

                                <div class="row ucpass-section-padding">

                                    <div class="col-md-6">

                                        <div>

                                            <img class="ucpaas-img" src="images/communication-experience.svg"
                                                alt="ucpaas-img" title="ucpaas-img">

                                        </div>

                                    </div>

                                    <div class="col-md-6 ucpass-section-padding">

                                        <h1 class="ucpaas-heading4">Lets Renew Communication Experience for your
                                            Customers</h1>

                                        <div class="mask15"></div>

                                        <div class="mask" style="height:35px;"></div>

                                        <!-- <div class="view_more" onclick="document.location.href = '#Contact_Us'">Contact an expert</div> -->

                                    </div>

                                </div>

                                <div class="clear"></div>

                                <div class="mask" style="height:40px;"></div>

                                <!-- <div class="row ucpass-section-padding2"><h3 class="ucpaas-heading3">Sign Up for <br>Free Demo Account</h3><div class="clear"></div><div class="mask" style="height:40px;"></div><div class="view_more3" onclick="document.location.href = '#'">Try to free</div></div>  -->

                                <div class="row sign-up-section">

                                    <div class="col-md-6 ucpass-section-padding">

                                        <h1 class="ucpaas-heading5">Sign Up for <br>Free Demo Account </h1>

                                        <div class="mask15"></div>

                                        <div class="mask" style="height:35px;"></div>

                                        <div class="signup-try-to-free"
                                            onclick="document.location.href = '#Contact_Us'">Book a Demo</div>

                                    </div>

                                    <div class="col-md-6">

                                        <div>

                                            <img class="sign-up-free-demo-account"
                                                src="images/sign-up-free-demo-account-img.svg" alt="ucpaas-img"
                                                title="ucpaas-img">

                                        </div>

                                    </div>

                                </div>

                                <!-- <div class="row ucpass-section-padding"><div class="custom_banner"><h3 class="ucpaas-heading3">We’re ready to help you design the perfect communication experiences for your customers</h3></div></div> -->

                                <div class="clear"></div>

                                <div class="mask" style="height:40px;"></div>

                                <div class="row ucpass-section-padding">

                                    <h3 class="ucpaas-heading3" id="Contact_Us">Contact Us</h3>

                                    <div class="clear"></div>

                                    <div class="mask" style="height:40px;"></div>

                                </div>

                                <div class="row " style="width: 100%;">

                                    <div class="col-md-6">

                                        <form action="#" class="my-form">

                                            <div class="form-group">

                                                <label for="form-name" class="mb-3">Name</label>

                                                <input type="email" class="form-control" id="form-name"
                                                    placeholder="Name">

                                            </div>

                                            <div class="form-group">

                                                <label for="form-email" class="mb-3">Email Address</label>

                                                <input type="email" class="form-control" id="form-email"
                                                    placeholder="Email Address">

                                            </div>

                                            <div class="form-group">

                                                <label for="form-subject" class="mb-3">Phone No.</label>

                                                <input type="text" class="form-control" id="form-subject"
                                                    placeholder="Subject">

                                            </div>

                                            <div class="form-group">

                                                <label for="form-message" class="mb-3">Message</label>

                                                <textarea class="form-control" id="form-message"
                                                    placeholder="Message"></textarea>

                                            </div>

                                            <!-- <div class="view_more1" onclick="document.location.href = '#'"class="mb-3">Submit</div> -->

                                            <button type="submit" name="Send" id="send"
                                                class="btn btn-primary btn-block">Submit</button>

                                        </form>

                                    </div>

                                    <br>

                                    <div class="col-md-6">

                                        <!-- <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d14964.20319284379!2d85.80326887939633!3d20.339520032010654!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3a1909106f557927%3A0x45b505e2e9e9cfee!2sTatwa%20Technologies%20Ltd!5e0!3m2!1sen!2sin!4v1653130100721!5m2!1sen!2sin" width="100%" height="350" frameborder="0" style="border:0" allowfullscreen></iframe> -->

                                        <iframe
                                            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3768.1057650695584!2d72.9550549!3d19.1905823!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3be7b940af81b621%3A0xf4d62a5a362dc48b!2sOpal%20Square%20IT%20park!5e0!3m2!1sen!2sin!4v1684915148647!5m2!1sen!2sin"
                                            width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy"
                                            referrerpolicy="no-referrer-when-downgrade"></iframe>

                                        <!-- <div id="googlemap" style="width:100%; height:350px;"></div> -->

                                    </div>

                                </div>

                            </div>

                            <div class="clear"></div>

                            <div class="mask" style="height:25px;"></div>

                        </div>

                    </div>

                </div>

                <!--footer area start-->

                <!--top footer area start-->

                <div class="footerdiv">

                    <div class="footer_top">

                        <div class="arial38"> Let's make something <strong>great</strong> together </div>

                        <div class="clear"></div>

                        <div class="button_area mt50">

                            <div>

                                <a href="#" class="button_blue" target="_blank">Visit Our Blog</a>

                            </div>

                            <div>

                                <a href="contacts" class="button_border">Get in touch</a>

                            </div>

                            <div class="clear"></div>

                        </div>

                    </div>

                    <!--top footer area end-->

                    <div class="wrapper-inner2">

                        <div class="footer container-black container-tight1">

                            <div class="container-inner social-container">

                                <div class="contact-container12">

                                    <img src="images/cmmi-3.png"
                                        alt="We are an ISO 9001:2008 and CMMI Level 3 Assesses company."
                                        title="We are an ISO 9001:2008 and CMMI Level 3 Assesses company.">

                                </div>

                                <!---contact area start-->

                                <div class="contact-container">

                                    <div class="contact">

                                        <p class="contact-phone">Support Hotline: <span class="text-phone">+91 97774
                                                13556</span>

                                        </p>

                                        <a href="tel://******-796-6890" class="contact-phone-icon">

                                            <i style="padding:6px;" class="fa fa-phone fa-2x"></i>

                                        </a>

                                        <p class="visible-phone">(9am-5:30pm)</p>

                                        <p class="contact-email">

                                            <a
                                                href="cdn-cgi/l/email-protection.html#*********10e13152115001516004f080f070e"><span
                                                    class="__cf_email__"
                                                    data-cfemail="6c1f191c1c031e182c180d181b0d4205020a03">[email&#160;protected]</span></a>

                                            <span class="hidden-phone">(9.30am-6:30pm)</span>

                                        </p>

                                        <p class="contact-email">

                                            <a
                                                href="cdn-cgi/l/email-protection.html#1c6f7d70796f5c687d686b7d3275727a73"><span
                                                    class="__cf_email__"
                                                    data-cfemail="5221333e37211226332625337c3b3c343d">[email&#160;protected]</span></a>

                                        </p>

                                    </div>

                                    <div class="clearfix" style="height:10px;"></div>

                                    <!--social network area start-->

                                    <ul class="social-list" style="visibility: inherit;">

                                        <li class="social-item">
                                            <a href="https://twitter.com/tatwalive" target="_blank">
                                                <div class="social-icon social-icon-twitter">
                                                    <span
                                                        style="font-family: Arial; font-weight: bold; font-size: 1.2em;">𝕏</span>
                                                </div>
                                            </a>
                                        </li>

                                        <li class="social-item">

                                            <a href="https://www.facebook.com/tatwalive" target="_blank">

                                                <div class="social-icon social-icon-facebook"><i
                                                        class="fa fa-facebook"></i></div>

                                            </a>

                                        </li>

                                        <li class="social-item">

                                            <a href="https://www.linkedin.com/company/tatwalive" target="_blank">

                                                <div class="social-icon social-icon-linkdin"><i
                                                        class="fa fa-linkedin"></i></div>

                                            </a>

                                        </li>

                                        <li class="social-item">

                                            <a href="https://www.instagram.com/tatwalive" target="_blank">

                                                <div class="social-icon social-icon-instagram"><i
                                                        class="fa fa-instagram"></i></div>

                                            </a>

                                        </li>

                                        <li class="social-item">

                                            <a href="https://www.youtube.com/@tatwalive" target="_blank">

                                                <div class="social-icon social-icon-youtube"><i
                                                        class="fa fa-youtube"></i></div>

                                            </a>

                                        </li>

                                    </ul>

                                    <!--social network area end-->

                                    <a href="#" id="livechat-mobile" class="livechat-mobile"></a>

                                </div>

                                <!---contact area end-->

                                <div class="clearfix"></div>

                            </div>

                            <div class="line-hori"></div>

                            <!--footer bottom area start-->

                            <div class="container-inner container-inner-bordered">

                                <div id="disclaimer"> Copyright © 2024 Tatwa Technologies Ltd. All rights reserved.
                                    &nbsp; &nbsp; &nbsp; <a href="privacy-policy">Privacy policy</a>&nbsp; | &nbsp;
                                    <a href="disclaimer">Disclaimer</a>&nbsp; | &nbsp; <a
                                        href="press-release">Newsroom</a>&nbsp; | &nbsp; <a href="blog"
                                        target="_blank">Blog</a>&nbsp;
                                </div>

                                <!--footer bottom area end-->

                            </div>

                        </div>

                        <!--footer area end-->

                    </div>

                    <!--tab menu area end-->

                    <div class="clear"></div>

                </div>

            </div>

            <script data-cfasync="false" src="cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script>

        </div>

    </div>

    <script>

        var timer;



        function openPopup(element) {

            var popup = element.nextElementSibling;

            popup.style.display = "block";

        }



        function closePopup(element) {

            var popup = element.nextElementSibling;

            timer = setTimeout(function () {

                popup.style.display = "none";

            }, 500);

        }



        function cancelClose() {

            clearTimeout(timer);

        }

    </script>

</body>

</html>