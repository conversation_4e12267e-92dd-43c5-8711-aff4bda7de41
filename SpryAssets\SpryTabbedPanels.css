@charset "UTF-8";



/* SpryTabbedPanels.css - version 0.6 - Spry Pre-Release 1.6.1 */



/* Copyright (c) 2006. Adobe Systems Incorporated. All rights reserved. */



/* Horizontal Tabbed Panels

 *

 * The default style for a TabbedPanels widget places all tab buttons

 * (left aligned) above the content panel.

 */



/* This is the selector for the main TabbedPanels container. For our

 * default style, this container does not contribute anything visually,

 * but it is floated left to make sure that any floating or clearing done

 * with any of its child elements are contained completely within the

 * TabbedPanels container, to minimize any impact or undesireable

 * interaction with other floated elements on the page that may be used

 * for layout.

 *

 * If you want to constrain the width of the TabbedPanels widget, set a

 * width on the TabbedPanels container. By default, the TabbedPanels widget

 * expands horizontally to fill up available space.

 *

 * The name of the class ("TabbedPanels") used in this selector is not

 * necessary to make the widget function. You can use any class name you

 * want to style the TabbedPanels container.

 */

.TabbedPanels {

	overflow: hidden;

	margin: 0px;

	padding: 0px;

	clear: none;

	width: 100%; /* IE Hack to force proper layout when preceded by a paragraph. (hasLayout Bug)*/

}



/* This is the selector for the TabGroup. The TabGroup container houses

 * all of the tab buttons for each tabbed panel in the widget. This container

 * does not contribute anything visually to the look of the widget for our

 * default style.

 *

 * The name of the class ("TabbedPanelsTabGroup") used in this selector is not

 * necessary to make the widget function. You can use any class name you

 * want to style the TabGroup container.

 */

.TabbedPanelsTabGroup {

  width: 452px;

  margin: 60px auto 40px auto;

}



/* This is the selector for the TabbedPanelsTab. This container houses

 * the title for the panel. This is also the tab "button" that the user clicks

 * on to activate the corresponding content panel so that it appears on top

 * of the other tabbed panels contained in the widget.

 *

 * For our default style, each tab is positioned relatively 1 pixel down from

 * where it wold normally render. This allows each tab to overlap the content

 * panel that renders below it. Each tab is rendered with a 1 pixel bottom

 * border that has a color that matches the top border of the current content

 * panel. This gives the appearance that the tab is being drawn behind the

 * content panel.

 *

 * The name of the class ("TabbedPanelsTab") used in this selector is not

 * necessary to make the widget function. You can use any class name you want

 * to style this tab container.

 */

.TabbedPanelsTab {

	position: relative;

	top:1px;

	float: left;

	padding: 20px 40px;

	margin: 0px 4px 0px 0px;

	background-color:#3f4143;

	-webkit-border-radius: 2px;

	-moz-border-radius: 2px;

	border-radius: 2px;

	list-style: none;

	-moz-user-select: none;

	-khtml-user-select: none;

	cursor: pointer;

	color: #d1d1d1;

   font-size: 18px;

   font-weight: normal;

   text-transform:uppercase;

}



/* This selector is an example of how to change the appearnce of a tab button

 * container as the mouse enters it. The class "TabbedPanelsTabHover" is

 * programatically added and removed from the tab element as the mouse enters

 * and exits the container.

 */

.TabbedPanelsTabHover {

	color: #ecebeb;

	background-color: rgba(0, 0, 0, 0.4);

	outline-color:#484b4d;

}



/* This selector is an example of how to change the appearance of a tab button

 * container after the user has clicked on it to activate a content panel.

 * The class "TabbedPanelsTabSelected" is programatically added and removed

 * from the tab element as the user clicks on the tab button containers in

 * the widget.

 *

 * As mentioned above, for our default style, tab buttons are positioned

 * 1 pixel down from where it would normally render. When the tab button is

 * selected, we change its bottom border to match the background color of the

 * content panel so that it looks like the tab is part of the content panel.

 */

.TabbedPanelsTabSelected {

	color: #ecebeb;

	background-color: rgba(0, 0, 0, 0.4);

	outline-color:#484b4d;

}



/* This selector is an example of how to make a link inside of a tab button

 * look like normal text. Users may want to use links inside of a tab button

 * so that when it gets focus, the text *inside* the tab button gets a focus

 * ring around it, instead of the focus ring around the entire tab.

 */

.TabbedPanelsTab a {

	color: black;

	text-decoration: none;

}



/* This is the selector for the ContentGroup. The ContentGroup container houses

 * all of the content panels for each tabbed panel in the widget. For our

 * default style, this container provides the background color and borders that

 * surround the content.

 *

 * The name of the class ("TabbedPanelsContentGroup") used in this selector is

 * not necessary to make the widget function. You can use any class name you

 * want to style the ContentGroup container.

 */

.TabbedPanelsContentGroup {

	clear: both;

	

}

/* This is the selector for the Content panel. The Content panel holds the

 * content for a single tabbed panel. For our default style, this container

 * provides some padding, so that the content is not pushed up against the

 * widget borders.

 *

 * The name of the class ("TabbedPanelsContent") used in this selector is

 * not necessary to make the widget function. You can use any class name you

 * want to style the Content container.

 */

.TabbedPanelsContent {

	overflow: hidden;

}



/* This selector is an example of how to change the appearnce of the currently

 * active container panel. The class "TabbedPanelsContentVisible" is

 * programatically added and removed from the content element as the panel

 * is activated/deactivated.

 */

.TabbedPanelsContentVisible {

}



/* Vertical Tabbed Panels

 *

 * The following rules override some of the default rules above so that the

 * TabbedPanels widget renders with its tab buttons along the left side of

 * the currently active content panel.

 *

 * With the rules defined below, the only change that will have to be made

 * to switch a horizontal tabbed panels widget to a vertical tabbed panels

 * widget, is to use the "VTabbedPanels" class on the top-level widget

 * container element, instead of "TabbedPanels".

 */



.VTabbedPanels {

	overflow: hidden;

	zoom: 1;

}



/* This selector floats the TabGroup so that the tab buttons it contains

 * render to the left of the active content panel. A border is drawn around

 * the group container to make it look like a list container.

 */

.VTabbedPanels .TabbedPanelsTabGroup {

	float: left;

	width: 10em;

	height: 20em;

	background-color: #EEE;

	position: relative;

	border-top: solid 1px #999;

	border-right: solid 1px #999;

	border-left: solid 1px #CCC;

	border-bottom: solid 1px #CCC;

}



/* This selector disables the float property that is placed on each tab button

 * by the default TabbedPanelsTab selector rule above. It also draws a bottom

 * border for the tab. The tab button will get its left and right border from

 * the TabGroup, and its top border from the TabGroup or tab button above it.

 */

.VTabbedPanels .TabbedPanelsTab {

	float: none;

	margin: 0px;

	border-top: none;

	border-left: none;

	border-right: none;

}



/* This selector disables the float property that is placed on each tab button

 * by the default TabbedPanelsTab selector rule above. It also draws a bottom

 * border for the tab. The tab button will get its left and right border from

 * the TabGroup, and its top border from the TabGroup or tab button above it.

 */

.VTabbedPanels .TabbedPanelsTabSelected {

	background-color: #EEE;

	border-bottom: solid 1px #999;

}



/* This selector floats the content panels for the widget so that they

 * render to the right of the tabbed buttons.

 */

.VTabbedPanels .TabbedPanelsContentGroup {

	clear: none;

	float: left;

	padding: 0px;

	width: 30em;

	height: 20em;

}



/* Styles for Printing */

@media print {

.TabbedPanels {

	overflow: visible !important;

}

.TabbedPanelsContentGroup {

	display: block !important;

	overflow: visible !important;

	height: auto !important;

}

.TabbedPanelsContent {

	overflow: visible !important;

	display: block !important;

	clear:both !important;

}

.TabbedPanelsTab {

	 overflow: visible !important;

	 display: block !important;

	 clear:both !important;

}

}