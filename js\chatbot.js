const chatResponses = {
	services: {
		general:
			'We offer various services including:\n- Business Process Management\n- Technology Consulting & Implementation\n- IT & Cloud Operations\n- Unified Communication Platform as a Service',
		bpm: 'Our Business Process Management services include:\n- Inbound Process\n- Outbound Process\n- Technical Help Desk & Data Support\n- Customer Life Cycle Management\n- Premium Support Desk',
		tech: 'Our Technology services include:\n- Digital Transformation\n- Blockchain Services\n- Cyber Security Services\n- Custom Enterprise Software Development\n- Quality Assurance',
		system: 'Our System Integration services include:\n- Data Center\n- Cloud Computing\n- Virtualization Services\n- Server & Storage Solutions\n- Network Infrastructure',
	},
	contact: {
		general:
			'You can reach us at:\nPhone: +91 97774 13556\nEmail: support@tatwa.<NAME_EMAIL>\nWorking hours: 9:30am-6:30pm\nPost 6:30 PM contact/<NAME_EMAIL>',
		support:
			'For technical support:\nEmail: <EMAIL>\nPhone: +91 97774 13556\nWorking hours: 9:30am-6:30pm\nPost 6:30 PM contact/<NAME_EMAIL>',
		sales: 'For sales inquiries:\nEmail: <EMAIL>\nWorking hours: 9:30am-6:30pm\nPost 6:30 PM contact/<NAME_EMAIL>',
	},
	about: {
		general:
			'TATWA Technologies Ltd was founded in 2002. We are a CMMI Level 3 assessed company with over 1700+ domain & technical experts working on 120+ ongoing projects.',
		history:
			'Founded in 2002, TATWA has grown from a 3-person organization to a global technology leader with over 1700+ experts.',
		certifications:
			'We are proud to be:\n- CMMI Level 3 Assessed\n- ISO 9001:2008 Certified\n- Multiple Award-Winning Company',
	},
	locations: {
		general: 'We have 8+ offices in India and abroad, serving clients globally.',
		india: 'Our major offices in India are located in:\n- Bhubaneswar\n- Bangalore\n- Mumbai',
		global: 'We have international presence with offices strategically located to serve our global clients.',
	},
	careers: {
		general: 'Visit our careers page at tatwa.com/careers to explore current job opportunities.',
		positions:
			'We regularly hire for positions in:\n- Software Development\n- System Integration\n- Business Process Management\n- Technical Support\n- Project Management',
		culture:
			'At Tatwa, we offer:\n- Professional Growth\n- Innovative Work Environment\n- Global Exposure\n- Competitive Benefits',
	},
	products: {
		general:
			'Our product portfolio includes solutions for:\n- Enterprise Management\n- Digital Transformation\n- Blockchain\n- Security',
		blockchain:
			'Our blockchain products include:\n- Tatwa Validate\n- Tatwa Fund-Track\n- Tatwa Crowd Source\n- Tatwa AssetReg\n- Tatwa Elect',
	},
	default:
		"I apologize, but I'm not sure about that specific query. Would you like to:\n1. Speak with our support team?\n2. Browse our website for more information?\n3. Ask a different question?",
};

let isOpen = false;

function clearChat() {
	const chatMessages = document.getElementById('chatMessages');
	chatMessages.innerHTML = ''; // Clear all messages
	const userInput = document.getElementById('userInput');
	userInput.value = ''; // Clear input field
}

function toggleChat() {
	const chatContainer = document.getElementById('chatContainer');
	isOpen = !isOpen;

	if (isOpen) {
		chatContainer.style.display = 'flex';
		// Only show welcome message if chat is empty
		if (document.getElementById('chatMessages').children.length === 0) {
			addBotMessage("Hello! 👋 I'm Tatwa's virtual assistant. How can I help you today?");
			showOptions();
		}
	} else {
		chatContainer.style.display = 'none';
		clearChat(); // Clear chat when closing
	}
}

function showOptions() {
	const options = [
		'Tell me about your services',
		'How can I contact you?',
		'About Tatwa',
		'Office locations',
		'Career opportunities',
		'Your products',
		'Technical support',
	];

	const optionsContainer = document.createElement('div');
	optionsContainer.className = 'options-container';

	options.forEach((option) => {
		const button = document.createElement('button');
		button.className = 'option-button';
		button.textContent = option;
		button.onclick = () => handleOptionClick(option);
		optionsContainer.appendChild(button);
	});

	const chatMessages = document.getElementById('chatMessages');
	chatMessages.appendChild(optionsContainer);
}

function handleOptionClick(option) {
	const userInput = document.getElementById('userInput');
	userInput.value = option;
	sendMessage();
}

function addMessage(message, isUser) {
	const chatMessages = document.getElementById('chatMessages');
	const messageDiv = document.createElement('div');
	messageDiv.className = `message ${isUser ? 'user-message' : 'bot-message'}`;
	// Replace \n with <br> for proper line breaks
	messageDiv.innerHTML = message.replace(/\n/g, '<br>');
	chatMessages.appendChild(messageDiv);
	chatMessages.scrollTop = chatMessages.scrollHeight;
}

function addBotMessage(message) {
	addMessage(message, false);
}

function handleKeyPress(event) {
	if (event.key === 'Enter') {
		sendMessage();
	}
}

function getBotResponse(message) {
	message = message.toLowerCase();

	// Check for specific topics within categories
	if (message.includes('services')) {
		if (message.includes('bpm') || message.includes('process') || message.includes('business')) {
			return chatResponses.services.bpm;
		} else if (message.includes('tech') || message.includes('technology') || message.includes('digital')) {
			return chatResponses.services.tech;
		} else if (message.includes('system') || message.includes('integration')) {
			return chatResponses.services.system;
		}
		return chatResponses.services.general;
	}

	if (message.includes('contact')) {
		if (message.includes('support') || message.includes('help') || message.includes('technical')) {
			return chatResponses.contact.support;
		} else if (message.includes('sales') || message.includes('purchase')) {
			return chatResponses.contact.sales;
		}
		return chatResponses.contact.general;
	}

	if (message.includes('about')) {
		if (message.includes('history') || message.includes('founded')) {
			return chatResponses.about.history;
		} else if (message.includes('certification') || message.includes('cmmi') || message.includes('iso')) {
			return chatResponses.about.certifications;
		}
		return chatResponses.about.general;
	}

	if (message.includes('location')) {
		if (message.includes('india') || message.includes('domestic')) {
			return chatResponses.locations.india;
		} else if (message.includes('global') || message.includes('international')) {
			return chatResponses.locations.global;
		}
		return chatResponses.locations.general;
	}

	if (message.includes('career') || message.includes('job') || message.includes('work')) {
		if (message.includes('position') || message.includes('role') || message.includes('opening')) {
			return chatResponses.careers.positions;
		} else if (message.includes('culture') || message.includes('benefit') || message.includes('perks')) {
			return chatResponses.careers.culture;
		}
		return chatResponses.careers.general;
	}

	if (message.includes('product')) {
		if (message.includes('blockchain') || message.includes('crypto')) {
			return chatResponses.products.blockchain;
		}
		return chatResponses.products.general;
	}

	// Handle common variations of questions
	if (message.includes('help') || message.includes('support')) {
		return chatResponses.contact.support;
	}

	if (message.includes('office') || message.includes('branch')) {
		return chatResponses.locations.general;
	}

	if (message.includes('company') || message.includes('tatwa')) {
		return chatResponses.about.general;
	}

	return chatResponses.default;
}

function sendMessage() {
	const userInput = document.getElementById('userInput');
	const message = userInput.value.trim();

	if (message) {
		addMessage(message, true);
		const botResponse = getBotResponse(message);
		setTimeout(() => {
			addBotMessage(botResponse);
			showOptions();
		}, 500);

		userInput.value = '';
	}
}
