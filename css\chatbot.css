.chat-container {
	position: fixed;
	bottom: 20px;
	right: 20px;
	width: 350px;
	height: 500px;
	background: white;
	border-radius: 10px;
	box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
	display: none;
	flex-direction: column;
	z-index: 1000;
}

.chat-header {
	background: #218ecd;
	color: white;
	padding: 15px;
	border-radius: 10px 10px 0 0;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.chat-header h3 {
	margin: 0;
	font-size: 16px;
}

.close-chat {
	cursor: pointer;
	font-size: 20px;
}

.chat-messages {
	flex-grow: 1;
	padding: 15px;
	overflow-y: auto;
}

.message {
	margin-bottom: 10px;
	max-width: 80%;
	padding: 10px;
	border-radius: 10px;
}

.bot-message {
	background: #f0f0f0;
	margin-right: auto;
}

.user-message {
	background: #218ecd;
	color: white;
	margin-left: auto;
	margin-top: 5px;
}

.chat-input {
	padding: 15px;
	border-top: 1px solid #eee;
	display: flex;
}

.chat-input input {
	flex-grow: 1;
	padding: 10px;
	border: 1px solid #ddd;
	border-radius: 5px;
	margin-right: 10px;
}

.chat-input button {
	padding: 10px 20px;
	background: #218ecd;
	color: white;
	border: none;
	border-radius: 5px;
	cursor: pointer;
}

.chat-button {
	position: fixed;
	bottom: 20px;
	right: 20px;
	background: #218ecd;
	color: white;
	padding: 15px;
	border-radius: 50%;
	cursor: pointer;
	box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
	z-index: 1000;
}

.chat-button i {
	font-size: 24px;
}

.options-container {
	display: flex;
	flex-wrap: wrap;
	gap: 5px;
	margin-top: 10px;
}

.option-button {
	background: #218ecd;
	color: white;
	border: none;
	padding: 5px 10px;
	border-radius: 15px;
	cursor: pointer;
	font-size: 14px;
}
