/*! Copyright Pingdom AB 2015. All rights reserved. */

(function(){var FixedModal,__hasProp={}.hasOwnProperty,__extends=function(child,parent){function ctor(){this.constructor=child}for(var key in parent)__hasProp.call(parent,key)&&(child[key]=parent[key]);return ctor.prototype=parent.prototype,child.prototype=new ctor,child.__super__=parent.prototype,child};!function(window,$){var Pingdom,Set,Set2D,capitalize,formatCurrency,formatNumber,hasCssAnimSupport,inViewport,isHighRes,isMobile,isTablet,lzwDecode,randomstring,setFormsLoading,setHighRes,sortedIndex,utils;return Pingdom=window.Pingdom=window.Pingdom||{},utils=Pingdom.utils=Pingdom.utils||{},formatNumber=Pingdom.utils.formatNumber=function(number,options){var numberStr,o;return isNaN(number)?"-":(o={useSuffix:!1,numDecimals:0,precision:0/0,numberFormat:null},$.extend(o,options),numberStr=void 0,numberStr=isNaN(o.precision)?Number(number).toFixed(o.numDecimals):Number(number).toPrecision(o.precision).replace(/^([\d.]+)e\+(\d+)$/,function(match,num,exp){return Math.floor(num*Math.pow(10,exp))}),numberStr=numberStr.replace(/^(-?)(\d+?)(\.\d+)?$/,function(match,sign,integers,decimals){var base_10_lenght,cutoff,decimalSeparator,formatted,nearestTripple,numbers,prefixes,thousandSeparator;return integers?(cutoff=integers.length%3,formatted="",o.useSuffix?(base_10_lenght=parseInt(number,10).toString().length-1,nearestTripple=3*(base_10_lenght/3|0),prefixes={24:"Y",21:"Z",18:"E",15:"P",12:"T",9:"G",6:"M",3:"k",0:""},numbers=(number/Math.pow(10,nearestTripple)).toFixed(o.numDecimals),numbers>=1e3&&(numbers=(numbers/1e3).toFixed(o.numDecimals),nearestTripple+=3),formatted=numbers+prefixes[nearestTripple]):(formatted=sign,integers.length>3?(formatted+=integers.substr(0,cutoff)+(cutoff>0?",":""),formatted+=integers.substr(cutoff).match(/(\d{3})/g).join(",")):formatted+=integers,decimals&&(formatted+=isNaN(o.precision)&&o.trim?decimals.replace(/\.?0*$/,""):decimals)),o.numberFormat&&(decimalSeparator=o.numberFormat.substr(0,1),thousandSeparator=o.numberFormat.substr(1,2),formatted=formatted.replace(/[.,]/g,function(match){return"."===match?decimalSeparator:thousandSeparator})),formatted):match}))},capitalize=Pingdom.utils.capitalize=function(string){return string.toLowerCase().replace(/\b./g,function(character){return character.toUpperCase()})},hasCssAnimSupport=Pingdom.utils.hasCssAnimSupport=function(){var checkEl,cssAnimSupport,i,len,prefixes;if(cssAnimSupport=!1,checkEl=document.getElementsByTagName("body")[0])if(void 0!==checkEl.style.animation)cssAnimSupport=!0;else for(prefixes=["webkit","Moz"],i=0,len=prefixes.length;len>i;)void 0!==checkEl.style[prefixes[i]+"Animation"]&&(cssAnimSupport=!0),i++;return cssAnimSupport},lzwDecode=Pingdom.utils.lzwDecode=function(string){var code,currChar,currCode,data,dict,i,oldPhrase,out,phrase;for(dict={},data=(string+"").split(""),currChar=data[0],oldPhrase=currChar,out=[currChar],code=256,i=1;i<data.length;)currCode=data[i].charCodeAt(0),phrase=256>currCode?data[i]:dict[currCode]?dict[currCode]:oldPhrase+currChar,out.push(phrase),currChar=phrase.charAt(0),dict[code]=oldPhrase+currChar,code++,oldPhrase=phrase,i++;return out.join("")},randomstring=Pingdom.utils.randomstring=function(L){var randomchar,s;for(s="",randomchar=function(){var n;return n=Math.floor(62*Math.random()),10>n?n:String.fromCharCode(36>n?n+55:n+61)};s.length<L;)s+=randomchar();return s},isMobile=Pingdom.utils.isMobile=navigator.userAgent.match(/mobile|phone|iPod|BlackBerry/i)&&!navigator.userAgent.match(/iPad/i)?!0:!1,isTablet=Pingdom.utils.isTablet=navigator.userAgent.match(/iPad/i)||navigator.userAgent.match(/Android/i)&&!navigator.userAgent.match(/mobile/i)?!0:!1,isHighRes=Pingdom.utils.isHighRes=function(){var result;return"function"==typeof matchMedia?(result=matchMedia("(-webkit-min-device-pixel-ratio: 2), (min-resolution: 168dpi)"),result.matches):!1},setHighRes=Pingdom.utils.setHighRes=function(images){var unbindEvents;return unbindEvents=function(target){return $(target).unbind("error.HighRes"),$(target).unbind("load.HighRes")},isHighRes()?images.each(function(){return $(this).bind("error.HighRes",function(evt){var _ref;return null!=(_ref=evt.target)&&(_ref.src=evt.target.src.replace("@2x","")),unbindEvents(evt.target)}),$(this).bind("load.HighRes",function(evt){return unbindEvents(evt.target)}),this.src=this.src.replace(/(@2x)?\.(png|jpe?g|gif)/,"@2x.$2")}):void 0},setFormsLoading=Pingdom.utils.setFormsLoading=function(isLoading){return isLoading?($("form .button-container").addClass("button-container-loading"),$("form .button-base").prop("disabled",!0)):($("form .button-container").removeClass("button-container-loading"),$("form .button-base").prop("disabled",!1))},inViewport=Pingdom.utils.inViewport=function(el){var rect;return rect=el.getBoundingClientRect(),rect.bottom>=0&&rect.top<=(window.innerHeight||document.documentElement.clientHeight)},formatCurrency=Pingdom.utils.formatCurrency=function(amount,currency,options){var amountStr,numberFormat,o,pos,sign,symbol;switch(null==options&&(options={}),o={numDecimals:2},$.extend(o,options),pos="LEFT",symbol=currency+" ",numberFormat=null,currency){case"EUR":symbol="€";break;case"GBP":symbol="£";break;case"SEK":symbol=" kr",pos="RIGHT",numberFormat=", ";break;case"USD":symbol="$"}return sign=0>amount?"-":"",amountStr=Pingdom.utils.formatNumber(Math.abs(amount),{numDecimals:o.numDecimals,trim:!1,numberFormat:numberFormat}),"RIGHT"===pos?sign+amountStr+symbol:sign+symbol+amountStr},Pingdom.utils.Set=Set=function(){function Set(){this._items={},this._count=0}return Set.prototype.add=function(value){return this._items.hasOwnProperty(value)?!1:(this._items[value]=1,this._count++,!0)},Set.prototype.contains=function(value){return null!=this._items[value]},Set.prototype.remove=function(value){return this._items.hasOwnProperty(value)?(delete this._items[value],this._count--,!0):!1},Set.prototype.count=function(){return this._count},Set.prototype.clear=function(){return this._items={},this._count=0},Set.prototype.items=function(){var k,_results;_results=[];for(k in this._items)_results.push(k);return _results},Set}(),Pingdom.utils.Set2D=Set2D=function(){function Set2D(){this._items={},this._count=0}return Set2D.prototype.add=function(x,y){var _base;return null==(_base=this._items)[x]&&(_base[x]=new Set),this._items[x].add(y)?this._count++:void 0},Set2D.prototype.contains=function(x,y){var _ref;return(null!=(_ref=this._items[x])?_ref.contains(y):void 0)===!0},Set2D.prototype.remove=function(x,y){var _ref,_ref1;return(null!=(_ref=this._items[x])?_ref.remove(y):void 0)&&this._count--,0===(null!=(_ref1=this._items[x])?_ref1.count():void 0)?delete this._items[x]:void 0},Set2D.prototype.count=function(){return this._count},Set2D.prototype.clear=function(){return this._items={},this._count=0},Set2D.prototype.items=function(){var i,items,k,v,_i,_len,_ref,_ref1;items=[],_ref=this._items;for(k in _ref)for(v=_ref[k],_ref1=v.items(),_i=0,_len=_ref1.length;_len>_i;_i++)i=_ref1[_i],items.push([k,i]);return items},Set2D}(),sortedIndex=Pingdom.utils.sortedIndex=function(array,value,cmp){var high,low,mid;for(high=(null!=array?array.length:void 0)||0,low=0,null==cmp&&(cmp=function(a,b){return b>a});high>low;)mid=low+high>>1,cmp(array[mid],value)?low=mid+1:high=mid;return low},null}(window,jQuery),function(window,jQuery){var $,Pingdom,addDot,canvas,ctx,currentCol,grid,gridResolution,img,init,map,nullZone,numCols,numRows,projection,renderQueue,sortDots,_clear,_connect,_connectCallback,_mercator,_outageCounter,_outageCounterEl,_redraw,_redrawQueued,_scanColumn,_shiftDots,_updateCounter;return $=jQuery,Pingdom=window.Pingdom||(window.Pingdom={}),map=Pingdom.map||(Pingdom.map={}),canvas=null,ctx=null,img=null,numRows=null,numCols=null,projection=null,nullZone=null,grid=[],gridResolution=3,currentCol=0,renderQueue=new Pingdom.utils.Set2D,_redrawQueued=!1,_outageCounter=0,_outageCounterEl=null,_connectCallback=null,init=Pingdom.map.init=function(options){var c,connect,dataUrl,nz,r,requestAnimationFrame,scale,translate,_ref;return(canvas=document.getElementById("map-main"))?(ctx=canvas.getContext("2d"),img=new Image,img.src="/_img/img-map-dot.png",connect=options.connect,scale=options.scale,translate=options.translate,dataUrl=options.dataUrl,null==scale&&(scale=canvas.width),null==translate&&(translate=[canvas.width/2,canvas.height/2]),null==dataUrl&&(dataUrl=null!=(_ref=Pingdom.map.url)?_ref:""),numCols=Math.floor(canvas.width/gridResolution),numRows=Math.floor(canvas.height/gridResolution),grid=function(){var _i,_results;for(_results=[],c=_i=0;numCols>=0?numCols>_i:_i>numCols;c=numCols>=0?++_i:--_i)_results.push(function(){var _j,_results1;for(_results1=[],r=_j=0;numRows>=0?numRows>_j:_j>numRows;r=numRows>=0?++_j:--_j)_results1.push([]);return _results1}());return _results}(),projection=_mercator().scale(scale).translate(translate),nz=projection([0,0]),nullZone={x:Math.floor(nz[0]/gridResolution),y:Math.floor(nz[1]/gridResolution)},requestAnimationFrame=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||window.msRequestAnimationFrame||function(callback){return setTimeout(callback,16)},window.requestAnimationFrame=requestAnimationFrame,setInterval(_scanColumn,10),_connectCallback=connect,_connect(dataUrl,options)):void 0},addDot=Pingdom.map.addDot=function(coords,_arg){var animate,ax,ay,cell,el,index,time,transCoords,ttl,x,y,_ref,_ref1;return _ref=null!=_arg?_arg:{},animate=_ref.animate,ttl=_ref.ttl,null==animate&&(animate=!0),null==ttl&&(ttl=3600),Pingdom.utils.inViewport(canvas)||(animate=!1),transCoords=projection(coords),ax=Math.floor(transCoords[0]/gridResolution),ay=Math.floor(transCoords[1]/gridResolution),cell=null!=(_ref1=grid[ax])?_ref1[ay]:void 0,null!=cell&&(time=Date.now()+1e3*ttl,index=Pingdom.utils.sortedIndex(cell,time),cell.splice(index,0,time),ax!==nullZone.x||ay!==nullZone.y)?(x=ax*gridResolution-img.width/2,y=ay*gridResolution-img.height/2,1===cell.length&&ctx.drawImage(img,x,y),animate?(el=$('<img src="/_img/img-map-dot-strong.png" class="map-dot-strong"></div>').css({left:x,top:y}).appendTo("#map-container"),setTimeout(function(){return setTimeout(function(){return el.remove(),el=null},700),el.addClass("map-dot-strong-fade")},1)):void 0):void 0},sortDots=Pingdom.map.sortDots=function(){var cell,col,_i,_j,_len,_len1;for(_i=0,_len=grid.length;_len>_i;_i++)for(col=grid[_i],_j=0,_len1=col.length;_len1>_j;_j++)cell=col[_j],cell.sort()},_shiftDots=function(x,y,length){var cell;return cell=grid[x][y],length=Math.min(length,cell.length),length>0&&(_updateCounter(-length),cell.splice(0,length),0===cell.length)?renderQueue.add(x,y):void 0},_scanColumn=function(){var cell,currentRow,i,length,now,_i,_j,_len,_ref;for(currentCol=++currentCol%numCols,now=Date.now(),_ref=grid[currentCol],currentRow=_i=0,_len=_ref.length;_len>_i;currentRow=++_i){for(cell=_ref[currentRow],length=cell.length,i=_j=0;length>=0?length>_j:_j>length;i=length>=0?++_j:--_j)if(cell[i]>now){i>0&&(_shiftDots(currentCol,currentRow,i),_redraw());break}i===length&&length>0&&(_shiftDots(currentCol,currentRow,length),_redraw())}},_redraw=function(){return _redrawQueued?void 0:(_redrawQueued=!0,requestAnimationFrame(function(){var c,clipSize,clipX,clipY,imgCellRadius,imgPxRadius,r,renderItems,renderX1,renderX2,renderY1,renderY2,x,y,_i,_j,_ref;for(imgCellRadius=Math.ceil((img.width/gridResolution-1)/2),imgPxRadius=Math.ceil(img.width/2),renderItems=renderQueue.items(),renderQueue.clear();renderItems.length;){for(_ref=renderItems.pop(),c=_ref[0],r=_ref[1],clipX=(c-imgCellRadius)*gridResolution,clipY=(r-imgCellRadius)*gridResolution,clipSize=(1+2*imgCellRadius)*gridResolution,renderX1=c-2*imgCellRadius,0>renderX1&&(renderX1=0),renderX2=c+2*imgCellRadius,renderX2>numCols&&(renderX2=numCols),renderY1=r-2*imgCellRadius,0>renderY1&&(renderY1=0),renderY2=r+2*imgCellRadius,renderY2>numRows&&(renderY2=numRows),ctx.clearRect(clipX,clipY,clipSize,clipSize),ctx.save(),ctx.beginPath(),ctx.rect(clipX,clipY,clipSize,clipSize),ctx.clip(),x=_i=renderX1;renderX2>=renderX1?renderX2>_i:_i>renderX2;x=renderX2>=renderX1?++_i:--_i)for(y=_j=renderY1;renderY2>=renderY1?renderY2>_j:_j>renderY2;y=renderY2>=renderY1?++_j:--_j)(x!==nullZone.x||y!==nullZone.y)&&grid[x][y].length&&ctx.drawImage(img,x*gridResolution-imgPxRadius,y*gridResolution-imgPxRadius);ctx.restore()}return _redrawQueued=!1}))},_clear=function(){var c,cell,col,r,_i,_j,_len,_len1;for(_updateCounter(-_outageCounter),c=_i=0,_len=grid.length;_len>_i;c=++_i)for(col=grid[c],r=_j=0,_len1=col.length;_len1>_j;r=++_j)cell=col[r],cell.length>0&&(cell.splice(0,cell.length),renderQueue.add(c,r));return _redraw()},_connect=function(dataUrl,_arg){var animate,hasFetchedHour,socket;return animate=(null!=_arg?_arg:{}).animate,null==animate&&(animate=!0),hasFetchedHour=!1,socket=io.connect(dataUrl),socket.on("connect",function(){return _outageCounterEl=$(".text-counter"),hasFetchedHour||socket.emit("requestHourData"),null!=_connectCallback?_connectCallback():void 0}),socket.on("points",function(points){var point,_i,_len;for(_clear(),hasFetchedHour=!0,_i=0,_len=points.length;_len>_i;_i++)point=points[_i],Pingdom.map.addDot([point.o,point.a],{animate:!1,ttl:point.t});return sortDots(),_updateCounter(points.length)}),socket.on("point",function(point){Pingdom.map.addDot([point.o,point.a],{animate:animate,ttl:point.t}),_updateCounter(1)})},_updateCounter=function(num){_outageCounter+=num,null!=_outageCounterEl&&_outageCounterEl.text(Pingdom.utils.formatNumber(_outageCounter))},_mercator=function(){var d3_geo_radians,mercator,scale,translate;return d3_geo_radians=Math.PI/180,mercator=function(coordinates){var x,y;return x=coordinates[0]/360,y=-(Math.log(Math.tan(Math.PI/4+coordinates[1]*d3_geo_radians/2))/d3_geo_radians)/360,[scale*x+translate[0],scale*Math.max(-.5,Math.min(.5,y))+translate[1]]},scale=500,translate=[480,250],mercator.invert=function(coordinates){var x,y;return x=(coordinates[0]-translate[0])/scale,y=(coordinates[1]-translate[1])/scale,[360*x,2*Math.atan(Math.exp(-360*y*d3_geo_radians))/d3_geo_radians-90]},mercator.scale=function(x){return arguments.length?(scale=+x,mercator):scale},mercator.translate=function(x){return arguments.length?(translate=[+x[0],+x[1]],mercator):translate},mercator},null}(window,jQuery),function(window,$){var Pingdom,init,slideshow;return Pingdom=window.Pingdom=window.Pingdom||{},slideshow=Pingdom.slideshow=Pingdom.slideshow||{},init=Pingdom.slideshow.init=function(slides){var container,featureItems,slide,_i,_len;if(slides)for(container=$(".rslides"),_i=0,_len=slides.length;_len>_i;_i++)slide=slides[_i],container.append('<li class="slideshow-item slideshow-item-'+slide+'"></li>');return featureItems=$(".features-item"),$(".rslides").responsiveSlides({auto:!1,before:function(idx){return featureItems.removeClass("features-item-marked"),featureItems.eq(idx).addClass("features-item-marked")},speed:200}),$(".slideshow-nav").on("click",function(e){var idx;return e.preventDefault(),idx=$(this).attr("data-navto"),isNaN(parseInt(idx,10))?("previous"===idx&&(idx=-1),"next"===idx&&(idx=1),Pingdom.slideshow.slideRelative(idx)):Pingdom.slideshow.slideTo(idx)}),$(".features-list").on("mouseover",".features-item:not(.features-item-noicon)",function(e){var idx;return idx=featureItems.index(this),Pingdom.slideshow.slideTo(idx)})},null}(window,jQuery),function(window,$){var Pingdom,parallax,parallaxBottom,parallaxBoxDOM,parallaxElements,parallaxInit,parallaxIsActive,parallaxRecalc,parallaxTop,transCssName;return Pingdom=window.Pingdom=window.Pingdom||{},parallax=Pingdom.parallax=Pingdom.parallax||{},parallaxBoxDOM=null,parallaxElements=null,parallaxTop=0,parallaxBottom=0,parallaxIsActive=!1,parallaxInit=Pingdom.parallax.init=function(){var docHeight,pBoxHeight,pBoxTop,parallaxBox;return parallaxBox=$(".js-parallax-box"),parallaxBox.length?(parallaxBoxDOM=parallaxBox.get(0),parallaxElements=parallaxBox.find("[data-parallax-offset]"),Modernizr.testAllProps("transform")?navigator.userAgent.indexOf("Mobile")>=0||$(window).width()<=975?(parallaxIsActive=!1,void parallaxElements.each(function(){return $(this).css(transCssName,"")})):(parallaxIsActive=!0,pBoxTop=parallaxBox.offset().top,pBoxHeight=parallaxBox.outerHeight(),docHeight=$(document).outerHeight(),parallaxTop=pBoxTop-$(window).height(),0>parallaxTop&&(parallaxTop=0),parallaxBottom=pBoxTop+pBoxHeight,parallaxBottom>docHeight-pBoxHeight&&(parallaxBottom=docHeight-pBoxHeight),parallaxRecalc()):void(parallaxIsActive=!1)):void 0},parallaxRecalc=Pingdom.parallax.recalc=function(){var parallaxFloat,scrollTop;if(parallaxIsActive&&Pingdom.utils.inViewport(parallaxBoxDOM))return scrollTop=$(window).scrollTop(),parallaxFloat=(scrollTop-parallaxTop)/(parallaxBottom-parallaxTop)*2-1,parallaxElements.each(function(){var el,offset;return el=$(this),offset=el.attr("data-parallax-offset")*parallaxFloat>>0,el.css(transCssName,"translate(0,"+offset+"px)")})},transCssName=Modernizr.prefixed("transform"),null}(window,jQuery),function(window,$){var Pingdom,c,cEl,canvas,gauge,loadTimeMax,pEl,wEl,_isVisible;return Pingdom=window.Pingdom=window.Pingdom||{},gauge=Pingdom.gauge=Pingdom.gauge||{},loadTimeMax=6,cEl=null,pEl=null,wEl=null,canvas=null,c={},Pingdom.gauge.init=function(){return gauge=$("#gauge").gauge({value:1.2,maxValue:6,unit:"s",color:"#90dc00",animationSpeed:Pingdom.utils.isMobile?1:10}),cEl=$(".performance-container"),pEl=cEl.find(".performance-man"),wEl=$(window),canvas=cEl.find("canvas").get(0),c={t:cEl.offset().top,h:cEl.outerHeight(!0)}},Pingdom.gauge.rebase=function(){return c={t:cEl.offset().top,h:cEl.outerHeight(!0)}},Pingdom.gauge.recalc=function(){var bottom,height,loadTime;if(Pingdom.utils.inViewport(canvas))return height=wEl.height(),bottom=wEl.scrollTop()+height,loadTime=loadTimeMax*(bottom-c.t)/height,loadTime>loadTimeMax?loadTime=loadTimeMax:0>loadTime&&(loadTime=0),gauge.gauge("value",loadTime),pEl.removeClass("performance-satisfied performance-tolerating performance-frustrated"),3>loadTime?(gauge.gauge("color","#90dc00"),pEl.addClass("performance-satisfied")):4>loadTime?(gauge.gauge("color","#fecb48"),pEl.addClass("performance-tolerating")):(gauge.gauge("color","#fd4c46"),pEl.addClass("performance-frustrated"))},_isVisible=function(){var rect;return rect=canvas.getBoundingClientRect(),rect.bottom>=0&&rect.top<=(window.innerHeight||document.documentElement.clientHeight)},null}(window,jQuery),function(window,jQuery){var $,Messenger,Pingdom,gui;return $=jQuery,Pingdom=window.Pingdom=window.Pingdom||{},gui=Pingdom.gui=Pingdom.gui||{},Pingdom.gui.Messenger=Messenger=function(){function Messenger(){this.els={container:$("#message-container"),top:$("#top"),close:$("#message-close"),text:$("#message-text")},this.setUpEvents(),this.calcCssTop(),this.calcTopPadding(),this.calcScrollLimit()}return Messenger.prototype.scrollLimit=0,Messenger.prototype.topPadding=0,Messenger.prototype.window=$(window),Messenger.prototype.setUpEvents=function(){return this.els.close.on("click",this.hide.bind(this)),this.window.on("scroll",this.checkScroll.bind(this))},Messenger.prototype.calcCssTop=function(){return this.cssTop=this.els.container.css("top")||"0","auto"===this.cssTop?this.cssTop="0":void 0},Messenger.prototype.calcTopPadding=function(){return this.topPadding=0,"fixed"===this.els.top.css("position")?this.topPadding=this.els.top.outerHeight():void 0},Messenger.prototype.calcScrollLimit=function(){return this.scrollLimit=parseInt(this.cssTop.match(/^(\d+)/)[1],10)-this.topPadding},Messenger.prototype.info=function(message,options){var o;return o={timeout:-1,closeButton:!0,cssClass:""},$.extend(o,options),this.setMessage(message),this.setCssClasses(o.cssClass),this.hideAfter(o.timeout),this.setCloseButton(o.closeButton),this.checkScroll(),this.show()},Messenger.prototype.error=function(message,options){return null==options&&(options={}),options.cssClass="message-container-warning",this.info(message,options)},Messenger.prototype.setMessage=function(message){return this.els.text.text(message)},Messenger.prototype.setCssClasses=function(cssClasses){return this.els.container.removeClass().addClass("message-container "+cssClasses)},Messenger.prototype.setCloseButton=function(hasButton){return this.els.close.toggle(hasButton)},Messenger.prototype.show=function(){return this.els.container.addClass("message-visible")},Messenger.prototype.hide=function(evt){return null!=evt&&evt.preventDefault(),this.els.container.removeClass("message-visible")},Messenger.prototype.hideAfter=function(timeout){return"number"==typeof timeout&&timeout>0?setTimeout(this.hide.bind(this),timeout):void 0},Messenger.prototype.checkScroll=function(){return this.positionFixed(this.window.scrollTop()>this.scrollLimit?!0:!1)},Messenger.prototype.positionFixed=function(isFixed){return this.els.container.toggleClass("message-fixed",isFixed)},Messenger}()}(window,jQuery),function(window,$){var Events,Pingdom,components;return Pingdom=window.Pingdom=window.Pingdom||{},components=Pingdom.components=Pingdom.components||{},Pingdom.components.Events=Events=function(){function Events(){}return Events.prototype.on=function(evtType,callback){var _ref;return null!=(_ref=Pingdom.debug)&&_ref.logFunc("Events::on",arguments),this.evtList=this.evtList||{},this.evtList[evtType]=this.evtList[evtType]||[],this.evtList[evtType].push(callback)},Events.prototype.off=function(evtType){var _ref;return null!=(_ref=Pingdom.debug)&&_ref.logFunc("Events::off",arguments),this.evtList[evtType]=this.evtList[evtType]||[],this.evtList[evtType].length=0},Events.prototype.trigger=function(evtType,args){var callback,_i,_len,_ref,_ref1,_ref2,_ref3;if(null!=(_ref=Pingdom.debug)&&_ref.logFunc("Events::trigger",arguments),null!=(_ref1=this.evtList)&&null!=(_ref2=_ref1[evtType])?_ref2.length:void 0)for(_ref3=this.evtList[evtType],_i=0,_len=_ref3.length;_len>_i;_i++)callback=_ref3[_i],callback.apply(this,args)},Events.prototype.triggerAsync=function(evtType,args){var self,_ref;return null!=(_ref=Pingdom.debug)&&_ref.logFunc("Events::triggerAsync",arguments),self=this,setTimeout(function(){var callback,_i,_len,_ref1,_ref2,_ref3;if(null!=(_ref1=self.evtList)&&null!=(_ref2=_ref1[evtType])?_ref2.length:void 0)for(_ref3=self.evtList[evtType],_i=0,_len=_ref3.length;_len>_i;_i++)callback=_ref3[_i],callback.apply(self,args)},1)},Events}()}(window,jQuery),function(window,$){var Lightbox,Pingdom;return Pingdom=window.Pingdom=window.Pingdom||{},Pingdom.lightbox=Pingdom.lightbox||{},Pingdom.lightbox.Lightbox=Lightbox=function(_super){function Lightbox(data){var lightbox,name,val;for(name in data)val=data[name],this[name]=val;lightbox=this,this.container.on("click",function(evt){return lightbox.close.call(lightbox,evt)}),this.container.find(".lightbox-close").on("click",function(evt){return lightbox.close.call(lightbox,evt)}),this.triggerEl&&this.triggerEl.on("click",function(evt){return lightbox.open.call(lightbox,evt)})}return __extends(Lightbox,_super),Lightbox.prototype.open=function(evt){var shield;return null!=evt&&evt.preventDefault(),shield=this.container,shield.show(),setTimeout(function(){return shield.addClass("lightbox-open")},1),this.trigger("open",Array.prototype.slice.call(arguments,0))},Lightbox.prototype.close=function(){var shield;return shield=this.container,shield.removeClass("lightbox-open"),setTimeout(function(){return shield.hide()},100),this.trigger("close",Array.prototype.slice.call(arguments,0))},Lightbox}(Pingdom.components.Events)}(window,jQuery),function(window,$){var Pingdom,SimpleLightbox;return Pingdom=window.Pingdom=window.Pingdom||{},Pingdom.lightbox=Pingdom.lightbox||{},Pingdom.lightbox.SimpleLightbox=SimpleLightbox=function(_super){function SimpleLightbox(data){var container,lightbox;SimpleLightbox.__super__.constructor.apply(this,arguments),lightbox=this,container=this.container,this.triggerEl&&this.triggerEl.off().on("click",function(evt){var imageContent,imageHeight,imageWidth;return imageContent=$(this).find("img"),imageWidth=imageContent.width(),imageHeight=imageContent.height(),container.find(".lightbox-content")(imageContent[0].outerHTML),container.find(".lightbox-content").css({width:imageWidth+"px",height:imageHeight+"px",margin:imageHeight/2*-1+"px "+imageWidth/2*-1+"px"}),lightbox.open.call(lightbox,evt)})}return __extends(SimpleLightbox,_super),SimpleLightbox}(Pingdom.lightbox.Lightbox)}(window,jQuery),function(window,jQuery){var $,Pingdom,scroll,scrollTo;return $=jQuery,Pingdom=window.Pingdom=window.Pingdom||{},scroll=Pingdom.scroll=Pingdom.scroll||{},Pingdom.scroll.options={correction:0},Pingdom.scroll.init=function(options){return $.extend(Pingdom.scroll.options,options),$(window).on("hashchange",function(e){var arr,targetName;return targetName=window.location.hash,e.newURL&&e.newURL.split&&(arr=e.newURL.split("#"),arr&&arr.length&&arr.length>0&&(targetName=arr[1])),scrollTo(targetName)}),scrollTo(window.location.hash)},scrollTo=Pingdom.scroll.scrollTo=function(targetName,callback){var correction,offset,topEl;return"string"==typeof targetName&&"#"!==targetName&&(targetName=targetName.replace(/^#?/,"#jumpto_"),offset=$(targetName).offset(),null!=(null!=offset?offset.top:void 0))?(correction=Pingdom.scroll.options.correction,topEl=$("#top"),"fixed"===topEl.css("position")&&(correction+=topEl.outerHeight()),$("html, body").stop().animate({scrollTop:offset.top-correction},{duration:500,complete:callback}),!0):!1}}(window,jQuery),$.fn.styleSelect=function(){return this.each(function(){var css,selectEl,wrapperEl,wrapperTextEl;return selectEl=$(this),selectEl.is("select")?(wrapperEl=selectEl.parent(),wrapperEl.is(".input-select-wrapper")||(css=selectEl.get(0).className,selectEl.wrap('<div class="input-select-wrapper '+css+'"></div>'),selectEl.after('<span class="input-select-wrappertext"></span>'),wrapperEl=selectEl.parent()),wrapperTextEl=wrapperEl.find(".input-select-wrappertext"),"none"===!selectEl.css("display")&&wrapperEl.hide(),selectEl.is(":disabled")&&wrapperEl.addClass(".input-select-disabled"),wrapperTextEl.text(selectEl.find("option:selected").text()),selectEl.bind("updateText",function(){return wrapperTextEl.text(selectEl.find("option:selected").text())}),selectEl.on("change keyup",function(e){return selectEl.trigger("updateText")})):void 0})},$.fn.styleCheckbox=function(){return this.each(function(){var checkboxEl,css;return checkboxEl=$(this),checkboxEl.parent().is(".input-checkbox-wrapper")?void 0:(css=this.className,checkboxEl.wrap('<div class="input-checkbox-wrapper '+css+'"></div>'),checkboxEl.after('<label for="'+this.id+'" class="input-checkbox-wrapperlabel"></label>'))})},$.fn.styleRadio=function(){return this.each(function(){var css,radioEl;return radioEl=$(this),radioEl.parent().is(".input-radio-wrapper")?void 0:(css=this.className,radioEl.wrap('<div class="input-radio-wrapper '+css+'"></div>'),radioEl.after('<label for="'+this.id+'" class="input-radio-wrapperlabel"></label>'))})},function(window,$){var Pingdom,init,onLoad,pingdom_com;return Pingdom=window.Pingdom=window.Pingdom||{},pingdom_com=Pingdom.pingdom_com=Pingdom.pingdom_com||{},Pingdom.pingdom_com._isInited=!1,Pingdom.pingdom_com._onLoadHasRun=!1,init=Pingdom.pingdom_com.init=function(){var entModal,entModalReady,entModalTemplate,menuListEl,menuToggleEl,openPressRelease,pressReleaseModal,url;if(!Pingdom.pingdom_com._isInited)return Pingdom.utils.setHighRes($(".highres-img")),menuToggleEl=$("#menu-toggle"),menuListEl=$("#menu-list"),menuToggleEl.on("click",function(e){return e.preventDefault(),menuToggleEl.toggleClass("menu-toggle-active"),menuToggleEl.hasClass("menu-toggle-active")?(menuListEl.css("height","auto"),menuListEl.css("height",menuListEl.css("height"))):menuListEl.css("height",0)}),$("select").styleSelect(),$(".input-checkbox").styleCheckbox(),$(".input-radio").styleRadio(),url="/social/",/[?&]nocache=/.test(document.location.search)&&(url+="?nocache=yes"),$.ajax(url).done(function(data){var count,type;for(type in data)count=data[type],count=Pingdom.utils.formatNumber(count),$(".social-bubble-"+type).text(count);return $(".social-list").css("visibility","inherit")}),Pingdom.pingdom_com._isInited=!0,entModalTemplate="",entModal=null,pressReleaseModal=new Pingdom.popup.Modal({template:$("#press_release_modal"),heading:"Press Release",buttons:{"Download PDF":{callback:"onDownload",cssClass:"button-base button-primary"}},primaryButton:"Download PDF",onDownload:function(){}}),pressReleaseModal.on("close",function(){return HashState.del("press_release"),HashState.pushToBrowser()}),openPressRelease=function(which){return"20141618-solarwinds"===which?(pressReleaseModal.setHeading("Through the acquisition of Pingdom, SolarWinds adds Cloud-based website and application performance monitoring to its portfolio"),pressReleaseModal.setContent($("#solarwinds_press_release")()),pressReleaseModal.onDownload=function(){return window.location.href="http://external.pingdom.com/press/releases/20140618-solarwinds-acquisition.pdf"},pressReleaseModal.open()):void 0},$(document).ready(function(){var pressRelease;return pressRelease=decodeURI(HashState.get("press_release")),pressRelease?openPressRelease(pressRelease):void 0}),$("body").on("click",".trigger-solarwinds-popup",function(evt){return openPressRelease("20141618-solarwinds")}),$(".button-open-enterprise-contact").on("click",function(e){var entModalTemplateReq;return e.preventDefault(),$(".button-open-enterprise-contact").attr("disabled"),entModal&&entModal.open(),entModal?void 0:(entModalTemplateReq={url:"/templates/modal_enterprise_contact/"+window.location.search,type:"GET",success:function(result){var _ref;return entModalTemplate=result,entModal=null!=(_ref=Pingdom.popup)?new _ref.FixedModal({template:entModalTemplate,heading:"Pingdom – Enterprise Ready",focusOnOpen:$(window).width()>740,resizeToWindow:!1,modalScroll:!0}):void 0,entModalReady(),entModal.open()}},$.ajax(entModalTemplateReq))}),entModalReady=function(){var $sendButton,entModalData,formEls,key,map,sendText,validator,_ref;return entModalData=Pingdom.form.extract("#enterprise-modal-form"),entModal.on("open",function(evt){return entModalData&&Pingdom.form.populate("#enterprise-modal-form",entModalData,{empty:!0}),$(".input-error").removeClass("input-error"),$(".enterprise-modal-input-submit").removeClass("button-green"),$(".enterprise-modal-input-submit").removeAttr("disabled"),$(".enterprise-modal-submit-message-container").hide(),$(".enterprise-modal-submit-button-container").show(),$(".enterprise-modal-submit-button-container").css("opacity",1),$(".enterprise-modal-submit-container").removeClass("enterprise-modal-submit-sent-container")}),entModal.on("close",function(evt){var element,_i,_len,_ref;for(_ref=$("#enterprise-modal-form input, #enterprise-modal-form textarea"),_i=0,_len=_ref.length;_len>_i;_i++)element=_ref[_i],"submit"!==$(element).attr("type")&&$(element).val("");return entModal.showZopim?($zopim.livechat.window.show(),entModal.showZopim=!1):void 0}),$("#enterprise-modal-phone").typeahead({name:"country_phone_code",prefetch:"/data/phone_codes",engine:Hogan,template:"{{value}} - {{name}}",limit:10}),$(".enterprise-chat-button").hide(),$zopim&&"online"===(null!=(_ref=Pingdom.zopim)?_ref.zopimStatus:void 0)&&($(".enterprise-chat-button").show(),$(".enterprise-chat-button").on("click",function(evt){return evt.preventDefault(),entModal.showZopim=!0,entModal.close()})),validator=$("#enterprise-modal-form").validate({errorClass:"input-error",errorPlacement:function(errorEl,inputEl){var parent;return parent=inputEl.parent(),parent.is(".input")?parent.addClass("input-error"):void 0},rules:{name:"required",email:{required:!0,email:!0},website:"required",country:"required",message:"required"}}),$sendButton=$(".enterprise-modal-input-submit"),sendText=$sendButton.val(),formEls=entModal.element.find("form"),key=Pingdom["const"].KEY_CODES,map={},formEls.on("keydown.enterprise-modal",function(evt){return(evt.keyCode===key.ENTER||evt.keyCode===key.SHIFT)&&(map[evt.keyCode]=!0),evt.keyCode===key.ENTER&&($(evt.target).is("textarea")||(evt.preventDefault(),

$("#enterprise-modal-form").trigger("submit.enterprise-modal"))),map[key.SHIFT]&&map[key.ENTER]&&$(evt.target).is("textarea")?(evt.preventDefault(),$("#enterprise-modal-form").trigger("submit.enterprise-modal")):void 0}),formEls.on("keyup.enterprise-modal",function(evt){return evt.keyCode===key.ENTER||evt.keyCode===key.SHIFT?void(map[evt.keyCode]=!1):void 0}),$("#enterprise-modal-form input, #enterprise-modal-form textarea").on("keyup",function(evt){var element,filled,validationRules,_i,_len,_ref1,_ref2,_ref3;if(!document.getElementById("enterprise-modal-form"))return null;if(validationRules=null!=(_ref1=$.data(document.getElementById("enterprise-modal-form"),"validator"))&&null!=(_ref2=_ref1.settings)?_ref2.rules:void 0,!validationRules)return null;for(filled=!0,_ref3=$("#enterprise-modal-form input, #enterprise-modal-form textarea"),_i=0,_len=_ref3.length;_len>_i;_i++)element=_ref3[_i],""===$(element).val()&&validationRules[$(element).attr("name")]&&(filled=!1);return filled?$(".enterprise-modal-input-submit").addClass("button-green"):$(".enterprise-modal-input-submit").removeClass("button-green")}),$("#enterprise-modal-form").on("submit.enterprise-modal",function(evt){var data,onFailure,onSuccess,type;return evt.preventDefault(),validator.valid()?($sendButton.attr("disabled","disabled"),$(".enterprise-modal-submit-container").addClass("enterprise-modal-submit-sent-container"),url="/contact/send/",type="POST",data={name:$("#enterprise-modal-name").val(),title:$("#enterprise-modal-title").val(),email:$("#enterprise-modal-email").val(),website:$("#enterprise-modal-website").val(),country:$("#enterprise-modal-country").val(),phone:$("#enterprise-modal-phone").val(),message:$("#enterprise-modal-message").val(),skipcall:$("#enterprise-modal-skip-call").prop("checked"),subject:"Enterprise",source:"enterprise-modal"},onSuccess=function(){return $("#enterprise-modal-form").off("submit.enterprise-modal"),"object"==typeof dataLayer?dataLayer.push({event:"GAevent",eventCategory:"Enterprise Contact Form",eventAction:"Click",eventLabel:"Send Message"}):_gaq.push(["_trackEvent","Enterprise Contact Form","Click","Send Message"]),$sendButton.removeAttr("disabled"),setTimeout(function(){var orgHeight;return orgHeight=$(".enterprise-modal-submit-button-container").height(),$(".enterprise-modal-submit-button-container").css("opacity",0),setTimeout(function(){return $(".enterprise-modal-submit-button-container").hide(),$(window).width()>740&&($(".enterprise-modal-submit-message-container p").css("line-height",orgHeight+"px"),$(".enterprise-modal-submit-message-container").height(orgHeight)),$(".enterprise-modal-submit-message-container").show().removeClass("enterprise-modal-submit-container-hidden")},200),setTimeout(function(){return entModal.close()},3e3)},1e3)},onFailure=function(jqXHR){return 4===jqXHR.readyState?(data=JSON.parse(jqXHR.responseText),$(".enterprise-modal-submit-container").removeClass("enterprise-modal-submit-sent-container"),null!=(null!=data?data.errors:void 0)?(data.errors["phone-prefix"]&&(data.errors.phone="Invalid phone number",delete data.errors["phone-prefix"]),validator.showErrors(data.errors),validator.focusInvalid(),$sendButton.text(sendText),$sendButton.removeClass("button-red"),$sendButton.addClass("button-green")):($sendButton.text("Error"),$sendButton.removeClass("button-green"),$sendButton.addClass("button-red")),$sendButton.removeAttr("disabled"),$sendButton.removeClass("enterprise-modal-submit-sent-container"),$sendButton.removeClass("button-green")):void 0},$.ajax({url:url,type:type,data:data}).success(onSuccess).fail(onFailure),!1):!1})}},onLoad=Pingdom.pingdom_com.onLoad=function(){var onclick;if(!Pingdom.pingdom_com._onLoadHasRun)return Pingdom.utils.isMobile&&(onclick=$("#kayako_sitebadgecontainer").attr("onclick"),onclick&&$("#livechat-mobile").attr("onclick",onclick).css("display","block")),Pingdom.pingdom_com._onLoadHasRun=!0}}(window,jQuery),$(document).ready(Pingdom.pingdom_com.init),$(window).load(Pingdom.pingdom_com.onLoad),function(window,$){var Pingdom,clientCards,init;return Pingdom=window.Pingdom=window.Pingdom||{},clientCards=Pingdom.clientCards=Pingdom.clientCards||{},init=Pingdom.clientCards.init=function(){var cards,flipCard,wH;return wH=$(window).height(),Pingdom.clientCards.cards=cards=$(".clientcards-item"),Pingdom.clientCards.cardsFlipped=!1,cards.length&&(cards.each(function(){return $(this).addClass("clientcards-item-hidden")}),Pingdom.clientCards.cardPosY=cards.slice(0,1).offset().top),$(window).on("scroll",function(){var scrollY;return scrollY=$(window).scrollTop(),!Pingdom.clientCards.cardsFlipped&&scrollY+wH>Pingdom.clientCards.cardPosY+100?flipCard(0,14):void 0}),flipCard=function(cardIndex,max){var card;return Pingdom.clientCards.cardsFlipped=!0,card=cards.slice(cardIndex,cardIndex+1),card.removeClass("clientcards-item-hidden"),max>cardIndex?setTimeout(flipCard,100,cardIndex+1,max):void 0},$(".clientcards-toggle").on("click",function(evt){var state;return evt.preventDefault(),$("#clientcards-list").toggleClass("clientcards-list-expanded"),state=$("#clientcards-list").hasClass("clientcards-list-expanded")?"2":"1",$(".clientcards-toggle").text($(".clientcards-toggle").attr("data-text-"+state)),flipCard(15,30)})}}(window,jQuery),Pingdom.popup.FixedModal=FixedModal=function(_super){function FixedModal(data){var defaultData,_ref;null!=(_ref=Pingdom.debug)&&_ref.logFunc("popup.ConfirmModal::constructor",arguments),defaultData={},data=$.extend(defaultData,data),FixedModal.__super__.constructor.call(this,data),this.modalEl=this.element.find(".popup-modal"),this.element.on("click",function(_this){return function(evt){return $(evt.target).hasClass("popup-fixed-modal-wrapper")?_this.close():void 0}}(this))}return __extends(FixedModal,_super),FixedModal.prototype.open=function(){var _ref;return null!=(_ref=Pingdom.debug)&&_ref.logFunc("popup.FixedModal::open",arguments),$("body, html").addClass("noscroll"),FixedModal.__super__.open.apply(this,arguments)},FixedModal.prototype.close=function(){var _ref;return null!=(_ref=Pingdom.debug)&&_ref.logFunc("popup.FixedModal::close",arguments),$("body, html").removeClass("noscroll"),FixedModal.__super__.close.apply(this,arguments)},FixedModal.prototype.center=function(){var h,w,_ref;return null!=(_ref=Pingdom.debug)&&_ref.logFunc("popup.FixedModal::center",arguments),this.modalEl?(h=this.modalEl.outerHeight(),w=this.modalEl.outerWidth(),this.modalEl.css({"margin-top":Math.max(0,($(window).height()-h)/2)})):void 0},FixedModal}(Pingdom.popup.Modal)}).call(this);