/* -------------------------------------------

main

------------------------------------------- */
*,
*:before,
*:after {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

body {
    font-family: $font-2;
    color: $m1;
    font-weight: 300;
    font-size: 14px;
}

.mil-page-wrapper {
    position: relative;
    overflow: hidden;
}

/* -------------------------------------------

settings

------------------------------------------- */

.mil-relative {
    position: relative;
    z-index: 1;
}

.mil-df {
    display: flex;
}

.mil-aic {
    display: flex;
    align-items: center;
}

.mil-aie {
    display: flex;
    align-items: flex-end;
}

.mil-ais {
    display: flex;
    align-items: flex-start;
}

.mil-jcc {
    display: flex;
    justify-content: center;
}

.mil-jce {
    display: flex;
    justify-content: flex-end;
}

.mil-jcs {
    display: flex;
    justify-content: flex-start;
}

.mil-jcb {
    display: flex;
    justify-content: space-between;
}

.mil-tac {
    text-align: center;
}

.mil-tar {
    text-align: right;
}

@media (max-width: 992px) {
    .mil-992-jcs {
        display: flex;
        justify-content: flex-start;
    }

    .mil-992-jcc {
        display: flex;
        justify-content: center;
    }

    .mil-992-tac {
        text-align: center;
    }

    .mil-992-tal {
        text-align: left;
    }
}

@media (max-width: 768px) {
    .mil-768-jcc {
        display: flex;
        justify-content: center;
    }

    .mil-768-tac {
        text-align: center;
    }

    .mil-768-tal {
        text-align: left;
    }
}

/* -------------------------------------------

typography

------------------------------------------- */
h1,
h2,
h3,
h4,
h5,
h6,
.mil-h1{
    font-family: $font-1;
    color: $m1;
    font-weight: 400;
    line-height: normal;
    color: $m1;
}

.mil-fs14 {
    font-size: 14px;

    @media (max-width: 768px) {
        font-size: 12px;
    }
}

.mil-fs16 {
    font-size: 16px;

    @media (max-width: 768px) {
        font-size: 14px;
    }
}

.mil-fs18 {
    font-size: 18px;

    @media (max-width: 768px) {
        font-size: 16px;
    }
}

.mil-fs20 {
    font-size: 20px;

    @media (max-width: 768px) {
        font-size: 18px;
    }
}

.mil-fs24 {
    font-size: 24px;

    @media (max-width: 768px) {
        font-size: 22px;
    }
}

.mil-fs90 {
    font-size: calc(1rem + 3.5vw);
}

.mil-light {
    color: $m4;
}

.mil-dark {
    color: $m1;
}

.mil-soft {
    color: $m2;
}

.mil-accent {
    color: $a;
}

.mil-upper {
    text-transform: uppercase;
}

.mil-shortened {
    width: 80%;
}

.mil-text-bg {
    background-color: $m3;
    padding-right: 20px;
}

.mil-text-link {
    color: $a;
    position: relative;

    &:before {
        content: '';
        width: 0;
        height: 1px;
        background-color: $a;
        position: absolute;
        bottom: 2px;
        transition: $tsm;
    }

    &:hover {
        &:before {
            width: 100%;
        }
    }
}

a {
    text-decoration: none;
    color: inherit;
}

.mil-suptitle {
    color: $m1;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 2px;
}

blockquote {
    padding: 30px 60px;
    border-left: solid 2px $a;
}
/* -------------------------------------------

social

------------------------------------------- */

.mil-social {
    display: flex;
    position: relative;

    & li {
        list-style-type: none;
        margin-right: 30px;

        & a {
            text-transform: uppercase;
            font-size: 14px;
            font-weight: 400;
            color: $m2;
            transition: $tsm;

            &:hover {
                color: $m1;
            }
        }

        &:last-child {
            margin-right: 0;
        }
    }

    @media (max-width: 992px) {
        display: none;
    }
}

/* -------------------------------------------

slider

------------------------------------------- */
.mil-swiper-pagination,
.mil-swiper-pagination-2 {
    font-size: 16px;
    padding-top: 60px;
    color: $m1;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 2px;
}

/* -------------------------------------------

cursor

------------------------------------------- */
.mil-cursor-follower {
    margin-top: -1.4%;
    margin-left: -.9%;
    transform: scale(1);
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    background-color: rgba($m1, 0.1);
    width: 30px;
    height: 30px;
    border-radius: 100%;
    user-select: none;
    pointer-events: none;
    z-index: 9999999;
    transition: transform 0.4s cubic-bezier(0.75, -1, 0.3, 2.33), background-color $tmd;

    &:after {
        content: 'iiiiii';
        min-height: 7px;
        min-width: 50px;
        text-align: center;
        border-radius: 10px;
        position: absolute;
        opacity: 0;
        transform: scale(0);
        text-transform: uppercase;
        font-size: 14px;
        font-weight: 400;
        letter-spacing: 2px;
        color: $m1;
        transition: $tsm;
        transition-delay: 0s;
    }

    &.mil-light-active,
    &.mil-dark-active,
    &.mil-gone-active,
    &.mil-view-active,
    &.mil-next-active,
    &.mil-read-active,
    &.mil-swipe-active {
        background-color: $a;
        transform: scale(3);
        filter: drop-shadow(0px 2px 4px rgba($m1, 0.1));

        &:after {
            transition-delay: .2s;
            opacity: 1;
            transform: scale(.33);
        }
    }

    &.mil-light-active {
        background-color: $m4;

        &:after {
            color: $m1;
        }
    }


    &.mil-dark-active {
        background-color: $m1;

        &:after {
            color: $m4;
            filter: invert(80%);
        }
    }

    &.mil-gone-active {
        background-color: rgba($m4, 0.1);
        transform: scale(0);

        &:after {
            content: '.';
        }
    }

    &.mil-next-active {
        &:after {
            content: 'next';
        }
    }


    &.mil-view-active {
        &:after {
            content: 'view';
        }
    }

    &.mil-read-active {
        &:after {
            content: 'read';
        }
    }

    &.mil-swipe-active {
        &:after {
            content: url('data:image/svg+xml,<svg width="31" height="31" viewBox="0 0 31 31" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M29.4364 14.3667C29.4101 14.342 29.3824 14.3188 29.3535 14.2972L24.653 9.59663C24.5636 9.50467 24.4568 9.4314 24.3389 9.38106C24.2209 9.33073 24.0941 9.30434 23.9659 9.30344C23.8376 9.30253 23.7105 9.32712 23.5918 9.37578C23.4732 9.42444 23.3654 9.4962 23.2747 9.58689C23.184 9.67757 23.1122 9.78538 23.0636 9.90404C23.0149 10.0227 22.9903 10.1498 22.9912 10.2781C22.9921 10.4063 23.0185 10.5331 23.0688 10.6511C23.1192 10.7691 23.1925 10.8758 23.2844 10.9652L26.4226 14.1034L3.72315 14.1034L6.86129 10.9652C6.9541 10.8749 7.02784 10.7669 7.07815 10.6476C7.12846 10.5283 7.15432 10.4001 7.1542 10.2706C7.15408 10.1411 7.12797 10.013 7.07743 9.89378C7.02688 9.77456 6.95293 9.6667 6.85996 9.57659C6.67587 9.39817 6.42852 9.30007 6.17218 9.30383C5.91585 9.30759 5.67147 9.41289 5.4927 9.59663L0.806869 14.2825C0.68112 14.372 0.578597 14.4902 0.507853 14.6274C0.43711 14.7646 0.400197 14.9167 0.400197 15.071C0.400197 15.2253 0.437109 15.3775 0.507853 15.5146C0.578596 15.6518 0.68112 15.7701 0.806869 15.8596L5.4927 20.5454C5.58209 20.6373 5.68888 20.7106 5.80684 20.761C5.9248 20.8113 6.05158 20.8377 6.17983 20.8386C6.30808 20.8395 6.43522 20.8149 6.55389 20.7662C6.67255 20.7176 6.78035 20.6458 6.87104 20.5551C6.96173 20.4644 7.03348 20.3566 7.08214 20.238C7.1308 20.1193 7.15539 19.9922 7.15449 19.8639C7.15358 19.7357 7.12719 19.6089 7.07686 19.4909C7.02653 19.373 6.95325 19.2662 6.86129 19.1768L3.72315 16.0386L26.4226 16.0386L23.2844 19.1768C23.1925 19.2662 23.1192 19.373 23.0688 19.4909C23.0185 19.6089 22.9921 19.7357 22.9912 19.8639C22.9903 19.9922 23.0149 20.1193 23.0636 20.238C23.1122 20.3566 23.184 20.4644 23.2747 20.5551C23.3654 20.6458 23.4732 20.7176 23.5918 20.7662C23.7105 20.8149 23.8376 20.8395 23.9659 20.8386C24.0941 20.8377 24.2209 20.8113 24.3389 20.761C24.4568 20.7106 24.5636 20.6373 24.653 20.5454L29.3562 15.8422C29.4684 15.7573 29.5607 15.649 29.6267 15.5248C29.6927 15.4006 29.7309 15.2635 29.7385 15.123C29.7461 14.9826 29.7231 14.8421 29.6709 14.7115C29.6187 14.5809 29.5387 14.4632 29.4364 14.3667Z" fill="black"/></svg>');
            margin-top: 1px;
        }
    }

    @media (max-width: 768px) {
        display: none;
    }
}

.mil-c-swipe {
    cursor: grab;
}

/* -------------------------------------------

scrollbar

------------------------------------------- */
::-webkit-scrollbar {
    display: none;
}

.mil-progress-track {
    position: fixed;
    z-index: 999999999999;
    top: 0;
    right: 0;
    height: 100%;
    width: 5px;
    background-color: $m3;

    & .mil-progress {
        background-color: $a;
        height: 0;
        width: 5px;
        border-radius: 4px;
        transition: $tmd;
    }

    @media screen and (max-width: 768px) {
        display: none;
    }
}

/* -------------------------------------------

pagination

------------------------------------------- */
.mil-pagination {
    margin-top: 60px;
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &:before {
        content: '';
        height: 1px;
        width: 100%;
        background-color: rgba($m2, .3);
        position: absolute;
        top: 50%;
    }

    & ul {
        display: flex;
        align-items: center;

        & li {
            margin-right: 10px;
            list-style-type: none;

            &:last-child {
                margin-right: 0;
            }
        }
    }

    & a {
        position: relative;
        background-color: $m3;
        display: block;
        border: solid 1px rgba($m2, .3);
        height: 60px;
        width: 60px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        color: rgba($m2, .3);
        font-weight: 400;
        text-transform: uppercase;
        letter-spacing: 2px;
        transition: $tmd;

        &:hover {
            border: solid 1px $m1;
            color: $m1;
            transform: scale(1.03);
        }

        &.mil-active {
            border: solid 1px $a;
            color: $m4;
            background-color: $a;
            pointer-events: none;
        }

        &.mil-pag-arrow {
            border: solid 1px $m1;
            color: $m1;

            &.mil-next {
                transform: rotate(180deg);

                &:hover {
                    transform: rotate(180deg) scale(1.03);
                }
            }
        }

        &.mil-all {
            width: auto;
            border-radius: 60px;
            padding: 0 40px;
            border: solid 1px $m1;
            color: $m1;
        }
    }

    @media (max-width: 550px) {
        & ul {
            display: none;
        }

        & a {
            &.mil-all {
                display: none;
            }
        }
    }
}

/* -------------------------------------------

filter

------------------------------------------- */

.mil-filter {
    display: flex;
    align-items: center;
    justify-content: center;

    & li {
        list-style-type: none;

        &:after {
            content: '/';
            margin: 0 15px;
            color: rgba($m2, .3);
        }

        &:last-child:after {
            display: none;
        }

        & a {
            color: $m1;
            font-weight: 400;
            text-transform: uppercase;
            letter-spacing: 2px;
            font-size: 14px;
            transition: $tsm;

            &:hover {
                color: $a;
            }

            &.mil-active {
                color: $a;
            }
        }
    }

    @media (max-width: 550px) {
        & li {
            &:after {
                margin: 0 10px;
            }

            & a {
                font-size: 12px;
            }
        }
    }
}

/* -------------------------------------------

form

------------------------------------------- */

form {

    & input,
    & textarea {
        padding: 0 15px;
        width: 100%;
        background-color: transparent;
        height: 50px;
        border: none;
        border-bottom: solid 1px $m1;
        font-size: 16px;
        font-family: $font-2;
        font-weight: 300;
        color: $m1;
        transition: $tsm;

        &::placeholder {
            color: $m2;
            opacity: 1;
            font-size: 16px;
            font-family: $font-2;
            font-weight: 300;
            transition: $tsm;
        }

        &:focus {
            outline: inherit;
            border-bottom: solid 1px $a;

            &::placeholder {
                color: rgba($m2, .3);
            }
        }
    }

    & textarea {
        height: auto;
    }
}

/* -------------------------------------------

buttons

------------------------------------------- */

.mil-btn {
    border: none;
    cursor: pointer;
    border-radius: 60px;
    padding: 0 40px;
    height: 60px;
    font-family: $font-2;
    background-color: $a;
    color: $m4;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    font-size: 16px;
    font-weight: 400;
    line-height: 100%;
    padding-bottom: 3px;
    white-space: nowrap;
    transition: $tmd;

    &.mil-btn-lg {
        height: 90px;
        padding: 0 70px;
        font-size: 16px;
    }

    &.mil-btn-border {
        border: solid 2px $m1;
        background-color: transparent;
        color: $m1;

    }

    &.mil-btn-soft {
        background-color: $a;
        color: $m1;
    }

    &.mil-btn-link {
        background-color: transparent;
        padding: 0;
        color: $m4;

        &.mil-accent {
            color: $a;
        }
    }

    & i {
        margin-left: 10px;
        transition: $tmd;
    }

    &:hover {
        transform: scale(1.03);
        filter: brightness(110%);

        & i {
            margin-left: 15px;
        }
    }
}

/* -------------------------------------------

spaces

------------------------------------------- */

.mil-divider {
    height: 1px;
    width: 100%;
    background-color: rgba($m2, .3);
}

.mil-mb10 {
    margin-bottom: 10px;
}

.mil-mb30 {
    margin-bottom: 30px;
}

.mil-mb40 {
    margin-bottom: 40px;
}

.mil-mb50 {
    margin-bottom: 50px;
}

.mil-mb60 {
    margin-bottom: 60px;
}

.mil-mb90 {
    margin-bottom: 90px;
}

.mil-p-90-90 {
    padding-top: 90px;
    padding-bottom: 90px;
}

.mil-p-0-90 {
    padding-bottom: 90px;
}

.mil-p-90-0 {
    padding-top: 90px;
}

.mil-p-90-60 {
    padding-top: 90px;
    padding-bottom: 60px;
}

.mil-p-90-30 {
    padding-top: 90px;
    padding-bottom: 30px;
}

.mil-p-0-30 {
    padding-bottom: 30px;
}

@media (max-width: 1200px) {

    .mil-1200-p-90-30 {
        padding-top: 90px;
        padding-bottom: 30px;
    }

    .mil-1200-p-30-90 {
        padding-top: 30px;
        padding-bottom: 90px;
    }
}
