@import url('../css2');

@import url('../css2-1');

@import url('../css2-2');

@font-face {

font-family: TT-Bold;

src: url('../fonts/TTNorms-Bold.otf');

}

@font-face {

font-family: TT-EBold;

src: url('../fonts/TTNorms-ExtraBold.otf');

}

@font-face {

font-family: TT-Light;

src: url('../fonts/TTNorms-Light.otf');

}

@font-face {

font-family: TT-Regular;

src: url('../fonts/TTNorms-Regular.otf');

}

@font-face {

font-family: TT-Medium;

src: url('../fonts/TTNorms-Medium.otf');

}

@font-face {

font-family: Mailx;

src: url('../fonts/Mistrully.ttf');

}

.bg-light{

background-color: #ffffff !important;

}

.navbar{

box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 2px 0px;

position: absolute;

z-index: 1;

top: 0;

}

.wave-banner{

padding-top: 120px;

padding-bottom: 100px;

}

.wave-banner .img-section{

padding-top: 40px;

}

.navbar-brand{

padding-left: 40px;

}

.cus-button .btn-mar{

margin-left: 20px;

}

.text-section h1{

font-family: TT-Bold;

font-size: 60px !important;

color: #141E46;

}

.text-section h6{

font-family: TT-Bold;

color: #141E46;

}

.text-section button{

width: auto;

}

.text-section p{

font-family: 'Inter', sans-serif;

font-size: 20px;

color: #141E46;

}

.text-section2{

background-color: white;

padding: 20px;

}

.text-section2 h2{

font-family: TT-Medium;

background-color: #034ea2;

width: fit-content;

padding: 4px;

padding-left: 20px;

padding-right: 20px;

color: white;

}

.text-section2 p{

font-family: 'Inter', sans-serif;

font-size: 20px;

color: #0d1432;

text-align: justify;

}

.text-section3{

background-color: white;

padding: 20px;

}

.text-section3 h2{

font-family: TT-Medium;

background-color: #034ea2;

padding: 4px;

padding-left: 20px;

padding-right: 20px;

width: fit-content;

color: white;

}

.text-section3 p{

font-family: 'Inter', sans-serif;

font-size: 20px;

color: #0d1432;

text-align: justify;

}

.btn-dark {

color: #fff;

background-color: #034ea2 !important;

border-color: #034ea2 !important;

}

.btn {

color: #ffffff !important;

font-family: TT-Medium !important;

}

.featurs-title{

font-family: TT-Bold;

font-size: 60px;

margin-top: 120px;

}

.featurs{

padding-top: 60px;

background-color: #0d1432; 

background-image: url('../images/back.png');

background-size: cover;

padding-bottom: 60px;

opacity:2.7;

}

.featurs2{

padding-top: 60px;

background-color: #0d1432;

background-image: url('../images/back.png');

padding-bottom: 60px;

}

.featurs .row{

justify-content: center;

}

.All-features{

margin-top: 80px;

margin-bottom: 50px;

}

.All-features .card-text{

font-family: 'Inter', sans-serif;

color: rgb(48, 48, 48);

text-align: justify;

}

.All-features .card-title{

font-family: TT-Medium;

color: white;

padding: 6px;

padding-right: 20px;

background-color: #034ea2;

width: fit-content;

}

.All-features .card{

box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;

border-bottom: 4px solid #1380c3;

border-top: none !important;

border-left: none !important;

border-right: none !important;

}

.last-con{

padding-top: 100px;

padding-bottom: 100px;

}

.last-con h1{

font-size: 50px;

font-family: TT-Bold;

}

.last-con p{

font-size: 20px;

font-family: 'Inter', sans-serif;

}

.last-con button{

margin-top: 20px;

}

.bg-footer{

background-color: #0d1432;

}

footer h5{

font-family: TT-Medium;

color: #1380c3;

}

footer li{

color: white;

font-family: 'Inter', sans-serif;

}

.Mailx{

font-family: Mailx; 

color: #034ea2;

font-size: 20px;

margin-left: 10px;

}

.Mailx2{

font-family: Mailx; 

color: #034ea2;

font-size: 20px;

}

/* ================== Media query start ================== */

@media (max-width: 1920px) {

.card.mb-4 {

min-height: 350px;

}

.rule-based {

min-height: 280px;

}

}

@media (max-width: 1366px) {

.card.mb-4 {

min-height: 400px;

}

.rule-based {

min-height: 304px;

}

}

@media (max-width: 1280px) {

.last-con h1 {

font-size: 54px;

}

}

@media (max-width: 1080px) {

.card.mb-4 {

min-height: auto;

}

.rule-based {

min-height: auto;

}

}

@media (max-width: 1024px) {

.last-con h1 {

font-size: 44px;

}

}

@media (max-width: 912px){

.All-features .row{

display: block;

}

.All-features .row .col-md-4{

width: auto;

}

.All-features .row .col-md-4 .card{

height: auto;

}

.All-features .col-sm-6{

width: auto;

}

.last-con h1{

font-size: 40px;

}

}

@media (max-width: 820px) {

.All-features .row{

display: block;

}

.All-features .row .col-md-4{

width: auto;

}

.All-features .row .col-md-4 .card{

height: auto;

}

.All-features .col-sm-6{

width: auto;

}

.featurs2 .row{

display: block;

}

.featurs2 .row .col-md-4{

width: auto;

}

.last-con h1{

font-size: 36px;

}

.featurs .row{

display: block;

}

.featurs .row .col-md-4{

width: auto;

}

.featurs .col-md-6{

margin-left: auto;

margin-right: auto;

}

}

@media (max-width: 768px) {

.text-section h1 {

font-size: 40px !important;

}

.text-section p {

font-size: 16px;

}

.last-con h1 {

font-size: 40px;

}

.last-con p {

font-size: 16px;

}

.featurs .col-md-6{

margin-left: auto;

margin-right: auto;

}

}

@media (max-width: 576px) {

.text-section h1 {

font-size: 26px !important;

}

.text-section p {

font-size: 10px;

}

.last-con h1 {

font-size: 24px;

}

.last-con p {

font-size: 14px;

}

.card.mb-4 {

min-height: auto;

}

.last-con {

padding-top: 20px;

padding-bottom: 20px;

}

.cus-button .btn-mar{

margin-left: 0px;

margin-top: 10px;

}

.All-features .row .col-md-4 .card{

height: auto;

}

}

@media (max-width: 480px) {

.rule-based {

min-height: auto;

}

}

@media (max-width: 280px) {

.text-section h1 {

font-size: 20px !important;

}

.last-con h1 {

font-size: 12px;

}

}

/* ================== Media query end ================== */

.container-fluid2 {

width: 90%;

padding-right: var(--bs-gutter-x,.75rem);

padding-left: var(--bs-gutter-x,.75rem);

margin-right: auto;

margin-left: auto;

}

.list-unstyled li {

padding-top: 5px;

}

.list-unstyled li a {

color: #ffffff;

text-decoration: none;

font-size: 14px;

}

.list-unstyled li a:hover {

color: #009eff;

}

.form-group{

margin-top: 10px;

margin-bottom: 10px;

}

.form-group label{

color: #0c4da2;

font-weight: 600;

}

.form-group input{

border-radius: 4px !important;

}

.form-group textarea{

border-radius: 4px;

}

.my-form{

    

padding: 20px;

box-shadow: rgba(17, 17, 26, 0.05) 0px 1px 0px, rgba(17, 17, 26, 0.1) 0px 0px 8px;

border-radius: 4px;

}