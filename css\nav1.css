

/* Clearfix */

.clearfix:before, .clearfix:after {

	content: " ";

	display: table;

}

.clearfix:after {

	clear: both;

}

.clearfix {

 *zoom: 1;

}



@import url(../css-1);



nav1 {

	font-size: 14px;

	color: #fff!important;

	font-weight: 500;

	margin:0px;

	height: auto;

	z-index: 99999;

	position: relative;

	float:left;

}

nav1 ul {

	padding: 0;

	width:100%;

	float:left;

	height: auto;

	margin-right:10px;

}

nav1 li {

	display: inline;

	float: left;

	line-height:15px;

	text-decoration:none;

	

}

nav1 a {

	color: #fff!important;

	display: inline-block;

	width: auto;

	text-align: center;

	text-decoration: none;

	

}

nav1 li:last-child {

	border-right:0px;

}

nav1 li a {

	box-sizing: border-box;

	-moz-box-sizing: border-box;

	-webkit-box-sizing: border-box;

	padding: 0px 15px;

	text-decoration:none !important;

}

nav1 a:hover {

	/*-webkit-border-top-left-radius: 8px;

	-webkit-border-top-right-radius: 8px;

	-moz-border-radius-topleft: 8px;

	-moz-border-radius-topright: 8px;

	border-top-left-radius: 8px;

	border-top-right-radius: 8px;*/

	color: #175e9a;

	text-decoration:none !important;

}

nav1 a.active {

	color: #175e9a;

	font-weight:700;

	text-decoration:none !important;

}

nav1 a#pull {

	display: none;

}



/*Styles for screen 600px and lower*/

/*Styles for screen 600px and lower*/

@media screen and (max-width:800px) {

nav1 {

	height: auto;

}

nav1 ul {

	display: block;

	height: auto;

	text-align: center;

}

}

@media screen and (max-width:768px) {

nav1 {

	height: auto;

}

nav1 ul {

	display: block;

	height: auto;

}

}

@media screen and (max-width:720px) {

nav1{

	height: auto;

}

nav1 ul {

	display: block;

	height: auto;

}

}

@media only screen and (max-width :640px) {

nav1 {

	border-bottom: 0;

	margin:0px;

}

nav1 ul {

	display: none;

	height: auto;

	margin: 0;

	background-color:#1f8ccb;

	text-align:left;

}

nav1 li {

	width: 100%;

	float: left;

	position: relative;

}

nav1 li a {

	border-bottom: 1px solid #fff;

	border-right: 1px solid #fff;

	padding:12px;

	color: #FFF!important;

	width:100%;

	text-align:left;

	border-radius:none;

}

nav1 a#pull {

	display: block;

	width: 100%;

	position: relative;

	background-color:#0f58ab;

}

nav1 a#pull:after {

	content: "";

	background:url(../images/nav-icon.png) no-repeat;

	width: auto;

	height: auto;

	display: inline-block;

	position: absolute;

	right: 15px;

	background-color:#0f58ab;

	padding: 19px;

    background-position: center 8px;

}

nav1 a:hover, nav a.active {

	background-color: #FF0000;

	color: #FFF;

	border-radius:none;

	border-top-left-radius:0px!important;

	border-top-right-radius:0px!important;

}

}

@media screen and (max-width: 600px) {

nav1 {

	border-bottom: 0;

	margin:0px;

}

nav1 ul {

	display: none;

	height: auto;

	margin: 0;

	background-color:#1f8ccb;

	text-align:left;

}

nav1 li {

	width: 100%;

	float: left;

	position: relative;

}

nav1 li a {

	border-bottom: 1px solid #fff;

	border-right: 1px solid #fff;

	padding:12px;

	color: #FFF!important;

	width:100%;

	text-align:left;

	border-radius:none;

}

nav1 a#pull {

	display: block;

	width: 100%;

	position: relative;

	background-color:#0f58ab;

}

nav1 a#pull:after {

	content: "";

	background:url(../images/nav-icon.png) no-repeat;

	width: auto;

	height: auto;

	display: inline-block;

	position: absolute;

	right: 15px;

	background-color:#0f58ab;

	padding: 19px;

    background-position: center 8px;

}

nav1 a:hover, nav a.active {

	background-color: #FF0000;

	color: #FFF;

	border-radius:none;

	border-top-left-radius:0px!important;

	border-top-right-radius:0px!important;

}

}



/*Styles for screen 515px and lower*/

@media only screen and (max-width : 480px) {

nav1 {

	border-bottom: 0;

	margin:0px;

}

nav1 ul {

	display: none;

	height: auto;

	margin: 0;

	background-color:#1f8ccb;

	text-align:left;

	z-index:9999;

}

nav1 li {

	width:100%;

	float: left;

	position: relative;

}

nav1 li a {

	border-bottom: 1px solid #fff;

	border-right: 1px solid #fff;

	padding:12px;

	color: #FFF!important;

	width:100%;

	text-align:left;

	border-radius:none;

	margin-left:15px;

}

nav1 a#pull {

	display: block;

	width: 100%;

	position: relative;

	background-color:#0f58ab;

}

nav1 a#pull:after {

	content: "";

	background:url(../images/nav-icon.png) no-repeat;

	width: auto;

	height: auto;

	display: inline-block;

	position: fixed;

	right: 5px !important;

	background-color:#0f58ab;

	padding: 19px;

    background-position: center 8px;

}

nav1 a:hover, nav a.active {

	background-color: #FF0000;

	color: #FFF;

	border-radius:none;

	border-top-left-radius:0px!important;

	border-top-right-radius:0px!important;

}



}



/*Smartphone*/

@media only screen and (max-width : 320px) {

nav1 li {

	display: block;

	float: none;

	width: 100%;

	font-size:16px;

	padding-left:0px;

}

nav1 li a {

	border-bottom: 1px solid #eaeaea;

}

}



/*Styles for screen 515px and lower*/





/*Smartphone*/

@media only screen and (max-width : 320px) {

nav1 li {

	display: block;

	float: none;

	width: 100%;

	font-size:16px;

	padding-left:0px;

}

nav1 li a {

	border-bottom: 1px solid #eaeaea;

}

}



/*Smartphone*/

@media only screen and (max-width : 360px) {

nav1 li {

	display: block;

	float: none;

	width: 100%;

	font-size:16px;

}

nav1 li a {

	border-bottom: 1px solid #eaeaea;

}

}

