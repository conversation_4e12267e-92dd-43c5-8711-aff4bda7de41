.dropdown-column {
	padding: 0 15px; /* Adjust spacing as needed */

	width: fit-content !important;
}

.dropdown-column li a {
	color: rgb(96, 96, 96) !important;
}

.dropdown-column li h6 {
	color: rgb(96, 96, 96) !important;
}

.dropdown-column h6 {
	color: rgb(96, 96, 96) !important;
}

.dropdown-toggle::after {
	display: none;

	margin-left: 0.255em;

	vertical-align: 0.255em;

	content: '';

	border-top: 0.3em solid;

	border-right: 0.3em solid transparent;

	border-bottom: 0;

	border-left: 0.3em solid transparent;
}

.fa {
	padding-top: 0px !important;
}

.pro {
	color: rgb(96, 96, 96) !important;
}

.dropdown-item:focus,
.dropdown-item:hover {
	color: #ffffff !important;

	background-color: rgb(50, 165, 232) !important;
}

.navbar-light .navbar-toggler {
	border-color: white !important;
}

.navbar-toggler {
	background-color: white !important;
}

.dropdown-item-column {
	margin-bottom: 10px; /* Adjust the spacing between items in a column */
}

.dropdown-column {
	padding: 0px 24px !important;

	padding-right: 10px !important;
}

.bor {
	border-right: 0px !important;
}

.dropdown-column-cus {
	padding: 0px 0px !important;
}

.dropdown-column .list {
	list-style: none;

	color: white;
}

.dropdown-column li a {
	font-size: small !important;
}

.dropdown-column h6 a {
	text-decoration: none;

	color: rgb(96, 96, 96) !important;
}

hr:not([size]) {
	height: 3px !important;

	background-color: #218ecd !important;
}

.dropdown-for-service {
	min-width: 48rem !important;

	left: -260px;

	background-color: darkgoldenrod !important;
}

.dropdown-for-product {
	min-width: 14rem !important;

	color: rgb(96, 96, 96) !important;

	font-size: small !important;
}

.dropdown-menu {
	background-color: rgb(255, 255, 255) !important;

	width: 100px;
}

.dropdown:hover > .dropdown-menu {
	display: block;
}

.dropdown > .dropdown-toggle:active {
	pointer-events: none;
}

.navbar-nav {
	/* background-color: rgba(30,147,215,0.8) !important; */

	border-radius: 0 0 10px 10px;

	padding-left: 10px;

	padding-right: 10px;

	margin-right: 26px;
}

.navbar {
	padding: 0 !important;
}

.nav-link {
	color: rgb(96, 96, 96) !important;

	font-weight: 500;

	text-transform: uppercase;

	font-size: 13px;

	font-style: bold;

	letter-spacing: 2px;

	text-decoration: none !important;
}

.nav-link:hover {
	background-color: #218ecd;

	color: white !important;

	text-decoration: underline !important;

	text-underline-offset: 4px;
}

.navbar-brand {
	margin-left: 60px;

	padding: 12px;
}

.navbar-brand img {
	height: 60px;
}

.bg-light {
	background-color: rgb(255, 255, 255) !important;
}

.serviceoffering .heading {
	background-image: url(../images/system-consulting-Integration1.svg);

	background-position: center;

	background-repeat: no-repeat;

	padding: 20px;
}

.serviceoffering .heading:hover {
	color: white;

	background-image: url(../images/system-consulting-Integration2.svg);

	background-position: center;

	background-repeat: no-repeat;
}

.index-cards .index-con .card:hover {
	color: white !important;

	background-color: #006c9a;
}

.index-cards .index-con .img1 h5 {
	margin-top: 130px;
}

.index-cards .index-con a {
	text-decoration: none;

	color: black !important;
}

.index-cards .index-con a:hover {
	color: white !important;
}

.index-cards .index-con .img1 {
	background-image: url('../images/cx-automation-operations-col.svg');

	background-repeat: no-repeat;

	background-position: center 30%;

	width: 18rem;

	padding: 10px;

	border-radius: 10px;

	max-height: 220px;
}

.index-cards .index-con .img1:hover {
	background-image: url('../images/cx-automation-operations.svg');

	background-repeat: no-repeat;

	background-position: center 30%;
}

.index-cards .index-con .img2 h5 {
	margin-top: 120px;
}

.index-cards .index-con .img2 {
	background-image: url('../images/aida-col.svg');

	background-repeat: no-repeat;

	background-position: center 30%;

	width: 18rem;

	padding: 20px;

	border-radius: 10px;

	max-height: 220px;
}

.index-cards .index-con .img2:hover {
	background-image: url('../images/aida.svg');

	background-repeat: no-repeat;

	background-position: center 30%;
}

.index-cards .index-con .img3 h5 {
	margin-top: 120px;
}

.index-cards .index-con .img3 {
	background-image: url('../images/it-cloud-operations-col.svg');

	background-repeat: no-repeat;

	background-position: center 30%;

	width: 18rem;

	padding: 20px;

	border-radius: 10px;

	max-height: 220px;
}

.index-cards .index-con .img3:hover {
	background-image: url('../images/it-cloud-operations.svg');

	background-repeat: no-repeat;

	background-position: center 30%;
}

.index-cards .index-con .img4 {
	background-image: url('../images/cx-digital-col.svg');

	background-repeat: no-repeat;

	background-position: center 30%;

	width: 18rem;

	padding: 20px;

	border-radius: 10px;

	max-height: 220px;
}

.index-cards .index-con .img4:hover {
	background-image: url('../images/cx-digital.svg');

	background-repeat: no-repeat;

	background-position: center 30%;
}

.index-cards .index-con .img5 {
	background-image: url('../images/ai-icon-col.svg');

	background-repeat: no-repeat;

	background-position: center 30%;

	width: 18rem;

	padding: 20px;

	border-radius: 10px;

	max-height: 220px;
}

.index-cards .index-con .img5:hover {
	background-image: url('../images/ai-icon-white.svg');

	background-repeat: no-repeat;

	background-position: center 30%;
}

.social-icon .fa {
	padding-top: 11px !important;
}

.clients_cont2 {
	margin-top: 30px;
}

@media (max-width: 768px) {
	.service-offerings-heading {
		margin-top: 20px !important;
	}

	.navbar-brand {
		margin-left: 13px;
	}
}

@media (max-width: 420px) {
	.dropdown-for-service {
		min-width: 19rem !important;
	}

	.dropdown-for-about {
		min-width: 19rem !important;
	}

	.navbar-nav {
		margin-top: 46px !important;
	}

	.ban_right {
		display: none !important;
	}
}

@media (max-width: 420px) {
	.navbar-brand {
		margin-left: 13px;
	}

	.footer {
		width: fit-content !important;
	}

	.dropdown-for-service {
		min-width: 17rem !important;
	}

	.dropdown-for-about {
		min-width: 17rem !important;
	}
}

@media (max-width: 820px) and(min-width: 768px) {
	.navbar-brand {
		margin-left: 13px;
	}

	.bg-light {
		background-color: white !important;
	}

	.navbar-nav {
		margin-top: 10px !important;
	}

	.navbar-nav {
		border-radius: 10px 10px 10px 10px;
	}

	.dropdown-for-service {
		min-width: 42rem !important;
	}
}

@media (max-width: 912px) {
	.navbar-brand {
		margin-left: 13px;
	}

	.bg-light {
		background-color: white !important;
	}

	.navbar-nav {
		margin-top: 10px !important;
	}

	.navbar-nav {
		border-radius: 10px 10px 10px 10px;
	}
}

@media (max-width: 1024px) {
	.navbar-brand img {
		height: 40px !important;
	}

	.dropdown-column li a {
		font-size: x-small !important;
	}
}

@media (max-width: 1280px) {
	.dropdown-column li a {
		font-size: x-small !important;
	}
}

/* .products{

    display: none !important;

} */

h5.card-text.text-center {
	margin-top: 120px;
}

.col-lg-3.col-md-6.col-sm-12.mb-3.d-flex.align-items-stretch {
	width: 19%;
}
h5.card-text.text-center {
	font-size: 1rem;
}
