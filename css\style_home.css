@charset "utf-8";

html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
font,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
input[type='radio'],
input[type='checkbox'] {
	margin: 0;

	padding: 0;

	border: 0;

	outline: 0;

	font-size: 100%;

	background: transparent;

	resize: none;
}

body {
	line-height: 1;
}

html,
body {
	height: 100%;
}

a,
a:hover {
	text-decoration: none;

	outline: none;
}

a:active,
a:focus {
	outline: 0;
}

img {
	border: none;

	vertical-align: -2px;
}

textarea {
	resize: none;
}

a {
	color: #54acbf;
}

[hidden] {
	display: none;
}

::-webkit-scrollbar {
	width: 8px;
}

::-webkit-scrollbar-track {
	background-color: #eaeaea;

	border-left: 1px solid #ccc;
}

::-webkit-scrollbar-thumb {
	background-color: #218ecd;
}

::-webkit-scrollbar-thumb:hover {
	background-color: #0f6ba0;
}

html {
	-ms-text-size-adjust: 100%;

	-webkit-text-size-adjust: 100%;
}

.fl {
	float: left;
}

.fr {
	float: right;
}

.ovfl-hidden {
	overflow: hidden;
}

ul.reset {
	list-style: none;
}

.clr {
	clear: both;

	overflow: hidden;

	height: 1px;
}

html,
body {
	width: 100%;

	height: 100%;
}

body {
	font: normal 13px Arial, Helvetica, sans-serif;

	color: #676767;
}

#main_bg {
}

.slideshow {
	width: 100% !important;

	height: 100% !important;
}

#main_bg img {
	width: 100% !important;

	position: absolute !important;

	height: 100% !important;

	top: 0 !important;
}

#maininnout_bg #main_bg img {
	position: fixed !important;
}

#wrapper_home {
	width: 1120px;

	margin: 0 auto;

	position: relative;
}

#maincont {
	width: 100%;
}

#wrapper_home #header {
}

#wrapper_home #header .header_lft {
	float: left;

	width: 202px;
}

#wrapper_home #header .header_lft .header_lft_top {
	background: url(../images/lft_box_rpt_bg.png) repeat-y 0 0;

	overflow: hidden;

	padding: 0 8px 0 2px;
}

#wrapper_home #header .header_lft h1#logo {
	margin: 18px 0 35px 0;
}

#wrapper_home #header .header_lft h1#logo a {
	display: block;

	width: 160px;

	height: 72px;

	margin: 0 auto;
}

#wrapper_home #header .header_lft .header_lft_btm {
	background: url(../images/lft_box_btm_bg.png) no-repeat center 100%;

	height: 19px;
}

#wrapper_home #header .header_rght {
	display: flex;
}

#wrapper_home #header .header_rght nav ul {
	display: flex;
}

#wrapper_home #header .header_rght a {
	float: left;

	font-size: 12px;

	color: #fff;

	font-family: tahoma;

	padding: 0 10px 0 0;

	margin: 0 10px 0 0;

	border-right: 1px solid #e36b0b;
}

#wrapper_home #header .header_rght p {
	float: right;

	width: 144px;
}

#wrapper_home #header .header_rght p input.input {
	width: 100%;

	float: left;

	border: none;

	height: 17px;

	background-color: #dedede;
}

#wrapper_home #header .header_rght p input.sub_btn {
	background: url(../images/sub_btn_bg.gif) no-repeat 0 0;

	width: 18px;

	height: 19px;

	float: left;

	border: none;

	display: block;

	cursor: pointer;
}

.marR {
	margin-right: 0 !important;
}

.marB {
	margin-bottom: 0 !important;
}

.padR {
	padding-right: 0 !important;
}

.padT {
	padding-top: 0 !important;
}

.padB {
	padding-bottom: 0 !important;
}

.backGn {
	background: none !important;
}

.brdB {
	border-bottom: none !important;
}

.brdT {
	border-top: none !important;
}

.brdR {
	border-right: none !important;
}

#footer {
}

.headerimg {
	background-position: center center;

	background-repeat: no-repeat;

	width: 100%;

	height: 100%;

	position: absolute;

	background-size: cover;
}

.field_error {
	font: verdana, tahoma, arial;

	color: #ff0000;

	font-size: 14px;
}

.error_msg {
	color: #ff0000;
}

ul.addbulletstyle li {
	background: url('../images/listing_bg.gif') no-repeat scroll 0 5px transparent;

	padding: 0px;

	color: #f57b20;

	margin-top: 0px;

	padding-left: 15px;
}

ul.addbulletstyle li p span a:hover {
	text-decoration: underline !important;
}

#frmWhitePaper table tr td label {
	color: #696969;

	font-size: 14px;

	margin-bottom: 5px;
}

#frmWhitePaper .control-group {
	margin-top: 5px;
}

.form_btns_div_whitepaper {
	overflow: hidden;

	width: 130px;

	margin: 25px auto 0;

	padding-bottom: 10px;
}

.form_btns_div_whitepaper input.sub_btn {
	background: url(../blue_images/form_sub_btn_bg.png) no-repeat 0 0;

	width: 130px;

	height: 34px;

	border: none;

	display: block;

	float: left;

	cursor: pointer;

	margin-right: 8px;
}

.form_btns_div_whitepaper input.reset_btn {
	background: url(../blue_images/form_reset_btn_bg.png) no-repeat 0 0;

	width: 92px;

	height: 34px;

	border: none;

	display: block;

	float: left;

	cursor: pointer;
}

.form_btns_div_whitepaper input.sub_btn {
	background: url(../blue_images/download.png) no-repeat 0 0;

	width: 130px;

	height: 34px;

	border: none;

	display: block;

	float: left;

	cursor: pointer;

	margin-right: 8px;
}

.footer_first_video {
	color: #fff;

	font-family: LocalArialNarrow, arial narrow, sans-serif;

	font-size: 26px;

	font-stretch: condensed;

	font-weight: normal;

	line-height: 24px;

	margin-bottom: 14px;

	padding: 15px;

	text-align: center;
}

.experience {
	padding-top: 10px;
}

.banner_caption {
	margin-top: 370px;

	width: 650px;

	height: auto;

	float: left;
}

.banner_caption1 {
	margin-top: 370px;

	width: 650px;

	height: auto;

	float: left;
}

.wrapper-inner {
	width: 1120px;

	height: auto;

	margin: 0 auto;
}

.banner_cap_heading {
	font-size: 29px;

	text-shadow: 0 1px 1px #000;

	color: #fff;

	padding: 2px 10px 5px 0px;

	border-radius: 10px;

	-moz-border-radius: 2px;

	-webkit-border-radius: 2px;

	-ms-border-radius: 2px;

	line-height: 28px;

	font-weight: 700 !important;

	margin-bottom: 6px;
}

.banner_cap_heading span {
	font-size: 18px;

	color: #fff;

	display: block;
}

.banner_cap_cont {
	font-size: 16px;

	color: #fff;

	padding: 10px 25px 15px 10px;

	border-radius: 4px;

	-moz-border-radius: 4px;

	-webkit-border-radius: 4px;

	-ms-border-radius: 4px;

	line-height: 23px;

	background-color: rgba(0, 0, 0, 0.4);

	margin-bottom: 10px;

	letter-spacing: 0.8px;
}

.banner_cap_cont2 {
	font-size: 16px;

	color: #fff;

	padding: 5px 25px 10px 0px;

	border-radius: 4px;

	-moz-border-radius: 4px;

	-webkit-border-radius: 4px;

	-ms-border-radius: 4px;

	line-height: 23px;

	/* background-color: rgba(0,0,0, 0.4); */

	margin-bottom: 10px;

	letter-spacing: 0.8px;
}

.view_more {
	float: left;

	width: auto;

	height: auto;

	background: #1e93d7;

	border-radius: 6px;

	-moz-border-radius: 6px;

	-webkit-border-radius: 6px;

	-ms-border-radius: 6px;

	text-align: center;

	padding: 7px 15px;

	font-size: 14px;

	font-weight: 700;

	color: #fff;
}

.view_more:hover {
	background: #014574;

	color: #fff;

	cursor: pointer;
}

.view_more_icon {
	width: 6px;

	height: 9px;

	float: left;

	padding: 12px 5px 0 12px;
}

.view_more_cont {
	width: auto;

	height: auto;

	float: left;

	padding: 7px 10px;

	font-size: 14px;

	font-weight: 700;
}

.view_more_cont a {
	text-decoration: none;

	color: #fff;
}

.wrapper-inner2 {
	width: 100%;

	height: auto;

	background-color: #000;

	position: relative;

	background-position: bottom;
}

.strip-bg {
	width: 100%;

	height: auto;
}

.strip_section {
	float: left;

	width: 22%;

	border-right: 1px solid rgba(0, 0, 0, 0.25);

	border-left: 1px solid rgba(255, 255, 255, 0.1);

	min-height: 160px;

	height: auto !important;

	padding: 2em 1.2em;
}

.strip_section:last-child {
	border-left: 1px solid rgba(255, 255, 255, 0.1);

	border-right: 0px solid rgba(0, 0, 0, 0.25);
}

.strip_section:first-child {
	border-top: 0;

	border-left: 0px solid rgba(0, 0, 0, 0.25);
}

.strip_section h1 {
	color: #fff;

	font-size: 18px;

	font-weight: normal;
}

.strip_section p {
	color: #c9c9c9;

	font-size: 14px;

	line-height: 20px;

	margin-top: 12px;

	margin-bottom: 9px;
}

.strip_section span {
	color: #3c97e6;

	font-size: 18px;
}

.strip_section span a {
	color: #3c97e6 !important;

	font-size: 18px;

	text-decoration: none;
}

.strip_section span a:hover {
	color: #005aa9;

	text-decoration: none;
}

.clear {
	clear: both;
}

#main_btm .footer_btm_bg {
	background: #cc3333;

	width: 100%;

	padding: 4px 0;

	float: left;

	height: 22px;
}

.btm-latest-content:hover {
	background-color: #cc3333 !important;
}

.btm-latest-content {
	width: 215px;

	margin-right: 25px;

	right: 0px;

	position: absolute;

	padding: 0 5px;

	top: 90px;
}

.link_to_bottom {
	cursor: pointer;

	color: rgb(255, 255, 255);

	margin-right: 0px;

	width: 200px;

	float: right;
}

.link_to_bottom span.latest-content-text {
	float: left;

	font-size: 18px;

	margin: 5px 0px 0px 3px;

	vertical-align: middle;

	width: 166px;
}

.link_to_bottom span.latest-content-img {
	float: right;

	padding: 0px;

	background: url('../images/scroll-bottom-red.png') 0 0 no-repeat !important;

	height: 30px;

	width: 30px;
}

.link_to_bottom span.latest-content-text-hover {
	color: #ffffff;
}

.link_to_bottom span.latest-content-img-hover {
	background: url('../images/scroll-bottom-hover.png') 0 0 no-repeat !important;
}

.footer.container-tight {
	padding: 30px 0;

	border-top: 1px solid #333;
}

.container-tight2 {
	padding: 30px 0;

	border-top: 1px solid #333;
}

.container-inner {
	clear: both;

	margin: 0 auto;

	max-width: 1120px;

	padding: 0 20px;

	position: relative;
}

.social-list {
	visibility: hidden;

	height: 44px;
}

ul,
li {
	list-style-type: none;

	padding: 0;

	margin: 0;
}

.container-black {
	color: #7e7e7e;

	background: #000;
}

.container-black a {
	color: #fff;

	/*font-weight: 700;*/
}

.social-icon-twitter {
	background-color: #16b3f4;
}

.social-icon-youtube {
	background-color: #ed1c24;
}

.fa {
	padding-top: 11px;
}

.social-bubble {
	background-color: #fff;

	border-radius: 3px;

	color: #000;

	float: left;

	font-size: 12px;

	font-weight: 700;

	margin: 5px 24px 0 11px;

	padding: 6px 12px;

	position: relative;
}

.social-bubble:before {
	left: -10px;

	border: solid transparent;

	content: '';

	height: 0;

	width: 0;

	position: absolute;

	border-right-color: #fff;

	border-width: 6px;

	top: 50%;

	margin-top: -5px;
}

.social-icon-facebook {
	background-color: #4f72ce;
}

.social-icon {
	background-color: gray;

	border-radius: 24px;

	color: #fff;

	float: left;

	font-family: 'LigatureSymbols';

	font-size: 1.45em;

	height: 48px;

	letter-spacing: -0.1em;

	line-height: 2.15em;

	text-align: center;

	width: 48px;

	-webkit-font-smoothing: antialiased;

	-moz-osx-font-smoothing: grayscale;
}

.social-icon-gplus {
	background-color: #ec332d;
}

.social-icon-gplus {
	background-color: #ec332d;
}

.contact {
	display: inline-block;

	margin-left: 50px;

	position: relative;

	text-align: left;

	line-height: 21px;
}

.contact-phone {
	color: #fff;

	font-size: 1.2em;

	margin-bottom: 0;
}

p {
	margin: 0px;
}

p {
	padding-bottom: 10px;

	text-align: justify;
}

a.contact-phone-icon {
	-webkit-border-radius: 24px;

	-moz-border-radius: 24px;

	border-radius: 24px;

	border: 2px solid #fff;

	color: #fff;

	display: block;

	font-family: pingdom-iconsregular;

	font-size: 1.3em;

	height: 44px;

	line-height: 2.7em;

	margin-top: -2em;

	position: absolute;

	right: 106%;

	text-align: center;

	top: 60%;

	width: 44px;
}

.contact a {
	color: inherit;
}

.contact-email {
	margin-bottom: 0;
}

.container-inner {
	clear: both;

	margin: 0 auto;

	max-width: 1100px !important;

	padding: 0 20px;

	position: relative;
}

.strip_section1 {
	float: left;

	width: 100%;

	min-height: 144px;

	height: auto !important;
}

.strip_section1 h1 {
	color: #151515;

	font-size: 22px;

	font-weight: normal;
}

.strip_section1 p {
	color: #606060;

	font-size: 14px;

	line-height: 20px;

	margin-top: 12px;

	margin-bottom: 9px;
}

.strip_section1 span {
	color: #606060;

	font-size: 18px;
}

.strip_section1 span a {
	color: #606060;

	font-size: 18px;

	text-decoration: none;
}

.strip_section span1 a:hover {
	color: #005aa9;

	text-decoration: none;
}

#carousel_inner1 {
	float: left; /* important for inline positioning */

	width: 100%; /* important (this width = width of list item(including margin) * items shown */

	overflow: hidden; /* important (hide the items outside the div) */ /* non-important styling bellow */

	margin-bottom: 7px;

	word-wrap: break-word;

	color: #c9c9c9;

	margin-top: 5px;
}

#carousel_inner1 ul {
	position: relative;

	left: 0px; /* important (this should be negative number of list items width(including margin) */

	list-style-type: none; /* removing the default styling for unordered list items */

	margin: 0px;

	padding: 0px;

	width: 9999px; /* important */ /* non-important styling bellow */
}

#carousel_inner1 ul li {
	width: 282px !important;

	height: auto;

	float: left;

	word-wrap: break-word;

	color: #c9c9c9;

	font-size: 14px;

	line-height: 20px;

	margin-top: 12px;

	margin-bottom: 9px;
}

#left_scroll1,
#right_scroll1 {
	float: left;

	height: 24px;

	width: 24px;

	margin-left: 0px;

	margin-right: 0px;
}

#left_scroll1 img,
#right_scroll1 img {
	/*styling*/

	cursor: pointer;

	cursor: hand;
}

#left_scroll1 img:hover,
#right_scroll1 img:hover {
	/*styling*/

	cursor: pointer;

	cursor: hand;

	opacity: 0.6;
}

/* ========================================= */

.wrapper-home1 {
	display: grid;

	grid-template-columns: repeat(5, 1fr);

	column-gap: 20px;

	row-gap: 1em;

	text-align: center;

	width: 90%;

	margin-left: auto;

	margin-right: auto;
}

.heading {
	font-size: 17px;

	color: #494949;

	font-weight: 700;

	text-align: center;

	margin-bottom: 10px;

	padding: 30px 10px;

	border: 1px solid #bbbdc0;

	background-color: white;

	border-radius: 15px;
}

.heading:hover {
	color: white;

	border: 1px solid #bbbdc0;

	background-color: #006c9a;

	border-radius: 15px;

	box-shadow: 0 0 11px rgba(33, 33, 33, 0.5);
}

.heading:hover a {
	color: white;
}

.service-offering {
	color: #676767;

	padding-top: 10px;
}

.service-offering:hover {
	color: white;
}

.heading a {
	color: #676767;
}

.heading a:hover {
	color: #ffffff;
}

/* .heading img {

	background-image: url(images/business-process-management.png);

} */

.headingimage1 {
	background-image: url('../images/business-process-management3.svg');

	height: 100px;

	background-repeat: no-repeat;

	background-position: center;

	text-align: center;
}

.headingimage1:hover {
	background-image: url('../images/business-process-management4.svg');
}

.headingimage2 {
	background-image: url('../images/it-services2.svg');

	height: 100px;

	background-repeat: no-repeat;

	background-position: center;

	text-align: center;
}

.headingimage2:hover {
	background-image: url('../images/it-services3.svg');
}

.headingimage3 {
	background-image: url('../images/system-consulting-Integration1.svg');

	height: 100px;

	background-repeat: no-repeat;

	background-position: center;

	text-align: center;
}

.headingimage3:hover {
	background-image: url('../images/system-consulting-Integration2.svg');
}

.headingimage4 {
	background-image: url('../images/blockchain-icon1.svg');

	height: 100px;

	background-repeat: no-repeat;

	background-position: center;

	text-align: center;
}

.headingimage4:hover {
	background-image: url('../images/blockchain-icon2.svg');
}

.headingimage5 {
	background-image: url('../images/cyber-security-icon1.svg');

	height: 100px;

	background-repeat: no-repeat;

	background-position: center;

	text-align: center;
}

.headingimage5:hover {
	background-image: url('../images/cyber-security-icon2.svg');
}

/* .heading img:hover {

   	background-image: url("../images/business-process-management2.png") 0 0 no-repeat!important;	

} */

/* .heading img:hover {

   	display: none;

} */

.inner_body_left {
	width: 727px;

	height: auto;

	float: left;

	margin-bottom: 15px;

	width: 60%;
}

.home_inner_body_left {
	height: auto;

	float: left;

	margin-bottom: 15px;

	width: 60%;
}

.inner_body_right {
	height: auto;

	float: right;

	margin-bottom: 15px;

	width: 30%;

	text-align: right;

	margin-right: 10%;

	padding-top: 5%;
}

.home_inner_body_right {
	height: auto;

	float: right;

	margin-bottom: 15px;

	width: 30%;

	text-align: right;

	padding-top: 5%;
}

.inner_body_left1 {
	width: 727px;

	height: auto;

	float: left;

	margin-bottom: 15px;

	width: 45%;
}

.home_inner_body_left1 {
	height: auto;

	float: left;

	margin-bottom: 15px;

	width: 50%;
}

.inner_body_right1 {
	width: 727px;

	height: auto;

	float: left;

	margin-bottom: 15px;

	width: 45%;

	margin-right: 10%;

	padding-top: 2%;
}

.home_inner_body_right1 {
	width: 727px;

	height: auto;

	float: left;

	margin-bottom: 15px;

	width: 50%;

	padding-top: 2%;
}

.inner_body_right2 {
	width: 727px;

	height: auto;

	float: left;

	margin-bottom: 15px;

	width: 45%;

	margin-right: 10%;

	padding-top: 0px;
}

.home_inner_body_right2 {
	height: auto;

	float: left;

	margin-bottom: 15px;

	width: 50%;

	padding-top: 0px;
}

.our-partners-text {
	font-size: 17px;

	line-height: 25px;

	padding: 0 5% 2% 0;
}

.our-client-text {
	font-size: 17px;

	line-height: 25px;
}

.connected-technology {
	font-size: 32px;

	font-weight: bold;

	padding: 5px 0;

	color: #218ecd;

	margin-left: 200px;
}

.connected-business {
	text-align: right;

	font-size: 17px;
}

.hareline1 {
	width: 50%;

	float: right;
}

.our-client-img {
	width: 90%;
}

.connected-technology-text {
	font-size: 17px;

	line-height: 25px;

	padding-top: 10px;
}

.connected-technology-heading {
	font-size: 32px;

	font-weight: bold;

	padding-bottom: 10px;
}

.connected-technology-img {
	width: 80%;
}

.our-client-heading {
	font-size: 32px;

	font-weight: bold;

	color: #676767;

	padding-bottom: 2%;

	letter-spacing: 3px;
}

.our-partners-heading {
	font-size: 32px;

	font-weight: bold;

	color: #676767;

	padding: 0 2% 2% 0;

	letter-spacing: 3px;
}

.service-offerings-heading {
	text-align: center;

	font-size: 32px;

	font-weight: bold;

	padding: 30px 0px 30px 0px;

	margin-left: auto;

	margin-right: auto;
}

.footer-wrapper-inner {
	padding: 4%;
}

@media (max-width: 375px) and(min-width: 412px) {
	a.contact-phone-icon {
		top: 33% !important;
	}
}

@media screen and (min-width: 1034px) and (max-width: 1370px) {
	.banner_cap_cont2 {
		margin-top: 40px;
	}

	.banner_caption {
		margin-top: 196px;

		margin-left: 20px;
	}

	.dropdown-column li a {
		font-size: medium !important;
	}
}

.bg-img {
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	height: 100vh;
	min-height: 600px;
	position: relative;
}

.carousel-caption {
	padding: 2rem;
	max-width: 600px;
	left: 10% !important;
	bottom: 140px !important;
	position: absolute;
	right: auto !important;
	z-index: 2;
}

.carousel-heading {
	font-size: 29px;
	font-weight: bold;
}

.carousel-text {
	font-size: 16px;
	color: #fff;
	padding: 5px 25px 10px 0px;
	border-radius: 4px;
	-moz-border-radius: 4px;
	-webkit-border-radius: 4px;
	-ms-border-radius: 4px;
	line-height: 23px;
	margin-bottom: 10px;
	letter-spacing: 0.8px;
}

.carousel-control-prev,
.carousel-control-next {
	display: none !important;
}

@media (max-width: 768px) {
	.bg-img {
		height: 500px;
		min-height: auto;
	}

	.carousel-caption {
		padding: 1rem;
		max-width: 90%;
		left: 5% !important;
		bottom: 30% !important;
		transform: translateY(50%);
		right: 5% !important;
		background-color: rgba(0, 0, 0, 0.6);
		border-radius: 8px;
	}

	.carousel-heading {
		font-size: 20px;
		margin-bottom: 10px;
	}

	.carousel-text {
		font-size: 14px;
		line-height: 20px;
		padding: 5px 15px 8px 0px;
		margin-bottom: 8px;
	}

	.carousel-caption .btn {
		padding: 0.4rem 0.8rem;
		font-size: 0.8rem;
	}
}

@media (max-width: 576px) {
	.bg-img {
		height: 400px;
		min-height: auto;
	}

	.carousel-caption {
		bottom: 30% !important;
		transform: translateY(50%);
		padding: 0.8rem;
		background-color: rgba(0, 0, 0, 0.6);
		border-radius: 8px;
	}

	.carousel-heading {
		font-size: 16px;
		margin-bottom: 8px;
	}

	.carousel-text {
		font-size: 12px;
		line-height: 18px;
		padding: 3px 10px 6px 0px;
		margin-bottom: 6px;
	}

	.carousel-caption .btn {
		padding: 0.4rem 0.8rem;
		font-size: 0.8rem;
	}
}
/* Large Screens */
.container2 {
	width: 100%;
	margin: 0 auto;
}

.index-con .row {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-evenly;
	gap: 10px;
}

.card {
	width: 18rem;
	padding: 20px;
	text-align: center;
	border-radius: 10px;
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Tablets and  Desktops (768px to 1024px) */
@media (max-width: 1024px) {
	.col-lg-3 {
		flex: 0 0 45%;
		max-width: 45%;
		margin-bottom: 20px;
	}

	.home_inner_body_left,
	.home_inner_body_right {
		width: 100%;
		margin-bottom: 20px;
	}

	.home_inner_body_right1,
	.home_inner_body_left1 {
		width: 100%;
		margin-bottom: 20px;
	}

	.connected-technology-img,
	.our-client-img {
		width: 100%;
		margin: 0 auto;
	}
}

/* Mobile (326px to 768px) */
@media (max-width: 768px) and (min-width: 326px) {
	.banner_cap_cont2 {
		text-align: center;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		width: 100%;
		padding: 10px;
	}

	.container2 {
		padding: 0 10px;
		text-align: center;
	}

	.service-offerings-heading,
	.connected-technology-heading {
		font-size: 1.2rem;
		text-align: center;
	}

	.card {
		width: 100%;
		padding: 15px;
		margin: 0 auto;
	}

	.connected-technology,
	.connected-business {
		font-size: 1.2rem;
		text-align: center;
	}

	.home_inner_body_left,
	.home_inner_body_right,
	.home_inner_body_left1,
	.home_inner_body_right1 {
		width: 100%;
		text-align: center;
	}

	.connected-technology-img,
	.our-client-img {
		width: 100%;
		margin: 0 auto;
	}

	.col-lg-3,
	.col-md-6,
	.col-sm-12 {
		flex: 0 0 100%;
		max-width: 100%;
		margin-bottom: 15px;
		display: flex;
		justify-content: center;
	}

	.card h5 {
		font-size: 1rem;
	}
}

/* Small Mobile (less than 480px) */
@media (max-width: 480px) {
	.container2 {
		padding: 0 10px;
	}

	.service-offerings-heading {
		font-size: 1.2rem;
		text-align: center;
	}

	.connected-technology-heading {
		font-size: 1.2rem;
		text-align: center;
	}

	.card h5 {
		font-size: 1rem;
	}

	.connected-technology {
		font-size: 1.5rem;
	}

	.connected-business {
		font-size: 1rem;
	}

	.our-client-img,
	.connected-technology-img {
		width: 100%;
		height: auto;
	}
}

/* Chatbot */
.chat-container {
	position: fixed;
	bottom: 20px;
	right: 20px;
	width: 350px;
	height: 500px;
	background: white;
	border-radius: 10px;
	box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
	display: none;
	flex-direction: column;
	z-index: 1000;
}

.chat-header {
	background: #218ecd;
	color: white;
	padding: 15px;
	border-radius: 10px 10px 0 0;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.chat-header h3 {
	margin: 0;
	font-size: 16px;
}

.close-chat {
	cursor: pointer;
	font-size: 20px;
}

.chat-messages {
	flex-grow: 1;
	padding: 15px;
	overflow-y: auto;
}

.message {
	margin-bottom: 10px;
	max-width: 80%;
	padding: 10px;
	border-radius: 10px;
}

.bot-message {
	background: #f0f0f0;
	margin-right: auto;
}

.user-message {
	background: #218ecd;
	color: white;
	margin-left: auto;
}

.chat-input {
	padding: 15px;
	border-top: 1px solid #eee;
	display: flex;
}

.chat-input input {
	flex-grow: 1;
	padding: 10px;
	border: 1px solid #ddd;
	border-radius: 5px;
	margin-right: 10px;
}

.chat-input button {
	padding: 10px 20px;
	background: #218ecd;
	color: white;
	border: none;
	border-radius: 5px;
	cursor: pointer;
}

.chat-button {
	position: fixed;
	bottom: 20px;
	right: 20px;
	background: #218ecd;
	color: white;
	padding: 15px;
	border-radius: 50%;
	cursor: pointer;
	box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
	z-index: 1000;
}

.chat-button i {
	font-size: 24px;
}

.options-container {
	display: flex;
	flex-wrap: wrap;
	gap: 5px;
	margin-top: 10px;
}

.option-button {
	background: #218ecd;
	color: white;
	border: none;
	padding: 5px 10px;
	border-radius: 15px;
	cursor: pointer;
	font-size: 14px;
}
