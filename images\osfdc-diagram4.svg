<?xml version="1.0" encoding="utf-8"?>

<!-- Generator: Adobe Illustrator 16.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">

<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"

	 width="1130.17px" height="396.844px" viewBox="0 0 1130.17 396.844" enable-background="new 0 0 1130.17 396.844"

	 xml:space="preserve">

<rect x="3.342" y="1.191" fill="none" stroke="#D1D3D4" stroke-width="0.5" stroke-miterlimit="10" width="1123.486" height="394.461"/>

<g>

	

		<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="322.115" y1="851.0486" x2="515.8435" y2="851.0486" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)">

		<stop  offset="0" style="stop-color:#EF829A"/>

		<stop  offset="1" style="stop-color:#EE3363"/>

	</linearGradient>

	<path fill="url(#SVGID_1_)" d="M186.151,185.31l76.945,76.946c7.755,7.753,20.449,7.753,28.206,0l76.943-76.946

		c7.757-7.755,7.757-20.447,0-28.204l-48.738-48.74c-23.268-23.268-61.347-23.268-84.615,0l-48.741,48.74

		C178.396,164.865,178.396,177.555,186.151,185.31L186.151,185.31z"/>

	

		<linearGradient id="SVGID_2_" gradientUnits="userSpaceOnUse" x1="229.115" y1="944.9646" x2="406.2742" y2="944.9646" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)">

		<stop  offset="0" style="stop-color:#FBB052"/>

		<stop  offset="1" style="stop-color:#F37221"/>

	</linearGradient>

	<path fill="url(#SVGID_2_)" d="M104.786,239.385l48.741-48.74c7.753-7.754,20.449-7.754,28.205,0l76.943,76.946

		c7.758,7.755,7.758,20.449,0,28.203l-48.736,48.741c-23.271,23.27-61.348,23.27-84.618,0l-20.535-20.533

		C81.517,300.731,81.517,262.656,104.786,239.385L104.786,239.385z"/>

	

		<linearGradient id="SVGID_3_" gradientUnits="userSpaceOnUse" x1="541.5017" y1="851.0486" x2="735.2292" y2="851.0486" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)">

		<stop  offset="0" style="stop-color:#777BB8"/>

		<stop  offset="1" style="stop-color:#52449B"/>

	</linearGradient>

	<path fill="url(#SVGID_3_)" d="M405.541,185.31l76.943,76.946c7.757,7.753,20.45,7.753,28.205,0l76.944-76.946

		c7.756-7.755,7.756-20.447,0-28.204l-48.738-48.74c-23.271-23.268-61.348-23.268-84.615,0l-48.738,48.74

		C397.781,164.865,397.781,177.555,405.541,185.31L405.541,185.31z"/>

	

		<linearGradient id="SVGID_4_" gradientUnits="userSpaceOnUse" x1="431.9314" y1="944.9646" x2="625.6589" y2="944.9646" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)">

		<stop  offset="0" style="stop-color:#D469A8"/>

		<stop  offset="1" style="stop-color:#D03593"/>

	</linearGradient>

	<path fill="url(#SVGID_4_)" d="M295.97,267.592l76.941-76.946c7.759-7.754,20.45-7.754,28.205,0l76.946,76.946

		c7.755,7.755,7.755,20.449,0,28.203l-48.737,48.741c-23.271,23.27-61.349,23.27-84.616,0l-48.738-48.741

		C288.211,288.041,288.211,275.345,295.97,267.592L295.97,267.592z"/>

	

		<linearGradient id="SVGID_5_" gradientUnits="userSpaceOnUse" x1="760.8914" y1="851.0486" x2="954.6189" y2="851.0486" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)">

		<stop  offset="0" style="stop-color:#85CBC3"/>

		<stop  offset="1" style="stop-color:#28B4C9"/>

	</linearGradient>

	<path fill="url(#SVGID_5_)" d="M624.93,185.31l76.943,76.946c7.756,7.753,20.449,7.753,28.203,0l76.946-76.946

		c7.754-7.755,7.754-20.447,0-28.204l-48.739-48.74c-23.27-23.268-61.347-23.268-84.616,0l-48.736,48.74

		C617.172,164.865,617.172,177.555,624.93,185.31L624.93,185.31z"/>

	

		<linearGradient id="SVGID_6_" gradientUnits="userSpaceOnUse" x1="651.3191" y1="944.9646" x2="845.0476" y2="944.9646" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)">

		<stop  offset="0" style="stop-color:#4D9ECD"/>

		<stop  offset="1" style="stop-color:#127BC0"/>

	</linearGradient>

	<path fill="url(#SVGID_6_)" d="M515.355,267.592l76.946-76.946c7.757-7.754,20.451-7.754,28.205,0l76.943,76.946

		c7.757,7.755,7.757,20.449,0,28.203l-48.736,48.741c-23.271,23.27-61.348,23.27-84.618,0l-48.739-48.741

		C507.599,288.041,507.599,275.345,515.355,267.592L515.355,267.592z"/>

	

		<linearGradient id="SVGID_7_" gradientUnits="userSpaceOnUse" x1="871.4392" y1="944.9646" x2="1065.1697" y2="944.9646" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)">

		<stop  offset="0" style="stop-color:#86BD66"/>

		<stop  offset="1" style="stop-color:#53B548"/>

	</linearGradient>

	<path fill="url(#SVGID_7_)" d="M735.476,267.592l76.946-76.946c7.757-7.754,20.446-7.754,28.204,0l76.948,76.946

		c7.753,7.755,7.753,20.449,0,28.203l-48.741,48.741c-23.27,23.27-61.348,23.27-84.616,0l-48.739-48.741

		C727.719,288.041,727.719,275.345,735.476,267.592L735.476,267.592z"/>

	

		<linearGradient id="SVGID_8_" gradientUnits="userSpaceOnUse" x1="986.8787" y1="851.0505" x2="1164.0398" y2="851.0505" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)">

		<stop  offset="0" style="stop-color:#E3E670"/>

		<stop  offset="0.6191" style="stop-color:#C5DA4F"/>

		<stop  offset="1" style="stop-color:#B6D441"/>

	</linearGradient>

	<path fill="url(#SVGID_8_)" d="M850.916,185.31l76.945,76.946c7.758,7.753,20.448,7.753,28.204,0l48.743-48.738

		c23.268-23.271,23.268-61.348,0-84.617l-20.534-20.532c-23.272-23.268-61.346-23.268-84.618,0l-48.739,48.741

		C843.159,164.865,843.159,177.555,850.916,185.31L850.916,185.31z"/>

	<path fill="#CACACA" d="M1075.982,42.272l-12.348,47.848l-15.054-15.051l2.629-10.12c-15.144,15.996-16.866,18.856-42.9,44.89

		l-12.753-12.754c-7.247-7.246-15.598-12.708-24.503-16.384c-9.265-3.825-19.169-5.74-29.088-5.74

		c-9.918,0-19.822,1.915-29.089,5.74c-8.902,3.675-17.25,9.139-24.5,16.384c-79.067,79.07-39.741,79.068-118.812,0

		c-7.245-7.246-15.596-12.708-24.499-16.384c-9.265-3.825-19.171-5.74-29.087-5.74c-9.919,0-19.826,1.915-29.089,5.74

		c-8.902,3.675-17.251,9.139-24.5,16.384c-71.666,71.667-40.545,71.667-112.207,0c-7.25-7.246-15.601-12.708-24.502-16.384

		c-9.263-3.825-19.169-5.74-29.087-5.74c-9.919,0-19.821,1.915-29.088,5.74c-8.902,3.675-17.252,9.139-24.502,16.384

		c-71.661,71.666-40.545,71.666-112.206,0c-7.249-7.246-15.601-12.708-24.5-16.384c-9.266-3.825-19.17-5.74-29.089-5.74

		c-9.918,0-19.819,1.915-29.087,5.74C220.716,92.019,83.334,242.488,59.538,266.284l-6.261-5.032

		c25.915-25.917,161.696-175.47,191.81-187.904c10.269-4.24,21.205-6.363,32.109-6.363c10.911,0,21.842,2.123,32.111,6.363

		c9.895,4.082,19.14,10.12,27.121,18.101c64.82,64.821,36.105,64.821,100.927,0c7.982-7.981,17.228-14.019,27.119-18.101

		c10.272-4.24,21.205-6.363,32.111-6.363c10.909,0,21.841,2.123,32.11,6.363c9.892,4.082,19.139,10.12,27.12,18.101

		c64.822,64.821,36.108,64.821,100.927,0c7.981-7.981,17.226-14.019,27.118-18.101c10.271-4.24,21.204-6.363,32.113-6.363

		c10.905,0,21.837,2.123,32.105,6.363c9.896,4.082,19.142,10.12,27.123,18.101c70.127,70.125,37.404,70.125,107.528,0

		c7.983-7.981,17.226-14.019,27.118-18.101c10.271-4.24,21.207-6.363,32.113-6.363s21.842,2.123,32.112,6.363

		c9.894,4.082,19.134,10.12,27.117,18.101l7.054,7.054c14.853-15.425,13.311-14.939,35.908-38.203l-8.496,1.854l-14.343-14.341

		l48.421-11.785L1075.982,42.272L1075.982,42.272z"/>

	<g enable-background="new    ">

		<path fill="#FFFFFF" d="M150.872,290.007v-12.94h8.729v1.526h-7.018v4.008h6.073v1.526h-6.073v5.879L150.872,290.007

			L150.872,290.007z"/>

		<path fill="#FFFFFF" d="M167.766,290.007v-1.377c-0.73,1.059-1.72,1.589-2.974,1.589c-0.553,0-1.07-0.105-1.549-0.317

			c-0.48-0.212-0.835-0.479-1.068-0.8c-0.233-0.32-0.396-0.712-0.49-1.177c-0.064-0.313-0.096-0.807-0.096-1.483v-5.809h1.588v5.199

			c0,0.83,0.033,1.391,0.098,1.678c0.099,0.418,0.312,0.745,0.635,0.984c0.324,0.238,0.724,0.357,1.2,0.357

			c0.478,0,0.924-0.123,1.342-0.366c0.418-0.245,0.714-0.577,0.887-0.998c0.174-0.42,0.261-1.03,0.261-1.83v-5.024h1.588v9.376

			h-1.421V290.007z"/>

		<path fill="#FFFFFF" d="M171.677,290.007v-9.376h1.429v1.333c0.688-1.029,1.683-1.544,2.984-1.544

			c0.564,0,1.084,0.101,1.558,0.304s0.829,0.47,1.064,0.798c0.236,0.331,0.4,0.722,0.494,1.176c0.059,0.294,0.088,0.809,0.088,1.545

			v5.764h-1.588v-5.703c0-0.646-0.062-1.131-0.186-1.451c-0.125-0.321-0.343-0.577-0.657-0.769c-0.316-0.19-0.684-0.287-1.108-0.287

			c-0.677,0-1.262,0.215-1.753,0.645c-0.491,0.43-0.737,1.245-0.737,2.445v5.12L171.677,290.007L171.677,290.007z"/>

		<path fill="#FFFFFF" d="M187.812,290.007v-1.184c-0.594,0.931-1.468,1.396-2.622,1.396c-0.748,0-1.434-0.207-2.061-0.618

			c-0.626-0.411-1.113-0.986-1.456-1.726c-0.344-0.738-0.517-1.588-0.517-2.547c0-0.936,0.156-1.784,0.467-2.547

			c0.312-0.762,0.781-1.346,1.404-1.751c0.624-0.406,1.32-0.609,2.092-0.609c0.565,0,1.069,0.119,1.509,0.357

			c0.441,0.238,0.8,0.549,1.077,0.932v-4.643h1.58v12.94L187.812,290.007L187.812,290.007z M182.79,285.327

			c0,1.2,0.253,2.1,0.759,2.692c0.505,0.596,1.104,0.892,1.792,0.892c0.694,0,1.284-0.283,1.77-0.852

			c0.485-0.568,0.729-1.435,0.729-2.6c0-1.284-0.248-2.225-0.742-2.825c-0.494-0.6-1.103-0.901-1.828-0.901

			c-0.706,0-1.296,0.289-1.77,0.866C183.027,283.176,182.79,284.086,182.79,285.327z"/>

	</g>

	<g enable-background="new    ">

		<path fill="#FFFFFF" d="M138.757,311.697v-12.94h5.737c1.153,0,2.03,0.116,2.63,0.349c0.601,0.232,1.08,0.643,1.439,1.231

			c0.359,0.588,0.539,1.238,0.539,1.95c0,0.918-0.298,1.692-0.893,2.322s-1.513,1.029-2.753,1.2

			c0.453,0.217,0.798,0.432,1.033,0.644c0.501,0.46,0.974,1.033,1.421,1.722l2.251,3.522h-2.154l-1.712-2.692

			c-0.501-0.777-0.913-1.371-1.235-1.783c-0.324-0.412-0.614-0.7-0.87-0.866c-0.256-0.164-0.516-0.279-0.781-0.344

			c-0.194-0.042-0.512-0.062-0.954-0.062h-1.986v5.747H138.757L138.757,311.697z M140.469,304.468h3.681

			c0.782,0,1.394-0.081,1.836-0.243c0.441-0.161,0.777-0.421,1.006-0.777c0.23-0.355,0.345-0.742,0.345-1.16

			c0-0.612-0.222-1.114-0.667-1.509c-0.444-0.396-1.146-0.593-2.104-0.593h-4.096L140.469,304.468L140.469,304.468z"/>

		<path fill="#FFFFFF" d="M157.999,308.678l1.641,0.203c-0.258,0.96-0.738,1.704-1.438,2.232c-0.7,0.53-1.595,0.796-2.684,0.796

			c-1.371,0-2.458-0.423-3.262-1.268c-0.803-0.844-1.206-2.028-1.206-3.554c0-1.576,0.406-2.8,1.218-3.672s1.865-1.306,3.16-1.306

			c1.252,0,2.277,0.426,3.072,1.279c0.793,0.854,1.192,2.055,1.192,3.602c0,0.094-0.002,0.235-0.01,0.424h-6.99

			c0.058,1.029,0.35,1.818,0.874,2.365c0.523,0.549,1.177,0.822,1.959,0.822c0.583,0,1.08-0.153,1.492-0.459

			C157.429,309.839,157.758,309.348,157.999,308.678z M152.781,306.109h5.235c-0.071-0.788-0.271-1.38-0.599-1.774

			c-0.507-0.612-1.163-0.918-1.969-0.918c-0.729,0-1.343,0.244-1.84,0.732C153.111,304.637,152.836,305.292,152.781,306.109z"/>

		<path fill="#FFFFFF" d="M167.752,308.263l1.562,0.203c-0.171,1.077-0.608,1.92-1.311,2.529c-0.703,0.608-1.566,0.914-2.591,0.914

			c-1.283,0-2.314-0.42-3.093-1.259c-0.78-0.838-1.17-2.04-1.17-3.605c0-1.012,0.168-1.898,0.503-2.657

			c0.334-0.758,0.846-1.328,1.53-1.707c0.686-0.38,1.432-0.569,2.237-0.569c1.019,0,1.852,0.257,2.498,0.771

			c0.648,0.516,1.062,1.247,1.245,2.193l-1.545,0.238c-0.147-0.629-0.408-1.102-0.781-1.421c-0.373-0.318-0.825-0.477-1.355-0.477

			c-0.8,0-1.451,0.288-1.951,0.86c-0.501,0.574-0.75,1.483-0.75,2.723c0,1.26,0.241,2.176,0.724,2.746

			c0.482,0.571,1.113,0.856,1.889,0.856c0.624,0,1.146-0.19,1.563-0.573C167.376,309.647,167.64,309.057,167.752,308.263z"/>

		<path fill="#FFFFFF" d="M177.092,308.678l1.641,0.203c-0.259,0.96-0.738,1.704-1.438,2.232c-0.7,0.53-1.596,0.796-2.684,0.796

			c-1.372,0-2.458-0.423-3.262-1.268c-0.802-0.844-1.205-2.028-1.205-3.554c0-1.576,0.406-2.8,1.218-3.672

			c0.812-0.872,1.865-1.306,3.159-1.306c1.253,0,2.277,0.426,3.072,1.279c0.793,0.854,1.191,2.055,1.191,3.602

			c0,0.094-0.002,0.235-0.009,0.424h-6.99c0.058,1.029,0.35,1.818,0.873,2.365c0.524,0.549,1.178,0.822,1.96,0.822

			c0.582,0,1.079-0.153,1.492-0.459S176.849,309.348,177.092,308.678z M171.874,306.109h5.235c-0.071-0.788-0.271-1.38-0.6-1.774

			c-0.506-0.612-1.163-0.918-1.968-0.918c-0.73,0-1.344,0.244-1.84,0.732C172.204,304.637,171.927,305.292,171.874,306.109z"/>

		<path fill="#FFFFFF" d="M180.737,300.583v-1.826h1.59v1.826H180.737z M180.737,311.697v-9.375h1.59v9.375H180.737z"/>

		<path fill="#FFFFFF" d="M187.348,311.697l-3.566-9.375h1.677l2.013,5.613c0.217,0.606,0.418,1.237,0.6,1.89

			c0.141-0.494,0.337-1.089,0.591-1.783l2.083-5.72h1.633l-3.549,9.375H187.348L187.348,311.697z"/>

		<path fill="#FFFFFF" d="M200.2,308.678l1.641,0.203c-0.258,0.96-0.738,1.704-1.438,2.232c-0.7,0.53-1.595,0.796-2.684,0.796

			c-1.371,0-2.458-0.423-3.261-1.268c-0.803-0.844-1.206-2.028-1.206-3.554c0-1.576,0.406-2.8,1.218-3.672

			c0.812-0.872,1.865-1.306,3.159-1.306c1.253,0,2.277,0.426,3.072,1.279c0.793,0.854,1.192,2.055,1.192,3.602

			c0,0.094-0.003,0.235-0.01,0.424h-6.99c0.058,1.029,0.35,1.818,0.874,2.365c0.523,0.549,1.177,0.822,1.959,0.822

			c0.583,0,1.079-0.153,1.492-0.459S199.958,309.348,200.2,308.678z M194.983,306.109h5.235c-0.071-0.788-0.271-1.38-0.599-1.774

			c-0.507-0.612-1.164-0.918-1.969-0.918c-0.729,0-1.343,0.244-1.84,0.732C195.313,304.637,195.036,305.292,194.983,306.109z"/>

	</g>

	<g enable-background="new    ">

		<path fill="#FFFFFF" d="M228.321,188.049v-12.939h4.458c1.006,0,1.774,0.061,2.304,0.185c0.741,0.17,1.374,0.48,1.898,0.927

			c0.682,0.577,1.193,1.314,1.532,2.211c0.337,0.898,0.507,1.923,0.507,3.076c0,0.982-0.114,1.854-0.344,2.613

			c-0.23,0.76-0.524,1.388-0.883,1.885c-0.358,0.498-0.751,0.888-1.179,1.173c-0.427,0.287-0.941,0.502-1.545,0.648

			c-0.603,0.148-1.295,0.221-2.079,0.221H228.321z M230.033,186.521h2.762c0.853,0,1.522-0.079,2.008-0.238

			c0.487-0.159,0.873-0.382,1.162-0.671c0.405-0.406,0.722-0.951,0.948-1.637c0.227-0.686,0.34-1.517,0.34-2.493

			c0-1.354-0.222-2.394-0.666-3.121c-0.445-0.727-0.984-1.213-1.62-1.461c-0.459-0.175-1.198-0.265-2.216-0.265h-2.718

			L230.033,186.521L230.033,186.521z"/>

		<path fill="#FFFFFF" d="M241.181,176.935v-1.827h1.589v1.827H241.181z M241.181,188.049v-9.375h1.589v9.375H241.181z"/>

		<path fill="#FFFFFF" d="M244.553,185.25l1.571-0.247c0.088,0.63,0.334,1.112,0.737,1.447c0.402,0.335,0.966,0.503,1.689,0.503

			c0.73,0,1.271-0.147,1.625-0.445c0.353-0.297,0.529-0.645,0.529-1.046c0-0.36-0.155-0.642-0.468-0.847

			c-0.217-0.141-0.759-0.321-1.625-0.539c-1.165-0.294-1.973-0.549-2.423-0.764s-0.791-0.512-1.023-0.892

			c-0.232-0.379-0.348-0.799-0.348-1.257c0-0.417,0.096-0.805,0.287-1.161c0.191-0.356,0.452-0.652,0.781-0.887

			c0.248-0.182,0.584-0.336,1.012-0.463c0.427-0.127,0.883-0.189,1.372-0.189c0.736,0,1.382,0.105,1.938,0.317

			c0.557,0.212,0.968,0.498,1.232,0.86c0.266,0.363,0.448,0.846,0.548,1.452l-1.554,0.212c-0.071-0.482-0.275-0.86-0.613-1.131

			c-0.338-0.271-0.816-0.405-1.434-0.405c-0.73,0-1.25,0.121-1.562,0.362c-0.312,0.242-0.468,0.524-0.468,0.848

			c0,0.208,0.065,0.392,0.194,0.557c0.13,0.171,0.332,0.312,0.609,0.424c0.159,0.058,0.627,0.194,1.404,0.405

			c1.124,0.3,1.907,0.546,2.352,0.737c0.444,0.191,0.792,0.47,1.046,0.834c0.254,0.364,0.38,0.818,0.38,1.359

			c0,0.529-0.154,1.029-0.463,1.497s-0.754,0.83-1.337,1.085c-0.582,0.257-1.242,0.385-1.977,0.385

			c-1.218,0-2.147-0.253-2.786-0.759C245.137,186.995,244.73,186.245,244.553,185.25z"/>

		<path fill="#FFFFFF" d="M257.697,186.628l0.23,1.403c-0.448,0.096-0.848,0.142-1.2,0.142c-0.577,0-1.024-0.091-1.342-0.273

			c-0.317-0.183-0.541-0.423-0.67-0.72c-0.13-0.297-0.195-0.922-0.195-1.875v-5.394h-1.165v-1.236h1.165v-2.321l1.581-0.953v3.274

			h1.597v1.236H256.1v5.482c0,0.453,0.027,0.745,0.084,0.874c0.056,0.129,0.147,0.232,0.273,0.309s0.308,0.115,0.543,0.115

			C257.176,186.69,257.408,186.67,257.697,186.628z"/>

		<path fill="#FFFFFF" d="M259.233,188.049v-9.375h1.43v1.421c0.364-0.665,0.701-1.102,1.011-1.315

			c0.309-0.211,0.649-0.317,1.02-0.317c0.536,0,1.08,0.17,1.633,0.512l-0.547,1.474c-0.388-0.229-0.776-0.343-1.165-0.343

			c-0.347,0-0.659,0.104-0.936,0.313s-0.474,0.499-0.591,0.869c-0.177,0.565-0.265,1.182-0.265,1.854v4.908H259.233z"/>

		<path fill="#FFFFFF" d="M265.279,176.935v-1.827h1.589v1.827H265.279z M265.279,188.049v-9.375h1.589v9.375H265.279z"/>

		<path fill="#FFFFFF" d="M275.404,184.615l1.562,0.203c-0.171,1.076-0.607,1.919-1.311,2.529c-0.702,0.607-1.566,0.913-2.59,0.913

			c-1.283,0-2.314-0.42-3.094-1.257c-0.781-0.839-1.17-2.041-1.17-3.606c0-1.012,0.169-1.898,0.502-2.657

			c0.335-0.759,0.847-1.329,1.532-1.708c0.686-0.38,1.431-0.569,2.237-0.569c1.019,0,1.851,0.257,2.498,0.771

			c0.648,0.515,1.063,1.246,1.245,2.194l-1.545,0.238c-0.146-0.629-0.407-1.103-0.781-1.421c-0.374-0.318-0.825-0.477-1.354-0.477

			c-0.8,0-1.451,0.287-1.951,0.861c-0.501,0.574-0.75,1.483-0.75,2.723c0,1.26,0.241,2.175,0.724,2.745

			c0.482,0.572,1.112,0.857,1.889,0.857c0.624,0,1.144-0.191,1.562-0.573C275.027,185.999,275.292,185.408,275.404,184.615z"/>

		<path fill="#FFFFFF" d="M281.795,186.628l0.23,1.403c-0.448,0.096-0.848,0.142-1.201,0.142c-0.577,0-1.024-0.091-1.342-0.273

			c-0.317-0.183-0.542-0.423-0.671-0.72c-0.129-0.297-0.194-0.922-0.194-1.875v-5.394h-1.165v-1.236h1.165v-2.321l1.58-0.953v3.274

			h1.598v1.236h-1.598v5.482c0,0.453,0.028,0.745,0.085,0.874c0.056,0.129,0.146,0.232,0.273,0.309

			c0.126,0.076,0.308,0.115,0.543,0.115C281.273,186.69,281.506,186.67,281.795,186.628z"/>

		<path fill="#FFFFFF" d="M290.101,188.049l-2.869-9.375h1.642l1.491,5.411l0.557,2.012c0.022-0.098,0.186-0.743,0.485-1.933

			l1.492-5.491h1.634l1.403,5.439l0.467,1.791l0.539-1.81l1.606-5.42h1.545l-2.93,9.375h-1.651l-1.492-5.614l-0.362-1.597

			l-1.898,7.211H290.101z"/>

		<path fill="#FFFFFF" d="M301.435,176.935v-1.827h1.589v1.827H301.435z M301.435,188.049v-9.375h1.589v9.375H301.435z"/>

		<path fill="#FFFFFF" d="M304.805,185.25l1.571-0.247c0.089,0.63,0.334,1.112,0.737,1.447c0.404,0.335,0.968,0.503,1.69,0.503

			c0.73,0,1.271-0.147,1.625-0.445c0.353-0.297,0.529-0.645,0.529-1.046c0-0.36-0.156-0.642-0.468-0.847

			c-0.218-0.141-0.759-0.321-1.624-0.539c-1.166-0.294-1.974-0.549-2.423-0.764c-0.45-0.215-0.792-0.512-1.024-0.892

			c-0.233-0.379-0.349-0.799-0.349-1.257c0-0.417,0.097-0.805,0.288-1.161c0.19-0.356,0.451-0.652,0.78-0.887

			c0.249-0.182,0.584-0.336,1.011-0.463c0.427-0.127,0.885-0.189,1.373-0.189c0.736,0,1.382,0.105,1.938,0.317

			c0.557,0.213,0.966,0.498,1.231,0.86c0.265,0.363,0.447,0.846,0.548,1.452l-1.555,0.212c-0.07-0.482-0.274-0.86-0.613-1.131

			c-0.338-0.271-0.817-0.405-1.435-0.405c-0.729,0-1.25,0.121-1.562,0.362c-0.313,0.242-0.467,0.524-0.467,0.848

			c0,0.208,0.064,0.392,0.194,0.557c0.13,0.171,0.332,0.312,0.609,0.424c0.159,0.058,0.626,0.194,1.404,0.405

			c1.124,0.3,1.907,0.546,2.353,0.737c0.443,0.191,0.791,0.47,1.046,0.834c0.251,0.365,0.379,0.818,0.379,1.359

			c0,0.529-0.154,1.029-0.463,1.497c-0.31,0.468-0.755,0.83-1.337,1.085c-0.583,0.257-1.242,0.385-1.978,0.385

			c-1.218,0-2.147-0.253-2.786-0.759C305.39,186.995,304.982,186.245,304.805,185.25z"/>

		<path fill="#FFFFFF" d="M320.898,185.03l1.642,0.204c-0.258,0.959-0.739,1.704-1.439,2.233c-0.7,0.528-1.596,0.794-2.683,0.794

			c-1.372,0-2.458-0.423-3.261-1.267c-0.803-0.843-1.205-2.028-1.205-3.554c0-1.576,0.405-2.801,1.218-3.672

			c0.812-0.871,1.865-1.306,3.16-1.306c1.252,0,2.278,0.426,3.072,1.279c0.793,0.853,1.191,2.054,1.191,3.602

			c0,0.094-0.003,0.235-0.009,0.423h-6.991c0.058,1.029,0.35,1.819,0.873,2.366c0.524,0.547,1.177,0.82,1.96,0.82

			c0.583,0,1.079-0.152,1.492-0.457C320.33,186.19,320.656,185.701,320.898,185.03z M315.681,182.462h5.234

			c-0.07-0.79-0.271-1.381-0.6-1.775c-0.506-0.612-1.163-0.918-1.968-0.918c-0.73,0-1.344,0.245-1.841,0.733

			C316.009,180.99,315.733,181.643,315.681,182.462z"/>

	</g>

	<g enable-background="new    ">

		<path fill="#FFFFFF" d="M230.091,209.739v-11.413h-4.264v-1.527h10.257v1.527h-4.281v11.413H230.091z"/>

		<path fill="#FFFFFF" d="M241.752,208.584c-0.588,0.5-1.155,0.853-1.699,1.059c-0.544,0.207-1.129,0.31-1.753,0.31

			c-1.03,0-1.821-0.251-2.374-0.755c-0.554-0.503-0.83-1.146-0.83-1.929c0-0.458,0.105-0.878,0.313-1.258

			c0.209-0.379,0.483-0.684,0.821-0.912c0.338-0.229,0.72-0.403,1.143-0.521c0.312-0.083,0.783-0.162,1.413-0.238

			c1.282-0.154,2.227-0.336,2.833-0.548c0.006-0.218,0.01-0.356,0.01-0.415c0-0.646-0.15-1.104-0.45-1.367

			c-0.406-0.359-1.009-0.539-1.809-0.539c-0.748,0-1.3,0.132-1.655,0.394s-0.619,0.725-0.791,1.391l-1.553-0.212

			c0.142-0.665,0.374-1.202,0.697-1.611c0.325-0.408,0.792-0.723,1.404-0.944c0.612-0.22,1.321-0.33,2.127-0.33

			c0.8,0,1.451,0.095,1.951,0.283s0.867,0.426,1.103,0.711c0.235,0.286,0.401,0.646,0.495,1.081c0.053,0.271,0.08,0.759,0.08,1.466

			v2.117c0,1.479,0.033,2.412,0.102,2.804c0.067,0.392,0.201,0.766,0.401,1.125h-1.66

			C241.904,209.411,241.798,209.025,241.752,208.584z M241.618,205.036c-0.577,0.235-1.441,0.435-2.594,0.6

			c-0.654,0.095-1.116,0.201-1.386,0.318c-0.271,0.117-0.48,0.288-0.626,0.516c-0.147,0.227-0.221,0.479-0.221,0.755

			c0,0.424,0.16,0.777,0.482,1.059c0.32,0.283,0.79,0.424,1.407,0.424c0.612,0,1.156-0.134,1.632-0.401

			c0.478-0.268,0.828-0.636,1.051-1.1c0.17-0.358,0.255-0.889,0.255-1.589V205.036L241.618,205.036z"/>

		<path fill="#FFFFFF" d="M245.67,209.739v-9.374h1.429v1.421c0.365-0.666,0.701-1.103,1.011-1.315

			c0.309-0.211,0.649-0.317,1.019-0.317c0.536,0,1.079,0.171,1.633,0.512l-0.547,1.475c-0.388-0.229-0.776-0.345-1.166-0.345

			c-0.347,0-0.659,0.105-0.935,0.313c-0.277,0.21-0.474,0.499-0.591,0.87c-0.177,0.564-0.266,1.182-0.266,1.854v4.907H245.67z"/>

		<path fill="#FFFFFF" d="M251.417,210.516l1.545,0.229c0.065,0.478,0.244,0.824,0.539,1.041c0.395,0.294,0.933,0.442,1.616,0.442

			c0.735,0,1.304-0.147,1.704-0.442c0.401-0.293,0.671-0.705,0.812-1.235c0.082-0.324,0.12-1.004,0.115-2.039

			c-0.694,0.819-1.559,1.227-2.595,1.227c-1.29,0-2.287-0.465-2.993-1.394c-0.706-0.931-1.059-2.045-1.059-3.346

			c0-0.896,0.162-1.72,0.485-2.477c0.324-0.757,0.793-1.34,1.408-1.752c0.615-0.411,1.336-0.617,2.167-0.617

			c1.106,0,2.018,0.446,2.736,1.341v-1.13h1.465v8.104c0,1.46-0.149,2.494-0.445,3.103c-0.298,0.608-0.768,1.091-1.413,1.443

			c-0.644,0.353-1.438,0.529-2.379,0.529c-1.118,0-2.021-0.251-2.71-0.755C251.725,212.286,251.392,211.529,251.417,210.516z

			 M252.732,204.885c0,1.229,0.244,2.128,0.733,2.692c0.488,0.565,1.1,0.848,1.836,0.848c0.73,0,1.342-0.281,1.835-0.844

			c0.495-0.561,0.742-1.443,0.742-2.644c0-1.146-0.254-2.013-0.763-2.596c-0.509-0.582-1.123-0.873-1.841-0.873

			c-0.706,0-1.307,0.288-1.801,0.861C252.979,202.903,252.732,203.756,252.732,204.885z"/>

		<path fill="#FFFFFF" d="M268.178,206.721l1.642,0.203c-0.259,0.959-0.738,1.704-1.439,2.232c-0.7,0.53-1.594,0.795-2.683,0.795

			c-1.372,0-2.458-0.422-3.262-1.267c-0.803-0.844-1.205-2.028-1.205-3.553c0-1.577,0.405-2.802,1.217-3.673

			c0.813-0.871,1.866-1.306,3.16-1.306c1.253,0,2.278,0.426,3.072,1.279c0.794,0.854,1.192,2.055,1.192,3.602

			c0,0.094-0.003,0.235-0.009,0.423h-6.991c0.059,1.03,0.35,1.819,0.874,2.366c0.524,0.548,1.178,0.821,1.96,0.821

			c0.582,0,1.079-0.152,1.492-0.458C267.608,207.882,267.938,207.391,268.178,206.721z M262.962,204.152h5.235

			c-0.071-0.788-0.271-1.381-0.6-1.774c-0.506-0.612-1.162-0.918-1.968-0.918c-0.73,0-1.343,0.244-1.841,0.732

			C263.29,202.68,263.015,203.335,262.962,204.152z"/>

		<path fill="#FFFFFF" d="M275.284,208.318l0.23,1.404c-0.448,0.095-0.848,0.141-1.201,0.141c-0.577,0-1.024-0.091-1.342-0.273

			c-0.318-0.182-0.542-0.422-0.671-0.719c-0.129-0.298-0.194-0.923-0.194-1.876v-5.393h-1.165v-1.236h1.165v-2.322l1.58-0.952v3.274

			h1.598v1.236h-1.598v5.481c0,0.453,0.028,0.745,0.084,0.873c0.057,0.13,0.147,0.233,0.274,0.31

			c0.126,0.076,0.308,0.115,0.543,0.115C274.764,208.382,274.996,208.359,275.284,208.318z"/>

		<path fill="#FFFFFF" d="M282.099,209.739v-12.94h9.356v1.527h-7.645v3.964h7.159v1.518h-7.159v4.406h7.945v1.525H282.099

			L282.099,209.739z"/>

		<path fill="#FFFFFF" d="M293.918,209.739v-9.374h1.43v1.332c0.689-1.029,1.683-1.544,2.984-1.544c0.565,0,1.083,0.101,1.558,0.304

			c0.474,0.203,0.828,0.47,1.063,0.799c0.236,0.329,0.4,0.721,0.494,1.174c0.059,0.294,0.089,0.81,0.089,1.545v5.764h-1.59v-5.702

			c0-0.647-0.061-1.131-0.185-1.452c-0.124-0.32-0.342-0.576-0.658-0.768c-0.315-0.19-0.683-0.287-1.107-0.287

			c-0.677,0-1.261,0.216-1.752,0.646c-0.492,0.429-0.737,1.244-0.737,2.444v5.119L293.918,209.739L293.918,209.739z"/>

		<path fill="#FFFFFF" d="M307.441,208.318l0.23,1.404c-0.448,0.095-0.848,0.141-1.2,0.141c-0.578,0-1.024-0.091-1.342-0.273

			c-0.317-0.182-0.542-0.422-0.672-0.719c-0.129-0.298-0.194-0.923-0.194-1.876v-5.393h-1.165v-1.236h1.165v-2.322l1.58-0.952v3.274

			h1.598v1.236h-1.598v5.481c0,0.453,0.028,0.745,0.085,0.873c0.056,0.13,0.146,0.233,0.273,0.31

			c0.126,0.076,0.308,0.115,0.543,0.115C306.92,208.382,307.153,208.359,307.441,208.318z"/>

		<path fill="#FFFFFF" d="M308.977,209.739v-9.374h1.43v1.421c0.365-0.666,0.702-1.103,1.011-1.315

			c0.309-0.211,0.648-0.317,1.021-0.317c0.535,0,1.08,0.171,1.633,0.512l-0.548,1.475c-0.388-0.229-0.777-0.345-1.165-0.345

			c-0.347,0-0.659,0.105-0.937,0.313c-0.276,0.21-0.474,0.499-0.591,0.87c-0.177,0.564-0.264,1.182-0.264,1.854v4.907H308.977z"/>

		<path fill="#FFFFFF" d="M314.944,213.351l-0.176-1.493c0.347,0.096,0.65,0.142,0.909,0.142c0.353,0,0.635-0.059,0.848-0.177

			c0.211-0.117,0.385-0.282,0.52-0.493c0.1-0.159,0.262-0.554,0.485-1.183c0.03-0.089,0.077-0.218,0.142-0.388l-3.559-9.393h1.713

			l1.95,5.429c0.252,0.688,0.48,1.411,0.68,2.171c0.182-0.729,0.4-1.441,0.653-2.136l2.003-5.464h1.589l-3.565,9.532

			c-0.382,1.029-0.679,1.74-0.892,2.128c-0.283,0.523-0.605,0.907-0.972,1.152c-0.365,0.244-0.8,0.365-1.306,0.365

			C315.662,213.545,315.321,213.479,314.944,213.351z"/>

	</g>

	<g enable-background="new    ">

		<path fill="#FFFFFF" d="M342.613,285.856v-12.94h4.458c1.006,0,1.774,0.062,2.304,0.186c0.741,0.17,1.374,0.479,1.898,0.927

			c0.682,0.576,1.193,1.313,1.531,2.211c0.338,0.898,0.507,1.923,0.507,3.076c0,0.982-0.115,1.854-0.343,2.612

			c-0.23,0.76-0.524,1.388-0.883,1.886c-0.36,0.497-0.752,0.888-1.179,1.174s-0.942,0.503-1.545,0.647

			c-0.603,0.148-1.296,0.222-2.078,0.222H342.613L342.613,285.856z M344.326,284.328h2.763c0.854,0,1.522-0.079,2.009-0.238

			c0.486-0.158,0.872-0.383,1.16-0.671c0.406-0.406,0.722-0.951,0.949-1.638c0.227-0.686,0.34-1.517,0.34-2.493

			c0-1.354-0.222-2.394-0.667-3.12c-0.444-0.726-0.984-1.213-1.62-1.461c-0.459-0.176-1.197-0.264-2.214-0.264h-2.719V284.328

			L344.326,284.328z"/>

		<path fill="#FFFFFF" d="M355.474,274.742v-1.827h1.589v1.827H355.474z M355.474,285.856v-9.375h1.589v9.375H355.474z"/>

		<path fill="#FFFFFF" d="M358.846,283.056l1.571-0.247c0.089,0.63,0.334,1.112,0.737,1.447c0.404,0.335,0.967,0.504,1.69,0.504

			c0.73,0,1.271-0.148,1.625-0.446c0.353-0.297,0.53-0.645,0.53-1.046c0-0.359-0.156-0.642-0.468-0.847

			c-0.218-0.141-0.759-0.321-1.624-0.539c-1.167-0.294-1.975-0.548-2.423-0.764c-0.45-0.215-0.792-0.512-1.024-0.892

			c-0.233-0.379-0.349-0.799-0.349-1.257c0-0.418,0.097-0.805,0.288-1.161c0.19-0.356,0.45-0.652,0.78-0.888

			c0.248-0.182,0.584-0.337,1.011-0.463c0.427-0.126,0.885-0.189,1.373-0.189c0.735,0,1.381,0.105,1.938,0.317

			c0.556,0.213,0.966,0.498,1.231,0.861c0.265,0.361,0.447,0.847,0.547,1.451l-1.554,0.212c-0.07-0.481-0.274-0.859-0.613-1.13

			s-0.817-0.406-1.435-0.406c-0.729,0-1.25,0.121-1.562,0.362c-0.312,0.242-0.467,0.524-0.467,0.848

			c0,0.207,0.064,0.393,0.194,0.557c0.129,0.171,0.332,0.312,0.609,0.424c0.159,0.058,0.626,0.194,1.403,0.405

			c1.124,0.3,1.908,0.547,2.353,0.738c0.444,0.191,0.792,0.469,1.047,0.833c0.251,0.365,0.378,0.817,0.378,1.359

			c0,0.529-0.154,1.028-0.463,1.496c-0.31,0.469-0.754,0.83-1.336,1.086c-0.584,0.256-1.243,0.385-1.979,0.385

			c-1.217,0-2.146-0.254-2.785-0.759C359.429,284.802,359.023,284.052,358.846,283.056z"/>

		<path fill="#FFFFFF" d="M371.99,284.433l0.229,1.404c-0.446,0.095-0.847,0.141-1.2,0.141c-0.577,0-1.023-0.091-1.342-0.273

			c-0.317-0.182-0.542-0.422-0.671-0.719c-0.129-0.297-0.194-0.923-0.194-1.875v-5.394h-1.165v-1.236h1.165v-2.321l1.58-0.952v3.273

			h1.598v1.236h-1.598v5.481c0,0.453,0.028,0.745,0.084,0.874c0.057,0.13,0.147,0.232,0.273,0.31

			c0.127,0.075,0.308,0.114,0.543,0.114C371.469,284.497,371.701,284.475,371.99,284.433z"/>

		<path fill="#FFFFFF" d="M373.525,285.856v-9.375h1.43v1.421c0.366-0.665,0.702-1.103,1.011-1.315

			c0.31-0.211,0.649-0.317,1.02-0.317c0.535,0,1.08,0.171,1.634,0.512l-0.548,1.475c-0.389-0.229-0.778-0.344-1.166-0.344

			c-0.348,0-0.659,0.104-0.936,0.313c-0.276,0.209-0.474,0.499-0.591,0.868c-0.177,0.565-0.265,1.183-0.265,1.855v4.907H373.525z"/>

		<path fill="#FFFFFF" d="M379.571,274.742v-1.827h1.59v1.827H379.571z M379.571,285.856v-9.375h1.59v9.375H379.571z"/>

		<path fill="#FFFFFF" d="M389.696,282.423l1.562,0.202c-0.17,1.077-0.607,1.92-1.311,2.529c-0.704,0.609-1.567,0.914-2.591,0.914

			c-1.284,0-2.315-0.42-3.094-1.258c-0.781-0.839-1.17-2.041-1.17-3.606c0-1.012,0.167-1.897,0.503-2.657

			c0.335-0.759,0.846-1.328,1.531-1.707c0.686-0.38,1.432-0.569,2.238-0.569c1.018,0,1.85,0.257,2.498,0.771

			c0.647,0.517,1.061,1.246,1.244,2.194l-1.545,0.238c-0.147-0.63-0.408-1.103-0.781-1.422c-0.374-0.317-0.826-0.477-1.355-0.477

			c-0.8,0-1.45,0.287-1.95,0.861c-0.5,0.573-0.751,1.482-0.751,2.723c0,1.26,0.242,2.176,0.724,2.745

			c0.483,0.571,1.113,0.857,1.889,0.857c0.624,0,1.144-0.191,1.563-0.574C389.319,283.805,389.584,283.216,389.696,282.423z"/>

		<path fill="#FFFFFF" d="M396.086,284.433l0.23,1.404c-0.447,0.095-0.847,0.141-1.2,0.141c-0.578,0-1.023-0.091-1.342-0.273

			c-0.318-0.182-0.542-0.422-0.671-0.719s-0.194-0.923-0.194-1.875v-5.394h-1.165v-1.236h1.165v-2.321l1.581-0.952v3.273h1.597

			v1.236h-1.597v5.481c0,0.453,0.028,0.745,0.083,0.874c0.056,0.13,0.147,0.232,0.273,0.31c0.126,0.075,0.308,0.114,0.543,0.114

			C395.566,284.497,395.799,284.475,396.086,284.433z"/>

		<path fill="#FFFFFF" d="M404.393,285.856l-2.868-9.375h1.641l1.492,5.41l0.556,2.013c0.023-0.099,0.186-0.744,0.485-1.933

			l1.492-5.49h1.633l1.404,5.438l0.467,1.79l0.539-1.81l1.606-5.419h1.545l-2.93,9.375h-1.652l-1.492-5.614l-0.362-1.598

			l-1.898,7.212H404.393L404.393,285.856z"/>

		<path fill="#FFFFFF" d="M415.726,274.742v-1.827h1.59v1.827H415.726z M415.726,285.856v-9.375h1.59v9.375H415.726z"/>

		<path fill="#FFFFFF" d="M419.099,283.056l1.571-0.247c0.088,0.63,0.333,1.112,0.737,1.447c0.403,0.335,0.966,0.504,1.69,0.504

			c0.73,0,1.272-0.148,1.625-0.446c0.353-0.297,0.53-0.645,0.53-1.046c0-0.359-0.156-0.642-0.468-0.847

			c-0.218-0.141-0.759-0.321-1.624-0.539c-1.167-0.294-1.975-0.548-2.423-0.764c-0.45-0.215-0.791-0.512-1.024-0.892

			c-0.233-0.379-0.349-0.799-0.349-1.257c0-0.418,0.096-0.805,0.287-1.161c0.19-0.356,0.451-0.652,0.782-0.888

			c0.247-0.182,0.583-0.337,1.011-0.463c0.427-0.126,0.884-0.189,1.373-0.189c0.735,0,1.381,0.105,1.938,0.317

			c0.556,0.213,0.966,0.498,1.232,0.861c0.264,0.361,0.446,0.847,0.547,1.451l-1.554,0.212c-0.07-0.481-0.274-0.859-0.613-1.13

			s-0.817-0.406-1.435-0.406c-0.729,0-1.25,0.121-1.562,0.362c-0.312,0.242-0.467,0.524-0.467,0.848

			c0,0.207,0.063,0.393,0.194,0.557c0.129,0.171,0.332,0.312,0.609,0.424c0.159,0.058,0.626,0.194,1.403,0.405

			c1.124,0.3,1.908,0.547,2.353,0.738c0.444,0.191,0.792,0.469,1.046,0.833c0.253,0.365,0.379,0.817,0.379,1.359

			c0,0.529-0.154,1.028-0.463,1.496c-0.31,0.469-0.754,0.83-1.337,1.086s-1.242,0.385-1.977,0.385c-1.218,0-2.147-0.254-2.785-0.759

			C419.683,284.802,419.276,284.052,419.099,283.056z"/>

		<path fill="#FFFFFF" d="M435.19,282.838l1.643,0.203c-0.259,0.959-0.74,1.703-1.44,2.232c-0.7,0.529-1.594,0.794-2.683,0.794

			c-1.371,0-2.458-0.422-3.261-1.267c-0.803-0.844-1.205-2.028-1.205-3.554c0-1.576,0.405-2.8,1.216-3.672

			c0.812-0.871,1.866-1.306,3.16-1.306c1.254,0,2.278,0.426,3.072,1.279c0.794,0.854,1.191,2.056,1.191,3.603

			c0,0.094-0.002,0.235-0.008,0.423h-6.991c0.059,1.029,0.35,1.818,0.874,2.365c0.523,0.548,1.176,0.822,1.96,0.822

			c0.582,0,1.08-0.152,1.491-0.459C434.623,283.997,434.949,283.508,435.19,282.838z M429.973,280.269h5.234

			c-0.069-0.79-0.271-1.381-0.599-1.775c-0.506-0.612-1.162-0.918-1.968-0.918c-0.73,0-1.344,0.245-1.841,0.733

			C430.302,278.797,430.027,279.451,429.973,280.269z"/>

	</g>

	<g enable-background="new    ">

		<path fill="#FFFFFF" d="M334.632,307.548v-12.939h8.73v1.526h-7.017v4.008h6.071v1.527h-6.071v5.878H334.632L334.632,307.548z"/>

		<path fill="#FFFFFF" d="M351.528,307.548v-1.376c-0.729,1.059-1.721,1.59-2.975,1.59c-0.554,0-1.069-0.107-1.549-0.318

			c-0.48-0.212-0.836-0.479-1.068-0.8c-0.232-0.32-0.396-0.713-0.49-1.177c-0.065-0.313-0.097-0.808-0.097-1.483v-5.808h1.589v5.198

			c0,0.831,0.032,1.391,0.096,1.678c0.1,0.417,0.313,0.746,0.636,0.984c0.323,0.238,0.724,0.357,1.201,0.357

			c0.477,0,0.924-0.123,1.342-0.366c0.418-0.244,0.712-0.576,0.887-0.998c0.174-0.42,0.26-1.03,0.26-1.831v-5.022h1.589v9.374

			h-1.422V307.548z"/>

		<path fill="#FFFFFF" d="M355.438,307.548v-9.374h1.43v1.333c0.689-1.03,1.683-1.545,2.984-1.545c0.565,0,1.083,0.101,1.559,0.304

			c0.474,0.204,0.827,0.471,1.063,0.799c0.236,0.33,0.4,0.721,0.495,1.175c0.059,0.293,0.089,0.81,0.089,1.545v5.764h-1.59v-5.703

			c0-0.646-0.061-1.131-0.185-1.451c-0.124-0.321-0.342-0.576-0.658-0.768c-0.315-0.19-0.683-0.288-1.107-0.288

			c-0.677,0-1.261,0.216-1.752,0.646c-0.492,0.43-0.737,1.244-0.737,2.444v5.12H355.438L355.438,307.548z"/>

		<path fill="#FFFFFF" d="M371.574,307.548v-1.182c-0.596,0.931-1.468,1.396-2.622,1.396c-0.749,0-1.436-0.207-2.061-0.618

			c-0.627-0.412-1.113-0.987-1.457-1.726c-0.345-0.738-0.517-1.588-0.517-2.547c0-0.936,0.155-1.784,0.468-2.547

			c0.312-0.762,0.78-1.347,1.403-1.753c0.625-0.405,1.322-0.608,2.093-0.608c0.564,0,1.068,0.12,1.51,0.357

			c0.442,0.238,0.8,0.549,1.077,0.932v-4.643h1.58v12.939L371.574,307.548L371.574,307.548z M366.551,302.871

			c0,1.2,0.252,2.098,0.759,2.691c0.506,0.595,1.104,0.892,1.792,0.892c0.694,0,1.284-0.283,1.769-0.852

			c0.486-0.567,0.729-1.434,0.729-2.6c0-1.283-0.248-2.224-0.742-2.825c-0.494-0.6-1.102-0.9-1.827-0.9

			c-0.708,0-1.297,0.289-1.77,0.865C366.788,300.719,366.551,301.629,366.551,302.871z"/>

		<path fill="#FFFFFF" d="M380.798,307.548v-12.939h5.737c1.153,0,2.03,0.116,2.631,0.348c0.599,0.233,1.08,0.644,1.439,1.232

			c0.358,0.589,0.538,1.239,0.538,1.95c0,0.918-0.297,1.692-0.892,2.322s-1.512,1.029-2.753,1.2

			c0.453,0.217,0.797,0.433,1.033,0.644c0.5,0.46,0.974,1.033,1.421,1.722l2.251,3.521h-2.154l-1.713-2.691

			c-0.5-0.777-0.912-1.371-1.235-1.783c-0.324-0.412-0.614-0.7-0.87-0.865c-0.256-0.166-0.517-0.28-0.782-0.344

			c-0.194-0.042-0.512-0.063-0.953-0.063h-1.985v5.746H380.798z M382.51,300.319h3.68c0.784,0,1.396-0.08,1.836-0.243

			c0.441-0.162,0.776-0.421,1.006-0.777c0.229-0.355,0.344-0.742,0.344-1.16c0-0.612-0.222-1.114-0.666-1.509

			c-0.445-0.395-1.146-0.593-2.105-0.593h-4.096L382.51,300.319L382.51,300.319z"/>

		<path fill="#FFFFFF" d="M400.04,304.53l1.642,0.203c-0.259,0.959-0.739,1.705-1.44,2.232c-0.7,0.53-1.595,0.796-2.682,0.796

			c-1.372,0-2.458-0.423-3.262-1.268c-0.803-0.844-1.205-2.028-1.205-3.553c0-1.577,0.405-2.802,1.217-3.673

			c0.812-0.87,1.866-1.306,3.16-1.306c1.253,0,2.278,0.426,3.072,1.28c0.794,0.853,1.191,2.054,1.191,3.601

			c0,0.095-0.003,0.235-0.008,0.423h-6.991c0.059,1.03,0.35,1.819,0.874,2.366c0.524,0.548,1.177,0.821,1.96,0.821

			c0.582,0,1.079-0.152,1.492-0.458C399.471,305.689,399.799,305.2,400.04,304.53z M394.824,301.961h5.233

			c-0.069-0.788-0.271-1.381-0.599-1.774c-0.506-0.612-1.163-0.918-1.968-0.918c-0.73,0-1.344,0.245-1.841,0.732

			C395.152,300.489,394.876,301.142,394.824,301.961z"/>

		<path fill="#FFFFFF" d="M403.642,307.548v-12.939h1.589v12.939H403.642L403.642,307.548z"/>

		<path fill="#FFFFFF" d="M414.11,304.53l1.643,0.203c-0.259,0.959-0.739,1.705-1.439,2.232c-0.701,0.53-1.596,0.796-2.683,0.796

			c-1.372,0-2.458-0.423-3.261-1.268c-0.803-0.844-1.205-2.028-1.205-3.553c0-1.577,0.406-2.802,1.218-3.673

			c0.812-0.87,1.865-1.306,3.16-1.306c1.252,0,2.278,0.426,3.072,1.28c0.793,0.853,1.19,2.054,1.19,3.601

			c0,0.095-0.002,0.235-0.008,0.423h-6.991c0.058,1.03,0.35,1.819,0.873,2.366c0.524,0.548,1.177,0.821,1.96,0.821

			c0.582,0,1.079-0.152,1.492-0.458C413.543,305.689,413.87,305.2,414.11,304.53z M408.895,301.961h5.234

			c-0.07-0.788-0.271-1.381-0.6-1.774c-0.506-0.612-1.163-0.918-1.968-0.918c-0.73,0-1.344,0.245-1.841,0.732

			C409.223,300.489,408.947,301.142,408.895,301.961z"/>

		<path fill="#FFFFFF" d="M423.865,306.392c-0.589,0.5-1.155,0.854-1.699,1.06c-0.544,0.206-1.13,0.31-1.752,0.31

			c-1.03,0-1.821-0.252-2.374-0.756c-0.554-0.503-0.829-1.145-0.829-1.929c0-0.458,0.104-0.878,0.313-1.257

			c0.209-0.379,0.483-0.685,0.821-0.913c0.339-0.229,0.719-0.403,1.143-0.521c0.312-0.083,0.783-0.162,1.412-0.238

			c1.283-0.154,2.228-0.336,2.833-0.548c0.007-0.218,0.01-0.355,0.01-0.414c0-0.647-0.15-1.104-0.45-1.368

			c-0.406-0.359-1.009-0.538-1.81-0.538c-0.747,0-1.299,0.131-1.655,0.393c-0.356,0.263-0.619,0.726-0.79,1.391l-1.554-0.212

			c0.142-0.665,0.374-1.202,0.697-1.61c0.324-0.409,0.792-0.724,1.404-0.944c0.612-0.221,1.321-0.33,2.127-0.33

			c0.801,0,1.451,0.094,1.951,0.282s0.867,0.426,1.103,0.711c0.234,0.286,0.4,0.646,0.494,1.081

			c0.053,0.271,0.079,0.759,0.079,1.466v2.117c0,1.479,0.035,2.412,0.102,2.804c0.067,0.392,0.201,0.766,0.402,1.125h-1.66

			C424.017,307.219,423.911,306.834,423.865,306.392z M423.731,302.844c-0.577,0.235-1.442,0.436-2.596,0.601

			c-0.652,0.094-1.114,0.2-1.385,0.317c-0.271,0.117-0.481,0.29-0.627,0.516c-0.147,0.228-0.22,0.479-0.22,0.755

			c0,0.424,0.16,0.777,0.481,1.06s0.79,0.424,1.408,0.424c0.612,0,1.156-0.135,1.633-0.402c0.478-0.268,0.828-0.635,1.051-1.099

			c0.171-0.359,0.255-0.89,0.255-1.59V302.844L423.731,302.844z"/>

		<path fill="#FFFFFF" d="M427.165,304.751l1.571-0.248c0.089,0.631,0.334,1.112,0.737,1.448c0.404,0.335,0.967,0.502,1.69,0.502

			c0.73,0,1.271-0.147,1.625-0.445c0.353-0.296,0.529-0.645,0.529-1.046c0-0.359-0.156-0.641-0.468-0.848

			c-0.216-0.14-0.759-0.32-1.624-0.538c-1.166-0.294-1.974-0.548-2.423-0.764c-0.451-0.214-0.791-0.512-1.024-0.892

			c-0.233-0.379-0.348-0.799-0.348-1.257c0-0.417,0.096-0.805,0.288-1.162c0.19-0.355,0.451-0.65,0.781-0.887

			c0.248-0.182,0.584-0.336,1.011-0.463c0.428-0.127,0.884-0.189,1.373-0.189c0.735,0,1.382,0.105,1.938,0.318

			c0.557,0.212,0.967,0.498,1.232,0.859c0.264,0.362,0.447,0.847,0.548,1.452l-1.555,0.212c-0.07-0.481-0.274-0.859-0.613-1.13

			c-0.337-0.271-0.816-0.406-1.435-0.406c-0.729,0-1.25,0.122-1.563,0.362c-0.312,0.242-0.467,0.523-0.467,0.848

			c0,0.207,0.064,0.392,0.194,0.556c0.13,0.173,0.333,0.313,0.609,0.425c0.159,0.059,0.626,0.193,1.404,0.405

			c1.124,0.3,1.907,0.547,2.353,0.737c0.443,0.191,0.792,0.47,1.046,0.834c0.252,0.365,0.379,0.818,0.379,1.359

			c0,0.529-0.154,1.028-0.463,1.496c-0.31,0.469-0.755,0.829-1.337,1.086c-0.583,0.256-1.242,0.385-1.977,0.385

			c-1.218,0-2.147-0.254-2.786-0.76C427.75,306.495,427.343,305.745,427.165,304.751z"/>

		<path fill="#FFFFFF" d="M443.257,304.53l1.642,0.203c-0.259,0.959-0.739,1.705-1.439,2.232c-0.701,0.53-1.595,0.796-2.683,0.796

			c-1.372,0-2.458-0.423-3.262-1.268c-0.803-0.844-1.205-2.028-1.205-3.553c0-1.577,0.406-2.802,1.217-3.673

			c0.813-0.87,1.865-1.306,3.161-1.306c1.252,0,2.277,0.426,3.071,1.28c0.794,0.853,1.191,2.054,1.191,3.601

			c0,0.095-0.003,0.235-0.008,0.423h-6.99c0.058,1.03,0.35,1.819,0.873,2.366c0.524,0.548,1.177,0.821,1.96,0.821

			c0.582,0,1.08-0.152,1.492-0.458C442.69,305.689,443.016,305.2,443.257,304.53z M438.041,301.961h5.234

			c-0.07-0.788-0.271-1.381-0.6-1.774c-0.506-0.612-1.162-0.918-1.968-0.918c-0.73,0-1.344,0.245-1.841,0.732

			C438.37,300.489,438.093,301.142,438.041,301.961z"/>

	</g>

	<g enable-background="new    ">

		<path fill="#FFFFFF" d="M455.284,188.046v-12.94h4.855c0.988,0,1.781,0.131,2.379,0.393c0.596,0.262,1.064,0.665,1.403,1.209

			c0.339,0.544,0.507,1.114,0.507,1.709c0,0.553-0.149,1.074-0.448,1.562c-0.301,0.488-0.754,0.882-1.361,1.183

			c0.784,0.229,1.384,0.621,1.806,1.174c0.42,0.554,0.632,1.207,0.632,1.959c0,0.606-0.129,1.17-0.385,1.69

			c-0.255,0.521-0.572,0.922-0.948,1.206c-0.376,0.282-0.849,0.495-1.417,0.639c-0.566,0.145-1.263,0.217-2.087,0.217H455.284

			L455.284,188.046z M456.996,180.543h2.798c0.76,0,1.304-0.05,1.633-0.15c0.436-0.13,0.764-0.345,0.984-0.645

			c0.221-0.3,0.33-0.676,0.33-1.13c0-0.429-0.102-0.808-0.309-1.135c-0.206-0.327-0.5-0.55-0.882-0.671

			c-0.382-0.12-1.039-0.18-1.969-0.18h-2.586L456.996,180.543L456.996,180.543z M456.996,186.517h3.222

			c0.553,0,0.941-0.02,1.165-0.063c0.394-0.068,0.724-0.186,0.988-0.352c0.266-0.165,0.482-0.404,0.653-0.719

			c0.171-0.315,0.256-0.678,0.256-1.091c0-0.482-0.124-0.901-0.37-1.257c-0.248-0.356-0.591-0.605-1.028-0.75

			c-0.439-0.145-1.07-0.216-1.894-0.216h-2.993V186.517L456.996,186.517z"/>

		<path fill="#FFFFFF" d="M467.174,188.046v-12.94h1.589v12.94H467.174z"/>

		<path fill="#FFFFFF" d="M470.634,183.358c0-1.735,0.482-3.021,1.447-3.857c0.807-0.695,1.789-1.042,2.948-1.042

			c1.289,0,2.341,0.422,3.161,1.266c0.817,0.845,1.226,2.012,1.226,3.5c0,1.207-0.18,2.156-0.543,2.848

			c-0.362,0.69-0.889,1.228-1.581,1.61c-0.691,0.382-1.445,0.573-2.263,0.573c-1.313,0-2.373-0.421-3.182-1.263

			C471.039,186.154,470.634,184.94,470.634,183.358z M472.268,183.358c0,1.2,0.262,2.1,0.785,2.697

			c0.523,0.597,1.183,0.896,1.978,0.896c0.789,0,1.444-0.299,1.968-0.901c0.523-0.6,0.786-1.515,0.786-2.744

			c0-1.16-0.264-2.038-0.79-2.636c-0.526-0.596-1.181-0.896-1.963-0.896c-0.794,0-1.454,0.297-1.979,0.892

			C472.531,181.261,472.268,182.158,472.268,183.358z"/>

		<path fill="#FFFFFF" d="M487.397,184.612l1.561,0.203c-0.17,1.077-0.606,1.919-1.31,2.529c-0.704,0.608-1.567,0.913-2.591,0.913

			c-1.284,0-2.315-0.419-3.094-1.257c-0.782-0.838-1.171-2.041-1.171-3.606c0-1.012,0.169-1.897,0.503-2.657

			c0.336-0.759,0.846-1.329,1.532-1.708c0.686-0.38,1.431-0.569,2.238-0.569c1.019,0,1.85,0.256,2.498,0.771

			c0.647,0.516,1.062,1.247,1.245,2.194l-1.545,0.238c-0.146-0.629-0.407-1.103-0.782-1.421c-0.374-0.317-0.825-0.477-1.354-0.477

			c-0.8,0-1.45,0.287-1.951,0.861c-0.5,0.574-0.75,1.482-0.75,2.723c0,1.26,0.241,2.175,0.723,2.745

			c0.482,0.571,1.112,0.856,1.89,0.856c0.624,0,1.143-0.191,1.562-0.574C487.019,185.994,487.285,185.405,487.397,184.612z"/>

		<path fill="#FFFFFF" d="M490.327,188.046v-12.94h1.589v7.378l3.76-3.813h2.057l-3.584,3.479l3.947,5.896h-1.96l-3.099-4.794

			l-1.121,1.077v3.716L490.327,188.046L490.327,188.046z"/>

		<path fill="#FFFFFF" d="M506.111,188.046l-2.87-9.375h1.642l1.492,5.411l0.556,2.012c0.023-0.099,0.186-0.744,0.486-1.933

			l1.491-5.49h1.634l1.403,5.439l0.467,1.791l0.54-1.81l1.605-5.42h1.545l-2.93,9.375h-1.652l-1.491-5.614l-0.363-1.598

			l-1.898,7.212H506.111z"/>

		<path fill="#FFFFFF" d="M517.444,176.931v-1.827h1.589v1.827H517.444z M517.444,188.046v-9.375h1.589v9.375H517.444z"/>

		<path fill="#FFFFFF" d="M520.815,185.246l1.57-0.248c0.089,0.63,0.334,1.113,0.738,1.448c0.403,0.334,0.966,0.502,1.689,0.502

			c0.73,0,1.272-0.148,1.625-0.445c0.353-0.296,0.529-0.645,0.529-1.045c0-0.36-0.156-0.642-0.468-0.847

			c-0.217-0.141-0.758-0.321-1.624-0.539c-1.166-0.294-1.974-0.549-2.423-0.763c-0.45-0.215-0.791-0.513-1.023-0.892

			c-0.233-0.379-0.349-0.798-0.349-1.257c0-0.418,0.096-0.805,0.287-1.162c0.19-0.355,0.451-0.651,0.781-0.887

			c0.248-0.183,0.583-0.337,1.011-0.463c0.427-0.127,0.884-0.189,1.373-0.189c0.735,0,1.381,0.105,1.938,0.318

			c0.557,0.213,0.966,0.498,1.232,0.86c0.264,0.363,0.446,0.847,0.548,1.452l-1.555,0.212c-0.07-0.482-0.274-0.86-0.613-1.131

			c-0.338-0.271-0.817-0.405-1.435-0.405c-0.729,0-1.25,0.121-1.562,0.362c-0.312,0.242-0.467,0.524-0.467,0.847

			c0,0.207,0.064,0.392,0.194,0.557c0.13,0.171,0.332,0.312,0.609,0.424c0.159,0.058,0.626,0.194,1.404,0.405

			c1.124,0.3,1.907,0.546,2.353,0.737c0.443,0.191,0.792,0.469,1.046,0.834c0.252,0.364,0.379,0.817,0.379,1.359

			c0,0.53-0.154,1.029-0.463,1.497c-0.31,0.469-0.755,0.83-1.337,1.086c-0.583,0.256-1.242,0.384-1.977,0.384

			c-1.219,0-2.147-0.253-2.786-0.758C521.399,186.992,520.991,186.241,520.815,185.246z"/>

		<path fill="#FFFFFF" d="M536.906,185.026l1.642,0.204c-0.258,0.959-0.739,1.704-1.439,2.233s-1.595,0.794-2.683,0.794

			c-1.371,0-2.458-0.423-3.261-1.267c-0.802-0.844-1.205-2.028-1.205-3.553c0-1.577,0.406-2.801,1.217-3.672

			c0.812-0.871,1.865-1.306,3.16-1.306c1.253,0,2.278,0.426,3.072,1.28c0.793,0.853,1.191,2.054,1.191,3.602

			c0,0.094-0.003,0.236-0.009,0.423h-6.99c0.058,1.029,0.35,1.818,0.874,2.365c0.523,0.548,1.176,0.821,1.959,0.821

			c0.582,0,1.08-0.152,1.492-0.458C536.339,186.186,536.665,185.698,536.906,185.026z M531.691,182.459h5.234

			c-0.07-0.79-0.271-1.381-0.6-1.775c-0.506-0.612-1.162-0.918-1.968-0.918c-0.73,0-1.344,0.245-1.84,0.733

			C532.019,180.986,531.742,181.639,531.691,182.459z"/>

	</g>

	<g enable-background="new    ">

		<path fill="#FFFFFF" d="M451.613,209.739v-11.413h-4.263v-1.527h10.257v1.527h-4.281v11.413H451.613z"/>

		<path fill="#FFFFFF" d="M463.274,208.584c-0.588,0.5-1.155,0.853-1.7,1.059c-0.544,0.207-1.129,0.31-1.751,0.31

			c-1.03,0-1.822-0.251-2.375-0.755c-0.553-0.503-0.829-1.146-0.829-1.929c0-0.458,0.104-0.878,0.314-1.258

			c0.208-0.379,0.482-0.684,0.821-0.912c0.339-0.229,0.719-0.403,1.143-0.521c0.312-0.083,0.783-0.162,1.411-0.238

			c1.284-0.154,2.228-0.336,2.834-0.548c0.006-0.218,0.009-0.356,0.009-0.415c0-0.646-0.15-1.104-0.45-1.367

			c-0.405-0.359-1.009-0.539-1.81-0.539c-0.747,0-1.298,0.132-1.655,0.394s-0.619,0.725-0.789,1.391l-1.554-0.212

			c0.141-0.665,0.373-1.202,0.697-1.611c0.323-0.408,0.791-0.723,1.403-0.944c0.612-0.22,1.321-0.33,2.127-0.33

			c0.8,0,1.451,0.095,1.951,0.283c0.5,0.188,0.868,0.426,1.104,0.711c0.234,0.286,0.4,0.646,0.494,1.081

			c0.053,0.271,0.079,0.759,0.079,1.466v2.117c0,1.479,0.034,2.412,0.101,2.804c0.068,0.392,0.202,0.766,0.403,1.125h-1.66

			C463.427,209.411,463.321,209.025,463.274,208.584z M463.142,205.036c-0.577,0.235-1.443,0.435-2.597,0.6

			c-0.652,0.095-1.114,0.201-1.385,0.318c-0.271,0.117-0.48,0.288-0.627,0.516s-0.22,0.479-0.22,0.755

			c0,0.424,0.161,0.777,0.482,1.059c0.32,0.283,0.789,0.424,1.408,0.424c0.611,0,1.156-0.134,1.633-0.401

			c0.477-0.268,0.827-0.636,1.05-1.1c0.172-0.358,0.256-0.889,0.256-1.589V205.036L463.142,205.036z"/>

		<path fill="#FFFFFF" d="M467.194,209.739v-9.374h1.43v1.421c0.366-0.666,0.702-1.103,1.011-1.315

			c0.311-0.211,0.649-0.317,1.021-0.317c0.535,0,1.08,0.171,1.633,0.512l-0.548,1.475c-0.388-0.229-0.777-0.345-1.165-0.345

			c-0.347,0-0.659,0.105-0.936,0.313c-0.277,0.21-0.475,0.499-0.592,0.87c-0.176,0.564-0.264,1.182-0.264,1.854v4.907H467.194z"/>

		<path fill="#FFFFFF" d="M472.939,210.516l1.544,0.229c0.065,0.478,0.246,0.824,0.539,1.041c0.395,0.294,0.933,0.442,1.615,0.442

			c0.735,0,1.303-0.147,1.705-0.442c0.4-0.293,0.67-0.705,0.812-1.235c0.083-0.324,0.12-1.004,0.115-2.039

			c-0.695,0.819-1.56,1.227-2.596,1.227c-1.289,0-2.286-0.465-2.992-1.394c-0.707-0.931-1.06-2.045-1.06-3.346

			c0-0.896,0.162-1.72,0.486-2.477c0.323-0.756,0.792-1.34,1.407-1.752c0.615-0.411,1.336-0.617,2.167-0.617

			c1.106,0,2.019,0.446,2.736,1.341v-1.13h1.467v8.104c0,1.46-0.149,2.494-0.446,3.103c-0.298,0.608-0.768,1.091-1.413,1.443

			c-0.644,0.353-1.438,0.529-2.378,0.529c-1.118,0-2.022-0.251-2.711-0.755C473.247,212.286,472.916,211.529,472.939,210.516z

			 M474.254,204.885c0,1.229,0.245,2.128,0.732,2.692c0.488,0.565,1.1,0.848,1.836,0.848c0.73,0,1.342-0.281,1.836-0.844

			c0.495-0.561,0.742-1.443,0.742-2.644c0-1.146-0.255-2.013-0.764-2.596c-0.509-0.582-1.122-0.873-1.84-0.873

			c-0.706,0-1.306,0.288-1.799,0.861C474.502,202.903,474.254,203.756,474.254,204.885z"/>

		<path fill="#FFFFFF" d="M489.701,206.721l1.642,0.203c-0.258,0.959-0.739,1.704-1.439,2.232c-0.7,0.53-1.595,0.795-2.683,0.795

			c-1.371,0-2.458-0.422-3.262-1.267c-0.802-0.844-1.205-2.028-1.205-3.553c0-1.577,0.406-2.802,1.217-3.673

			c0.812-0.87,1.865-1.306,3.16-1.306c1.253,0,2.278,0.426,3.072,1.279c0.793,0.854,1.191,2.055,1.191,3.602

			c0,0.094-0.002,0.235-0.009,0.423h-6.99c0.058,1.03,0.35,1.819,0.874,2.366c0.523,0.548,1.176,0.821,1.959,0.821

			c0.583,0,1.08-0.152,1.492-0.458C489.134,207.882,489.46,207.391,489.701,206.721z M484.486,204.152h5.234

			c-0.07-0.788-0.271-1.381-0.599-1.774c-0.507-0.612-1.163-0.918-1.969-0.918c-0.73,0-1.343,0.244-1.84,0.732

			C484.814,202.68,484.538,203.335,484.486,204.152z"/>

		<path fill="#FFFFFF" d="M496.807,208.318l0.23,1.404c-0.447,0.095-0.847,0.141-1.2,0.141c-0.578,0-1.024-0.091-1.342-0.273

			c-0.318-0.182-0.542-0.422-0.671-0.719c-0.13-0.298-0.194-0.923-0.194-1.876v-5.393h-1.166v-1.236h1.166v-2.322l1.58-0.952v3.274

			h1.597v1.236h-1.597v5.481c0,0.453,0.027,0.745,0.084,0.873c0.056,0.13,0.147,0.233,0.273,0.31

			c0.126,0.076,0.308,0.115,0.543,0.115C496.287,208.382,496.519,208.359,496.807,208.318z"/>

		<path fill="#FFFFFF" d="M503.623,209.739v-12.94h9.356v1.527h-7.644v3.964h7.159v1.518h-7.159v4.406h7.944v1.525H503.623

			L503.623,209.739z"/>

		<path fill="#FFFFFF" d="M515.441,209.739v-9.374h1.43v1.332c0.688-1.029,1.683-1.544,2.984-1.544c0.564,0,1.084,0.101,1.558,0.304

			c0.474,0.203,0.829,0.47,1.064,0.799c0.236,0.329,0.4,0.721,0.495,1.174c0.058,0.294,0.088,0.81,0.088,1.545v5.764h-1.589v-5.702

			c0-0.647-0.061-1.131-0.185-1.452c-0.124-0.32-0.343-0.576-0.658-0.768c-0.315-0.19-0.684-0.287-1.108-0.287

			c-0.676,0-1.261,0.216-1.752,0.646c-0.492,0.429-0.738,1.244-0.738,2.444v5.119L515.441,209.739L515.441,209.739z"/>

		<path fill="#FFFFFF" d="M528.963,208.318l0.23,1.404c-0.447,0.095-0.848,0.141-1.201,0.141c-0.577,0-1.023-0.091-1.342-0.273

			c-0.317-0.182-0.542-0.422-0.671-0.719c-0.13-0.298-0.194-0.923-0.194-1.876v-5.393h-1.166v-1.236h1.166v-2.322l1.58-0.952v3.274

			h1.597v1.236h-1.597v5.481c0,0.453,0.028,0.745,0.084,0.873c0.056,0.13,0.147,0.233,0.273,0.31s0.308,0.115,0.543,0.115

			C528.444,208.382,528.677,208.359,528.963,208.318z"/>

		<path fill="#FFFFFF" d="M530.5,209.739v-9.374h1.429v1.421c0.367-0.666,0.703-1.103,1.012-1.315

			c0.31-0.211,0.649-0.317,1.02-0.317c0.535,0,1.08,0.171,1.634,0.512l-0.549,1.475c-0.388-0.229-0.777-0.345-1.165-0.345

			c-0.348,0-0.659,0.105-0.936,0.313c-0.276,0.21-0.475,0.499-0.592,0.87c-0.176,0.564-0.264,1.182-0.264,1.854v4.907H530.5z"/>

		<path fill="#FFFFFF" d="M536.467,213.351l-0.175-1.493c0.346,0.096,0.649,0.142,0.908,0.142c0.353,0,0.636-0.059,0.847-0.177

			c0.212-0.117,0.385-0.282,0.521-0.493c0.099-0.159,0.262-0.554,0.484-1.183c0.03-0.089,0.077-0.218,0.142-0.388l-3.557-9.393

			h1.712l1.951,5.429c0.251,0.688,0.479,1.411,0.678,2.171c0.183-0.729,0.401-1.441,0.654-2.136l2.003-5.464h1.59l-3.566,9.532

			c-0.383,1.029-0.68,1.74-0.892,2.128c-0.282,0.523-0.605,0.907-0.971,1.152c-0.365,0.244-0.799,0.365-1.306,0.365

			C537.185,213.545,536.844,213.479,536.467,213.351z"/>

	</g>

	<g enable-background="new    ">

		<path fill="#FFFFFF" d="M563.525,291.795v-12.939h4.855c0.987,0,1.781,0.132,2.379,0.393c0.596,0.262,1.063,0.666,1.402,1.21

			c0.34,0.544,0.508,1.113,0.508,1.708c0,0.555-0.149,1.074-0.448,1.563c-0.301,0.487-0.754,0.883-1.361,1.182

			c0.784,0.229,1.385,0.622,1.806,1.176c0.421,0.553,0.632,1.205,0.632,1.959c0,0.606-0.129,1.169-0.385,1.69

			c-0.255,0.52-0.572,0.922-0.948,1.204c-0.376,0.283-0.849,0.496-1.417,0.64c-0.567,0.144-1.263,0.216-2.087,0.216H563.525

			L563.525,291.795z M565.237,284.292h2.798c0.76,0,1.304-0.05,1.633-0.148c0.437-0.131,0.764-0.345,0.984-0.645

			c0.221-0.301,0.331-0.677,0.331-1.131c0-0.43-0.104-0.808-0.309-1.134c-0.207-0.328-0.5-0.551-0.883-0.672

			s-1.039-0.181-1.969-0.181h-2.587L565.237,284.292L565.237,284.292z M565.237,290.269h3.222c0.553,0,0.942-0.021,1.165-0.063

			c0.396-0.07,0.724-0.188,0.988-0.353c0.266-0.165,0.481-0.405,0.653-0.72c0.171-0.314,0.256-0.677,0.256-1.09

			c0-0.483-0.124-0.902-0.37-1.258c-0.249-0.356-0.591-0.606-1.028-0.75c-0.438-0.145-1.07-0.217-1.894-0.217h-2.992V290.269

			L565.237,290.269z"/>

		<path fill="#FFFFFF" d="M575.415,291.795v-12.939h1.589v12.939H575.415z"/>

		<path fill="#FFFFFF" d="M578.876,287.108c0-1.736,0.481-3.021,1.446-3.857c0.807-0.695,1.79-1.042,2.948-1.042

			c1.289,0,2.342,0.423,3.16,1.267c0.818,0.845,1.227,2.012,1.227,3.5c0,1.206-0.18,2.155-0.542,2.847

			c-0.363,0.691-0.889,1.229-1.58,1.611s-1.446,0.573-2.265,0.573c-1.312,0-2.372-0.42-3.182-1.262

			C579.28,289.903,578.876,288.691,578.876,287.108z M580.51,287.108c0,1.2,0.261,2.1,0.784,2.697

			c0.523,0.596,1.184,0.896,1.979,0.896c0.789,0,1.444-0.3,1.969-0.901c0.523-0.599,0.785-1.515,0.785-2.744

			c0-1.16-0.262-2.038-0.789-2.635c-0.526-0.598-1.182-0.897-1.964-0.897c-0.794,0-1.454,0.297-1.979,0.893

			C580.771,285.01,580.51,285.907,580.51,287.108z"/>

		<path fill="#FFFFFF" d="M595.637,288.361l1.563,0.203c-0.172,1.076-0.607,1.92-1.312,2.528c-0.704,0.609-1.566,0.914-2.591,0.914

			c-1.283,0-2.314-0.419-3.094-1.258c-0.781-0.838-1.171-2.04-1.171-3.605c0-1.012,0.168-1.898,0.503-2.657

			c0.336-0.758,0.846-1.328,1.532-1.707c0.686-0.38,1.431-0.57,2.237-0.57c1.02,0,1.851,0.258,2.498,0.772

			c0.647,0.516,1.063,1.247,1.245,2.193l-1.546,0.239c-0.146-0.63-0.406-1.103-0.781-1.422c-0.373-0.318-0.824-0.477-1.354-0.477

			c-0.801,0-1.45,0.287-1.951,0.861c-0.5,0.574-0.75,1.482-0.75,2.722c0,1.26,0.241,2.176,0.723,2.746

			c0.483,0.57,1.113,0.856,1.89,0.856c0.624,0,1.144-0.191,1.563-0.573C595.26,289.746,595.526,289.156,595.637,288.361z"/>

		<path fill="#FFFFFF" d="M598.568,291.795v-12.939h1.589v7.379l3.761-3.813h2.057l-3.585,3.478l3.947,5.896h-1.96l-3.099-4.793

			l-1.121,1.077v3.715L598.568,291.795L598.568,291.795z"/>

		<path fill="#FFFFFF" d="M614.351,291.795l-2.868-9.374h1.641l1.492,5.41l0.556,2.013c0.023-0.099,0.187-0.744,0.485-1.934

			l1.492-5.489h1.634l1.403,5.438l0.468,1.792l0.539-1.81l1.605-5.42h1.545l-2.931,9.374h-1.651l-1.49-5.614l-0.363-1.597

			l-1.897,7.211H614.351L614.351,291.795z"/>

		<path fill="#FFFFFF" d="M625.683,280.682v-1.827h1.59v1.827H625.683z M625.683,291.795v-9.374h1.59v9.374H625.683z"/>

		<path fill="#FFFFFF" d="M629.056,288.998l1.57-0.249c0.089,0.631,0.333,1.113,0.737,1.449c0.404,0.334,0.967,0.502,1.69,0.502

			c0.73,0,1.271-0.147,1.625-0.445c0.353-0.296,0.529-0.646,0.529-1.046c0-0.359-0.157-0.642-0.469-0.848

			c-0.217-0.141-0.759-0.32-1.624-0.539c-1.166-0.294-1.974-0.548-2.423-0.763c-0.449-0.214-0.791-0.512-1.023-0.892

			s-0.349-0.799-0.349-1.258c0-0.417,0.096-0.804,0.287-1.161c0.19-0.355,0.451-0.651,0.781-0.887

			c0.248-0.182,0.583-0.336,1.011-0.463c0.427-0.127,0.885-0.19,1.372-0.19c0.735,0,1.382,0.106,1.938,0.319

			c0.556,0.212,0.967,0.498,1.231,0.859c0.264,0.362,0.446,0.847,0.547,1.452l-1.555,0.211c-0.07-0.48-0.274-0.858-0.612-1.129

			c-0.338-0.271-0.817-0.406-1.436-0.406c-0.729,0-1.249,0.121-1.561,0.362c-0.313,0.242-0.469,0.523-0.469,0.848

			c0,0.206,0.064,0.392,0.195,0.556c0.129,0.173,0.331,0.313,0.608,0.424c0.158,0.06,0.627,0.194,1.403,0.406

			c1.124,0.299,1.907,0.546,2.354,0.737c0.442,0.191,0.791,0.47,1.046,0.834c0.252,0.364,0.379,0.817,0.379,1.359

			c0,0.529-0.154,1.028-0.463,1.496c-0.31,0.469-0.756,0.829-1.338,1.086c-0.583,0.256-1.241,0.384-1.978,0.384

			c-1.218,0-2.146-0.253-2.785-0.759C629.639,290.743,629.232,289.991,629.056,288.998z"/>

		<path fill="#FFFFFF" d="M645.147,288.777l1.642,0.203c-0.258,0.959-0.739,1.704-1.439,2.232c-0.7,0.53-1.595,0.795-2.683,0.795

			c-1.371,0-2.458-0.422-3.261-1.267c-0.803-0.844-1.206-2.028-1.206-3.553c0-1.577,0.406-2.802,1.218-3.673

			c0.813-0.87,1.865-1.307,3.16-1.307c1.254,0,2.278,0.427,3.072,1.281c0.793,0.853,1.191,2.054,1.191,3.601

			c0,0.094-0.003,0.235-0.009,0.423h-6.991c0.059,1.03,0.351,1.818,0.874,2.365c0.523,0.549,1.176,0.822,1.96,0.822

			c0.582,0,1.079-0.152,1.491-0.458C644.58,289.937,644.907,289.447,645.147,288.777z M639.93,286.208h5.234

			c-0.07-0.788-0.271-1.381-0.6-1.775c-0.507-0.611-1.163-0.917-1.969-0.917c-0.729,0-1.343,0.245-1.84,0.732

			C640.259,284.736,639.984,285.39,639.93,286.208z"/>

	</g>

	<g enable-background="new    ">

		<path fill="#FFFFFF" d="M550.102,313.488v-12.94h8.73v1.526h-7.017v4.008h6.072v1.526h-6.072v5.879L550.102,313.488

			L550.102,313.488z"/>

		<path fill="#FFFFFF" d="M566.998,313.488v-1.377c-0.729,1.059-1.721,1.589-2.975,1.589c-0.553,0-1.069-0.106-1.549-0.317

			c-0.48-0.212-0.836-0.479-1.068-0.8c-0.233-0.32-0.396-0.712-0.49-1.177c-0.065-0.313-0.097-0.807-0.097-1.483v-5.809h1.589v5.199

			c0,0.83,0.032,1.39,0.096,1.678c0.1,0.418,0.312,0.745,0.636,0.983c0.323,0.239,0.724,0.358,1.2,0.358

			c0.477,0,0.924-0.124,1.342-0.366c0.418-0.245,0.712-0.577,0.888-0.998c0.173-0.421,0.26-1.03,0.26-1.83v-5.024h1.589v9.376

			h-1.422V313.488z"/>

		<path fill="#FFFFFF" d="M570.908,313.488v-9.376h1.431v1.333c0.688-1.029,1.682-1.544,2.983-1.544

			c0.564,0,1.084,0.101,1.559,0.305c0.474,0.202,0.827,0.469,1.063,0.797c0.235,0.331,0.401,0.722,0.495,1.176

			c0.059,0.293,0.088,0.81,0.088,1.545v5.764h-1.589v-5.702c0-0.647-0.061-1.132-0.186-1.452c-0.123-0.32-0.343-0.577-0.657-0.769

			c-0.315-0.19-0.684-0.288-1.107-0.288c-0.677,0-1.261,0.216-1.753,0.646c-0.491,0.43-0.737,1.245-0.737,2.446v5.119

			L570.908,313.488L570.908,313.488z"/>

		<path fill="#FFFFFF" d="M587.045,313.488v-1.184c-0.596,0.931-1.469,1.396-2.622,1.396c-0.749,0-1.436-0.207-2.061-0.618

			c-0.628-0.411-1.113-0.987-1.457-1.726c-0.345-0.738-0.516-1.587-0.516-2.547c0-0.936,0.155-1.784,0.467-2.546

			c0.313-0.762,0.78-1.347,1.404-1.753c0.624-0.405,1.321-0.608,2.092-0.608c0.565,0,1.068,0.119,1.51,0.357

			s0.801,0.549,1.077,0.932v-4.643h1.58v12.94L587.045,313.488L587.045,313.488z M582.02,308.808c0,1.2,0.253,2.1,0.76,2.692

			c0.506,0.595,1.104,0.892,1.791,0.892c0.694,0,1.285-0.283,1.77-0.852c0.485-0.568,0.729-1.435,0.729-2.6

			c0-1.284-0.247-2.225-0.742-2.825c-0.493-0.6-1.103-0.9-1.826-0.9c-0.708,0-1.297,0.288-1.77,0.865

			C582.258,306.658,582.02,307.568,582.02,308.808z"/>

		<path fill="#FFFFFF" d="M596.267,313.488v-12.94h5.738c1.153,0,2.029,0.116,2.63,0.349c0.6,0.232,1.081,0.643,1.439,1.231

			s0.538,1.238,0.538,1.949c0,0.918-0.297,1.692-0.892,2.323c-0.595,0.63-1.514,1.028-2.753,1.2

			c0.453,0.217,0.797,0.433,1.032,0.644c0.5,0.459,0.974,1.032,1.421,1.721l2.251,3.523h-2.154l-1.712-2.692

			c-0.5-0.776-0.912-1.371-1.235-1.784c-0.325-0.41-0.613-0.7-0.87-0.865c-0.255-0.164-0.516-0.278-0.781-0.344

			c-0.194-0.041-0.512-0.063-0.953-0.063h-1.985v5.748H596.267L596.267,313.488z M597.98,306.259h3.681

			c0.783,0,1.396-0.08,1.836-0.243c0.442-0.162,0.776-0.42,1.007-0.777c0.229-0.355,0.344-0.743,0.344-1.161

			c0-0.611-0.223-1.114-0.666-1.509c-0.445-0.395-1.146-0.592-2.105-0.592h-4.097L597.98,306.259L597.98,306.259z"/>

		<path fill="#FFFFFF" d="M615.51,310.469l1.643,0.203c-0.26,0.959-0.74,1.704-1.44,2.232c-0.7,0.529-1.595,0.795-2.683,0.795

			c-1.371,0-2.458-0.422-3.261-1.267c-0.804-0.844-1.205-2.029-1.205-3.554c0-1.576,0.405-2.801,1.217-3.672

			c0.812-0.87,1.866-1.306,3.16-1.306c1.253,0,2.278,0.426,3.072,1.28c0.794,0.853,1.19,2.054,1.19,3.601

			c0,0.095-0.002,0.236-0.008,0.423h-6.991c0.059,1.029,0.351,1.819,0.874,2.366c0.523,0.548,1.176,0.821,1.96,0.821

			c0.582,0,1.079-0.152,1.491-0.458C614.942,311.628,615.269,311.139,615.51,310.469z M610.294,307.901h5.234

			c-0.07-0.79-0.271-1.381-0.6-1.776c-0.507-0.611-1.162-0.917-1.969-0.917c-0.729,0-1.344,0.245-1.841,0.733

			C610.622,306.428,610.346,307.082,610.294,307.901z"/>

		<path fill="#FFFFFF" d="M619.112,313.488v-12.94h1.589v12.94H619.112z"/>

		<path fill="#FFFFFF" d="M629.581,310.469l1.642,0.203c-0.259,0.959-0.739,1.704-1.439,2.232c-0.7,0.529-1.595,0.795-2.683,0.795

			c-1.372,0-2.458-0.422-3.262-1.267c-0.803-0.844-1.204-2.029-1.204-3.554c0-1.576,0.405-2.801,1.217-3.672

			c0.813-0.87,1.865-1.306,3.161-1.306c1.252,0,2.277,0.426,3.07,1.28c0.794,0.853,1.191,2.054,1.191,3.601

			c0,0.095-0.003,0.236-0.008,0.423h-6.99c0.058,1.029,0.35,1.819,0.872,2.366c0.524,0.548,1.177,0.821,1.96,0.821

			c0.582,0,1.08-0.152,1.492-0.458C629.013,311.628,629.341,311.139,629.581,310.469z M624.365,307.901h5.234

			c-0.07-0.79-0.271-1.381-0.601-1.776c-0.506-0.611-1.162-0.917-1.968-0.917c-0.73,0-1.344,0.245-1.841,0.733

			C624.694,306.428,624.417,307.082,624.365,307.901z"/>

		<path fill="#FFFFFF" d="M639.335,312.332c-0.589,0.5-1.155,0.853-1.699,1.059c-0.544,0.207-1.13,0.31-1.753,0.31

			c-1.029,0-1.82-0.251-2.374-0.755c-0.553-0.503-0.828-1.145-0.828-1.929c0-0.458,0.104-0.877,0.313-1.258

			c0.21-0.379,0.483-0.684,0.821-0.913c0.34-0.229,0.72-0.402,1.143-0.52c0.313-0.083,0.784-0.162,1.413-0.239

			c1.283-0.153,2.227-0.335,2.832-0.548c0.008-0.217,0.01-0.355,0.01-0.414c0-0.646-0.149-1.103-0.45-1.367

			c-0.405-0.359-1.009-0.539-1.81-0.539c-0.747,0-1.299,0.131-1.654,0.393c-0.356,0.263-0.619,0.726-0.79,1.391l-1.554-0.211

			c0.142-0.666,0.373-1.203,0.697-1.61c0.324-0.409,0.792-0.725,1.404-0.945c0.612-0.22,1.321-0.329,2.127-0.329

			c0.801,0,1.451,0.094,1.951,0.282c0.5,0.188,0.867,0.425,1.103,0.711c0.235,0.285,0.401,0.646,0.494,1.081

			c0.053,0.271,0.079,0.759,0.079,1.466v2.118c0,1.478,0.034,2.41,0.102,2.803c0.067,0.392,0.202,0.766,0.402,1.125h-1.66

			C639.487,313.158,639.382,312.772,639.335,312.332z M639.201,308.783c-0.576,0.236-1.441,0.436-2.595,0.6

			c-0.652,0.094-1.115,0.2-1.386,0.317c-0.271,0.118-0.48,0.29-0.627,0.518c-0.147,0.228-0.221,0.479-0.221,0.754

			c0,0.424,0.16,0.776,0.481,1.059c0.321,0.283,0.79,0.425,1.408,0.425c0.611,0,1.155-0.136,1.632-0.402

			c0.479-0.269,0.829-0.635,1.052-1.099c0.171-0.36,0.255-0.889,0.255-1.589V308.783z"/>

		<path fill="#FFFFFF" d="M642.635,310.689l1.57-0.247c0.09,0.63,0.334,1.112,0.737,1.447c0.404,0.335,0.968,0.503,1.69,0.503

			c0.73,0,1.271-0.148,1.625-0.445s0.529-0.645,0.529-1.047c0-0.358-0.156-0.641-0.469-0.847c-0.217-0.14-0.759-0.32-1.623-0.539

			c-1.166-0.293-1.974-0.548-2.423-0.763c-0.45-0.215-0.792-0.513-1.024-0.893c-0.232-0.378-0.349-0.798-0.349-1.256

			c0-0.418,0.097-0.805,0.288-1.161c0.19-0.356,0.45-0.652,0.78-0.888c0.248-0.182,0.584-0.337,1.011-0.463

			c0.428-0.127,0.885-0.189,1.373-0.189c0.735,0,1.382,0.104,1.938,0.317c0.557,0.213,0.967,0.498,1.231,0.86

			c0.265,0.363,0.447,0.847,0.547,1.452l-1.554,0.212c-0.07-0.481-0.274-0.86-0.612-1.131c-0.339-0.271-0.817-0.405-1.436-0.405

			c-0.729,0-1.251,0.121-1.562,0.362c-0.313,0.241-0.468,0.524-0.468,0.848c0,0.206,0.064,0.392,0.194,0.557

			c0.13,0.171,0.332,0.312,0.608,0.424c0.159,0.058,0.627,0.194,1.403,0.405c1.125,0.3,1.908,0.546,2.354,0.737

			c0.443,0.191,0.792,0.469,1.047,0.834c0.251,0.364,0.379,0.817,0.379,1.359c0,0.529-0.154,1.028-0.463,1.496

			c-0.31,0.468-0.756,0.83-1.338,1.085c-0.583,0.257-1.242,0.385-1.979,0.385c-1.217,0-2.146-0.253-2.784-0.759

			C643.219,312.434,642.813,311.684,642.635,310.689z"/>

		<path fill="#FFFFFF" d="M658.726,310.469l1.643,0.203c-0.259,0.959-0.739,1.704-1.439,2.232c-0.701,0.529-1.596,0.795-2.683,0.795

			c-1.372,0-2.458-0.422-3.262-1.267c-0.803-0.844-1.205-2.029-1.205-3.554c0-1.576,0.406-2.801,1.218-3.672

			c0.813-0.87,1.865-1.306,3.16-1.306c1.253,0,2.278,0.426,3.071,1.28c0.794,0.853,1.191,2.054,1.191,3.601

			c0,0.095-0.003,0.236-0.009,0.423h-6.99c0.058,1.029,0.351,1.819,0.873,2.366c0.524,0.548,1.177,0.821,1.96,0.821

			c0.582,0,1.079-0.152,1.492-0.458C658.16,311.628,658.486,311.139,658.726,310.469z M653.511,307.901h5.234

			c-0.07-0.79-0.271-1.381-0.601-1.776c-0.506-0.611-1.162-0.917-1.968-0.917c-0.73,0-1.344,0.245-1.841,0.733

			C653.839,306.428,653.563,307.082,653.511,307.901z"/>

	</g>

	<g enable-background="new    ">

		<path fill="#FFFFFF" d="M697.189,190.253v-12.94h1.714v11.414h6.372v1.526H697.189z"/>

		<path fill="#FFFFFF" d="M706.521,185.566c0-1.735,0.481-3.021,1.446-3.857c0.807-0.695,1.789-1.042,2.948-1.042

			c1.289,0,2.342,0.422,3.16,1.266c0.817,0.845,1.227,2.012,1.227,3.5c0,1.207-0.181,2.157-0.542,2.847

			c-0.363,0.691-0.89,1.229-1.58,1.61c-0.691,0.383-1.447,0.574-2.265,0.574c-1.313,0-2.372-0.42-3.182-1.262

			C706.926,188.362,706.521,187.148,706.521,185.566z M708.154,185.566c0,1.202,0.262,2.1,0.786,2.696

			c0.522,0.598,1.183,0.897,1.978,0.897c0.788,0,1.443-0.3,1.968-0.901c0.524-0.6,0.786-1.516,0.786-2.744

			c0-1.16-0.263-2.038-0.789-2.635c-0.526-0.598-1.182-0.897-1.964-0.897c-0.794,0-1.454,0.297-1.978,0.892

			C708.417,183.469,708.154,184.365,708.154,185.566z"/>

		<path fill="#FFFFFF" d="M723.284,189.097c-0.589,0.5-1.155,0.852-1.699,1.059c-0.545,0.207-1.129,0.311-1.753,0.311

			c-1.028,0-1.821-0.252-2.373-0.756c-0.555-0.503-0.829-1.146-0.829-1.929c0-0.458,0.104-0.878,0.313-1.258

			c0.21-0.379,0.483-0.684,0.822-0.913c0.339-0.229,0.719-0.403,1.142-0.521c0.313-0.082,0.784-0.162,1.412-0.238

			c1.283-0.153,2.228-0.336,2.834-0.548c0.006-0.217,0.009-0.356,0.009-0.414c0-0.647-0.149-1.104-0.45-1.368

			c-0.405-0.359-1.009-0.539-1.81-0.539c-0.747,0-1.299,0.132-1.655,0.394s-0.619,0.725-0.788,1.391l-1.555-0.212

			c0.142-0.665,0.373-1.202,0.697-1.611c0.324-0.408,0.791-0.723,1.404-0.944c0.611-0.22,1.321-0.331,2.127-0.331

			c0.801,0,1.451,0.095,1.951,0.284c0.5,0.187,0.867,0.425,1.104,0.71c0.234,0.285,0.399,0.646,0.492,1.081

			c0.055,0.271,0.08,0.759,0.08,1.466v2.118c0,1.479,0.034,2.411,0.102,2.804c0.068,0.391,0.201,0.766,0.401,1.125h-1.659

			C723.436,189.925,723.331,189.539,723.284,189.097z M723.151,185.55c-0.576,0.235-1.443,0.435-2.597,0.6

			c-0.652,0.094-1.114,0.2-1.385,0.317c-0.271,0.118-0.48,0.289-0.627,0.517c-0.147,0.228-0.221,0.479-0.221,0.755

			c0,0.424,0.16,0.777,0.482,1.059c0.32,0.283,0.788,0.424,1.406,0.424c0.612,0,1.156-0.135,1.634-0.401

			c0.478-0.269,0.827-0.636,1.051-1.099c0.171-0.359,0.256-0.89,0.256-1.59V185.55z"/>

		<path fill="#FFFFFF" d="M727.219,190.253v-9.374h1.43v1.332c0.689-1.029,1.684-1.544,2.985-1.544c0.564,0,1.083,0.101,1.558,0.304

			s0.828,0.469,1.063,0.798c0.235,0.33,0.399,0.72,0.494,1.174c0.059,0.294,0.089,0.81,0.089,1.545v5.764h-1.589v-5.702

			c0-0.647-0.062-1.131-0.186-1.452c-0.123-0.32-0.343-0.577-0.657-0.768c-0.315-0.19-0.685-0.287-1.108-0.287

			c-0.676,0-1.261,0.215-1.752,0.646c-0.492,0.43-0.737,1.245-0.737,2.445v5.119H727.219L727.219,190.253z"/>

	</g>

	<g enable-background="new    ">

		<path fill="#FFFFFF" d="M681.612,207.791l1.616-0.141c0.075,0.647,0.253,1.177,0.534,1.593c0.278,0.415,0.712,0.751,1.3,1.006

			c0.59,0.257,1.252,0.385,1.986,0.385c0.653,0,1.23-0.097,1.73-0.29c0.5-0.194,0.872-0.461,1.117-0.799

			c0.243-0.339,0.365-0.708,0.365-1.108c0-0.405-0.117-0.76-0.353-1.063c-0.235-0.302-0.624-0.558-1.165-0.763

			c-0.348-0.136-1.115-0.346-2.304-0.632c-1.189-0.285-2.021-0.556-2.498-0.807c-0.618-0.324-1.079-0.726-1.382-1.206

			c-0.304-0.479-0.453-1.017-0.453-1.611c0-0.653,0.184-1.262,0.556-1.831c0.371-0.567,0.912-0.999,1.625-1.293

			c0.712-0.293,1.503-0.441,2.374-0.441c0.958,0,1.805,0.154,2.537,0.465c0.733,0.308,1.296,0.763,1.69,1.363

			s0.604,1.279,0.636,2.038l-1.642,0.124c-0.089-0.817-0.388-1.436-0.896-1.854s-1.261-0.627-2.254-0.627

			c-1.036,0-1.791,0.189-2.265,0.569s-0.711,0.837-0.711,1.372c0,0.466,0.169,0.848,0.503,1.147c0.329,0.3,1.19,0.607,2.583,0.922

			c1.392,0.315,2.345,0.591,2.864,0.827c0.752,0.347,1.31,0.786,1.668,1.319c0.36,0.532,0.539,1.146,0.539,1.841

			c0,0.688-0.197,1.335-0.592,1.944s-0.96,1.083-1.699,1.422c-0.738,0.338-1.569,0.508-2.494,0.508

			c-1.171,0-2.152-0.172-2.943-0.513c-0.792-0.341-1.412-0.854-1.863-1.54C681.873,209.43,681.635,208.656,681.612,207.791z"/>

		<path fill="#FFFFFF" d="M700.167,210.793c-0.589,0.499-1.155,0.853-1.7,1.059c-0.544,0.206-1.129,0.31-1.752,0.31

			c-1.029,0-1.821-0.251-2.374-0.756c-0.553-0.502-0.828-1.144-0.828-1.929c0-0.458,0.104-0.877,0.313-1.257

			c0.209-0.379,0.483-0.684,0.821-0.913c0.34-0.229,0.72-0.402,1.143-0.521c0.313-0.082,0.783-0.162,1.412-0.238

			c1.284-0.153,2.228-0.335,2.833-0.548c0.007-0.218,0.01-0.355,0.01-0.414c0-0.647-0.149-1.104-0.45-1.367

			c-0.406-0.36-1.009-0.539-1.81-0.539c-0.748,0-1.299,0.131-1.654,0.393c-0.356,0.263-0.619,0.726-0.79,1.391l-1.554-0.211

			c0.142-0.666,0.373-1.203,0.697-1.611c0.323-0.408,0.791-0.724,1.404-0.944c0.611-0.22,1.321-0.33,2.127-0.33

			c0.801,0,1.45,0.095,1.95,0.283c0.5,0.188,0.868,0.425,1.104,0.71c0.234,0.286,0.4,0.646,0.494,1.082

			c0.053,0.271,0.079,0.758,0.079,1.466v2.118c0,1.477,0.034,2.41,0.102,2.802c0.067,0.393,0.201,0.766,0.402,1.126h-1.66

			C700.319,211.619,700.214,211.234,700.167,210.793z M700.034,207.243c-0.576,0.235-1.442,0.437-2.596,0.601

			c-0.652,0.094-1.114,0.2-1.385,0.317s-0.481,0.29-0.628,0.518s-0.22,0.478-0.22,0.753c0,0.425,0.16,0.777,0.481,1.06

			s0.789,0.424,1.407,0.424c0.612,0,1.156-0.135,1.633-0.402c0.479-0.268,0.828-0.634,1.052-1.098

			c0.171-0.36,0.255-0.89,0.255-1.591V207.243z"/>

		<path fill="#FFFFFF" d="M704.103,211.949v-9.376h1.431v1.333c0.688-1.029,1.683-1.544,2.984-1.544c0.563,0,1.084,0.1,1.558,0.303

			c0.474,0.204,0.827,0.471,1.063,0.799c0.235,0.33,0.4,0.722,0.494,1.175c0.059,0.294,0.089,0.81,0.089,1.545v5.764h-1.59v-5.703

			c0-0.646-0.061-1.131-0.185-1.451c-0.124-0.321-0.343-0.576-0.657-0.768c-0.316-0.19-0.685-0.288-1.108-0.288

			c-0.677,0-1.261,0.216-1.753,0.646c-0.49,0.43-0.737,1.244-0.737,2.445v5.119h-1.589V211.949z"/>

		<path fill="#FFFFFF" d="M720.275,208.513l1.562,0.204c-0.171,1.077-0.606,1.919-1.311,2.528c-0.704,0.608-1.566,0.914-2.591,0.914

			c-1.283,0-2.315-0.42-3.094-1.258c-0.781-0.838-1.172-2.041-1.172-3.605c0-1.013,0.169-1.898,0.504-2.657

			c0.336-0.759,0.846-1.329,1.531-1.708c0.687-0.379,1.432-0.568,2.238-0.568c1.019,0,1.85,0.256,2.498,0.771

			c0.647,0.516,1.063,1.246,1.244,2.193l-1.545,0.238c-0.146-0.63-0.407-1.103-0.781-1.421c-0.374-0.317-0.826-0.477-1.354-0.477

			c-0.801,0-1.45,0.287-1.951,0.861c-0.5,0.573-0.75,1.481-0.75,2.723c0,1.259,0.241,2.175,0.723,2.745

			c0.483,0.571,1.113,0.856,1.889,0.856c0.625,0,1.145-0.191,1.563-0.574C719.898,209.897,720.163,209.309,720.275,208.513z"/>

		<path fill="#FFFFFF" d="M726.665,210.527l0.23,1.403c-0.447,0.096-0.848,0.142-1.2,0.142c-0.577,0-1.024-0.092-1.342-0.273

			c-0.318-0.182-0.542-0.422-0.673-0.719c-0.129-0.298-0.193-0.924-0.193-1.877v-5.393h-1.165v-1.236h1.165v-2.321l1.58-0.953v3.274

			h1.598v1.236h-1.598v5.482c0,0.452,0.027,0.744,0.085,0.873c0.056,0.131,0.146,0.232,0.273,0.31

			c0.126,0.075,0.308,0.114,0.543,0.114C726.143,210.589,726.377,210.569,726.665,210.527z"/>

		<path fill="#FFFFFF" d="M728.226,200.835v-1.827h1.59v1.827H728.226z M728.226,211.949v-9.376h1.59v9.376H728.226z"/>

		<path fill="#FFFFFF" d="M731.643,207.261c0-1.735,0.482-3.022,1.447-3.857c0.807-0.694,1.789-1.041,2.947-1.041

			c1.29,0,2.342,0.422,3.16,1.265c0.818,0.845,1.227,2.013,1.227,3.5c0,1.207-0.181,2.156-0.542,2.848

			c-0.362,0.69-0.889,1.228-1.58,1.61s-1.446,0.574-2.265,0.574c-1.313,0-2.371-0.421-3.181-1.263

			C732.048,210.056,731.643,208.844,731.643,207.261z M733.276,207.261c0,1.2,0.261,2.099,0.785,2.696

			c0.522,0.597,1.184,0.896,1.978,0.896c0.79,0,1.445-0.3,1.969-0.901c0.523-0.6,0.786-1.516,0.786-2.745

			c0-1.159-0.264-2.036-0.79-2.635c-0.525-0.598-1.181-0.896-1.964-0.896c-0.793,0-1.453,0.296-1.978,0.892

			C733.538,205.163,733.276,206.061,733.276,207.261z"/>

		<path fill="#FFFFFF" d="M742.288,211.949v-9.376h1.431v1.333c0.688-1.029,1.683-1.544,2.983-1.544c0.565,0,1.084,0.1,1.559,0.303

			c0.474,0.204,0.828,0.471,1.063,0.799c0.235,0.33,0.4,0.722,0.494,1.175c0.059,0.294,0.089,0.81,0.089,1.545v5.764h-1.59v-5.703

			c0-0.646-0.061-1.131-0.185-1.451c-0.124-0.321-0.342-0.576-0.658-0.768c-0.314-0.19-0.683-0.288-1.107-0.288

			c-0.677,0-1.261,0.216-1.752,0.646c-0.492,0.43-0.737,1.244-0.737,2.445v5.119h-1.59V211.949z"/>

	</g>

	<g enable-background="new    ">

		<path fill="#FFFFFF" d="M807.739,284.659v-12.939h1.714v11.413h6.372v1.526H807.739L807.739,284.659z"/>

		<path fill="#FFFFFF" d="M817.07,279.971c0-1.735,0.482-3.021,1.446-3.857c0.808-0.694,1.79-1.041,2.949-1.041

			c1.288,0,2.342,0.422,3.16,1.266c0.817,0.845,1.227,2.012,1.227,3.5c0,1.206-0.181,2.156-0.543,2.847

			c-0.362,0.691-0.889,1.229-1.58,1.61c-0.691,0.383-1.446,0.574-2.264,0.574c-1.313,0-2.373-0.421-3.183-1.262

			S817.07,281.554,817.07,279.971z M818.704,279.971c0,1.2,0.262,2.1,0.786,2.696c0.522,0.598,1.182,0.896,1.977,0.896

			c0.789,0,1.444-0.299,1.969-0.901c0.524-0.599,0.787-1.515,0.787-2.744c0-1.159-0.264-2.038-0.791-2.636

			c-0.526-0.597-1.181-0.896-1.964-0.896c-0.794,0-1.453,0.297-1.977,0.892C818.967,277.874,818.704,278.772,818.704,279.971z"/>

		<path fill="#FFFFFF" d="M833.833,283.504c-0.588,0.5-1.154,0.853-1.699,1.059c-0.544,0.207-1.129,0.31-1.751,0.31

			c-1.03,0-1.822-0.251-2.375-0.755c-0.553-0.503-0.829-1.146-0.829-1.929c0-0.458,0.104-0.878,0.314-1.258

			c0.208-0.379,0.481-0.684,0.821-0.913c0.339-0.229,0.718-0.402,1.142-0.52c0.313-0.083,0.784-0.162,1.412-0.238

			c1.283-0.154,2.227-0.336,2.833-0.549c0.007-0.217,0.01-0.355,0.01-0.414c0-0.646-0.15-1.104-0.45-1.367

			c-0.405-0.359-1.01-0.539-1.81-0.539c-0.747,0-1.299,0.132-1.655,0.393c-0.356,0.263-0.619,0.726-0.789,1.391l-1.555-0.211

			c0.142-0.665,0.374-1.202,0.698-1.611c0.323-0.408,0.791-0.724,1.403-0.943c0.612-0.221,1.321-0.331,2.127-0.331

			c0.801,0,1.451,0.094,1.951,0.283c0.5,0.188,0.867,0.425,1.104,0.711c0.235,0.285,0.4,0.646,0.493,1.081

			c0.054,0.271,0.079,0.759,0.079,1.466v2.117c0,1.478,0.034,2.412,0.103,2.804c0.067,0.392,0.201,0.766,0.401,1.125h-1.659

			C833.985,284.329,833.88,283.944,833.833,283.504z M833.7,279.955c-0.576,0.235-1.442,0.436-2.596,0.6

			c-0.652,0.095-1.114,0.201-1.386,0.318c-0.271,0.117-0.479,0.289-0.627,0.516c-0.147,0.228-0.22,0.479-0.22,0.755

			c0,0.424,0.159,0.777,0.481,1.059c0.32,0.282,0.789,0.424,1.407,0.424c0.611,0,1.156-0.134,1.633-0.401

			c0.478-0.268,0.828-0.635,1.051-1.099c0.173-0.359,0.256-0.89,0.256-1.59V279.955z"/>

		<path fill="#FFFFFF" d="M837.767,284.659v-9.375h1.431v1.333c0.688-1.03,1.684-1.544,2.984-1.544c0.565,0,1.084,0.101,1.558,0.304

			c0.475,0.203,0.828,0.47,1.064,0.799c0.235,0.329,0.399,0.72,0.494,1.174c0.058,0.294,0.089,0.81,0.089,1.545v5.764h-1.59v-5.702

			c0-0.646-0.062-1.132-0.185-1.452c-0.124-0.32-0.343-0.576-0.658-0.768c-0.314-0.19-0.684-0.287-1.108-0.287

			c-0.676,0-1.26,0.215-1.751,0.645c-0.492,0.431-0.737,1.245-0.737,2.445v5.119L837.767,284.659L837.767,284.659z"/>

	</g>

	<g enable-background="new    ">

		<path fill="#FFFFFF" d="M772.156,306.353v-12.94h4.458c1.006,0,1.773,0.062,2.304,0.186c0.741,0.171,1.373,0.48,1.897,0.927

			c0.683,0.576,1.193,1.313,1.531,2.212c0.339,0.897,0.508,1.923,0.508,3.076c0,0.981-0.115,1.854-0.344,2.611

			c-0.23,0.76-0.523,1.389-0.883,1.886s-0.751,0.888-1.178,1.175c-0.427,0.285-0.943,0.502-1.545,0.647

			c-0.603,0.147-1.296,0.221-2.079,0.221H772.156L772.156,306.353z M773.869,304.827h2.763c0.854,0,1.522-0.079,2.009-0.238

			c0.485-0.159,0.871-0.383,1.161-0.672c0.404-0.405,0.722-0.951,0.948-1.637c0.226-0.686,0.339-1.518,0.339-2.493

			c0-1.354-0.221-2.394-0.666-3.12c-0.444-0.727-0.984-1.214-1.619-1.462c-0.46-0.176-1.198-0.264-2.216-0.264h-2.719V304.827z"/>

		<path fill="#FFFFFF" d="M785.016,295.241v-1.828h1.59v1.828H785.016z M785.016,306.353v-9.375h1.59v9.375H785.016z"/>

		<path fill="#FFFFFF" d="M788.389,303.555l1.571-0.248c0.089,0.631,0.333,1.112,0.737,1.448c0.403,0.335,0.967,0.503,1.69,0.503

			c0.73,0,1.271-0.148,1.624-0.446c0.354-0.296,0.53-0.644,0.53-1.046c0-0.358-0.156-0.641-0.47-0.847

			c-0.217-0.141-0.758-0.32-1.623-0.539c-1.166-0.293-1.974-0.548-2.423-0.764c-0.45-0.214-0.791-0.512-1.023-0.892

			c-0.233-0.379-0.349-0.799-0.349-1.256c0-0.418,0.096-0.805,0.287-1.162c0.19-0.355,0.451-0.651,0.78-0.888

			c0.248-0.182,0.585-0.336,1.012-0.463c0.427-0.126,0.885-0.189,1.372-0.189c0.735,0,1.381,0.105,1.938,0.318

			c0.556,0.212,0.966,0.498,1.231,0.86c0.265,0.362,0.446,0.847,0.547,1.452l-1.555,0.211c-0.069-0.48-0.274-0.859-0.612-1.13

			s-0.817-0.406-1.436-0.406c-0.729,0-1.25,0.122-1.562,0.363s-0.467,0.523-0.467,0.847c0,0.207,0.063,0.393,0.193,0.557

			c0.129,0.172,0.332,0.312,0.608,0.424c0.16,0.059,0.627,0.194,1.404,0.406c1.124,0.3,1.907,0.546,2.353,0.737

			c0.443,0.191,0.793,0.469,1.046,0.834c0.253,0.364,0.379,0.817,0.379,1.358c0,0.53-0.154,1.029-0.463,1.497

			c-0.31,0.468-0.755,0.829-1.337,1.085c-0.583,0.257-1.242,0.385-1.978,0.385c-1.218,0-2.146-0.254-2.785-0.759

			C788.973,305.301,788.566,304.55,788.389,303.555z"/>

		<path fill="#FFFFFF" d="M799.529,306.353h-1.474v-12.94h1.589v4.616c0.672-0.841,1.526-1.262,2.569-1.262

			c0.576,0,1.121,0.115,1.637,0.349c0.515,0.233,0.938,0.56,1.271,0.98s0.593,0.928,0.781,1.521c0.188,0.595,0.283,1.23,0.283,1.907

			c0,1.606-0.397,2.848-1.191,3.725c-0.794,0.876-1.748,1.315-2.86,1.315c-1.106,0-1.975-0.462-2.604-1.386V306.353L799.529,306.353

			z M799.512,301.596c0,1.124,0.152,1.936,0.458,2.437c0.5,0.817,1.176,1.227,2.03,1.227c0.693,0,1.294-0.302,1.8-0.905

			c0.507-0.603,0.759-1.503,0.759-2.697c0-1.223-0.243-2.127-0.728-2.709c-0.486-0.583-1.072-0.875-1.761-0.875

			c-0.694,0-1.295,0.302-1.801,0.905C799.764,299.581,799.512,300.454,799.512,301.596z"/>

		<path fill="#FFFFFF" d="M814.261,306.353v-1.376c-0.729,1.059-1.721,1.589-2.974,1.589c-0.555,0-1.07-0.106-1.551-0.317

			c-0.479-0.212-0.835-0.479-1.066-0.8c-0.234-0.32-0.397-0.713-0.49-1.178c-0.065-0.313-0.098-0.807-0.098-1.482v-5.809h1.59v5.199

			c0,0.83,0.032,1.39,0.097,1.678c0.1,0.417,0.312,0.745,0.637,0.983c0.323,0.238,0.723,0.357,1.2,0.357

			c0.477,0,0.924-0.123,1.342-0.365c0.418-0.245,0.712-0.577,0.887-0.998c0.174-0.421,0.259-1.031,0.259-1.831v-5.023h1.591v9.375

			h-1.423V306.353z"/>

		<path fill="#FFFFFF" d="M818.154,306.353v-9.375h1.43v1.422c0.366-0.665,0.702-1.103,1.011-1.315

			c0.31-0.212,0.648-0.318,1.021-0.318c0.535,0,1.08,0.172,1.634,0.513l-0.548,1.474c-0.389-0.229-0.777-0.344-1.165-0.344

			c-0.348,0-0.659,0.104-0.938,0.313c-0.275,0.21-0.474,0.499-0.591,0.87c-0.177,0.563-0.265,1.181-0.265,1.854v4.907H818.154z"/>

		<path fill="#FFFFFF" d="M823.556,303.555l1.571-0.248c0.088,0.631,0.333,1.112,0.736,1.448c0.404,0.335,0.967,0.503,1.69,0.503

			c0.73,0,1.271-0.148,1.625-0.446c0.353-0.296,0.529-0.644,0.529-1.046c0-0.358-0.156-0.641-0.468-0.847

			c-0.218-0.141-0.76-0.32-1.624-0.539c-1.167-0.293-1.975-0.548-2.423-0.764c-0.45-0.214-0.791-0.512-1.024-0.892

			c-0.232-0.379-0.349-0.799-0.349-1.256c0-0.418,0.097-0.805,0.287-1.162c0.19-0.355,0.451-0.651,0.781-0.888

			c0.248-0.182,0.584-0.336,1.012-0.463c0.427-0.126,0.884-0.189,1.372-0.189c0.735,0,1.382,0.105,1.938,0.318

			c0.556,0.212,0.967,0.498,1.231,0.86s0.447,0.847,0.547,1.452l-1.554,0.211c-0.07-0.48-0.274-0.859-0.612-1.13

			c-0.339-0.271-0.817-0.406-1.436-0.406c-0.729,0-1.25,0.122-1.562,0.363c-0.313,0.241-0.468,0.523-0.468,0.847

			c0,0.207,0.064,0.393,0.194,0.557c0.129,0.172,0.332,0.312,0.609,0.424c0.158,0.059,0.626,0.194,1.403,0.406

			c1.123,0.3,1.907,0.546,2.353,0.737c0.443,0.191,0.792,0.469,1.046,0.834c0.252,0.364,0.379,0.817,0.379,1.358

			c0,0.53-0.154,1.029-0.463,1.497c-0.31,0.468-0.755,0.829-1.337,1.085c-0.584,0.257-1.242,0.385-1.978,0.385

			c-1.218,0-2.147-0.254-2.785-0.759C824.139,305.301,823.733,304.55,823.556,303.555z"/>

		<path fill="#FFFFFF" d="M839.647,303.336l1.643,0.203c-0.259,0.959-0.739,1.703-1.44,2.232c-0.7,0.529-1.595,0.795-2.682,0.795

			c-1.372,0-2.458-0.422-3.262-1.267c-0.803-0.844-1.204-2.029-1.204-3.554c0-1.576,0.404-2.801,1.216-3.673

			c0.813-0.87,1.866-1.306,3.16-1.306c1.254,0,2.279,0.426,3.072,1.28c0.794,0.854,1.191,2.055,1.191,3.602

			c0,0.095-0.003,0.235-0.008,0.423h-6.991c0.059,1.029,0.35,1.818,0.873,2.366c0.524,0.548,1.177,0.821,1.96,0.821

			c0.582,0,1.079-0.152,1.492-0.459C839.079,304.494,839.407,304.005,839.647,303.336z M834.431,300.766h5.234

			c-0.07-0.789-0.271-1.381-0.601-1.775c-0.506-0.611-1.162-0.918-1.968-0.918c-0.73,0-1.344,0.246-1.841,0.733

			C834.759,299.295,834.484,299.949,834.431,300.766z"/>

		<path fill="#FFFFFF" d="M843.284,306.353v-9.375h1.422v1.316c0.293-0.46,0.685-0.828,1.174-1.108

			c0.488-0.279,1.045-0.419,1.669-0.419c0.694,0,1.264,0.144,1.708,0.434c0.444,0.287,0.758,0.691,0.94,1.209

			c0.742-1.095,1.707-1.642,2.896-1.642c0.931,0,1.645,0.257,2.145,0.771c0.5,0.517,0.752,1.309,0.752,2.38v6.435h-1.58v-5.905

			c0-0.636-0.053-1.094-0.155-1.373s-0.29-0.505-0.561-0.674c-0.271-0.171-0.588-0.257-0.953-0.257c-0.658,0-1.206,0.22-1.641,0.658

			c-0.437,0.439-0.653,1.141-0.653,2.105v5.446h-1.59v-6.091c0-0.707-0.129-1.235-0.389-1.589c-0.258-0.354-0.681-0.53-1.271-0.53

			c-0.446,0-0.86,0.118-1.24,0.354c-0.379,0.237-0.654,0.58-0.825,1.033c-0.172,0.453-0.256,1.106-0.256,1.96v4.863h-1.592V306.353z

			"/>

		<path fill="#FFFFFF" d="M864.759,303.336l1.643,0.203c-0.259,0.959-0.739,1.703-1.439,2.232c-0.701,0.529-1.596,0.795-2.683,0.795

			c-1.372,0-2.458-0.422-3.262-1.267c-0.803-0.844-1.204-2.029-1.204-3.554c0-1.576,0.404-2.801,1.216-3.673

			c0.813-0.87,1.866-1.306,3.161-1.306c1.253,0,2.278,0.426,3.071,1.28c0.794,0.854,1.191,2.055,1.191,3.602

			c0,0.095-0.003,0.235-0.008,0.423h-6.991c0.059,1.029,0.351,1.818,0.873,2.366c0.524,0.548,1.177,0.821,1.96,0.821

			c0.582,0,1.079-0.152,1.492-0.459S864.519,304.005,864.759,303.336z M859.544,300.766h5.234c-0.07-0.789-0.271-1.381-0.601-1.775

			c-0.506-0.611-1.162-0.918-1.968-0.918c-0.73,0-1.344,0.246-1.841,0.733C859.872,299.295,859.596,299.949,859.544,300.766z"/>

		<path fill="#FFFFFF" d="M868.397,306.353v-9.375h1.43v1.332c0.688-1.028,1.684-1.544,2.983-1.544c0.565,0,1.084,0.102,1.559,0.305

			c0.474,0.203,0.828,0.469,1.063,0.798c0.236,0.331,0.4,0.722,0.494,1.176c0.059,0.293,0.09,0.81,0.09,1.545v5.763h-1.591v-5.702

			c0-0.647-0.06-1.131-0.185-1.451c-0.124-0.321-0.342-0.577-0.658-0.769c-0.314-0.19-0.683-0.288-1.107-0.288

			c-0.677,0-1.261,0.216-1.752,0.646c-0.492,0.43-0.737,1.245-0.737,2.445v5.119L868.397,306.353L868.397,306.353z"/>

		<path fill="#FFFFFF" d="M881.92,304.931l0.23,1.404c-0.448,0.095-0.848,0.142-1.2,0.142c-0.578,0-1.024-0.092-1.342-0.273

			c-0.318-0.182-0.542-0.422-0.673-0.719c-0.129-0.298-0.194-0.923-0.194-1.876v-5.394h-1.165v-1.236h1.165v-2.321l1.581-0.952

			v3.273h1.598v1.236h-1.598v5.481c0,0.453,0.027,0.746,0.084,0.874c0.057,0.13,0.146,0.232,0.273,0.31

			c0.127,0.075,0.309,0.114,0.544,0.114C881.398,304.994,881.632,304.974,881.92,304.931z"/>

	</g>

	<g enable-background="new    ">

		<path fill="#FFFFFF" d="M925.196,194.573v-12.94h1.713v11.414h6.373v1.526H925.196z"/>

		<path fill="#FFFFFF" d="M934.526,189.886c0-1.736,0.482-3.022,1.447-3.858c0.807-0.695,1.789-1.042,2.947-1.042

			c1.29,0,2.342,0.422,3.16,1.266c0.819,0.844,1.227,2.012,1.227,3.5c0,1.206-0.18,2.155-0.542,2.847

			c-0.362,0.69-0.889,1.228-1.58,1.61s-1.446,0.574-2.265,0.574c-1.312,0-2.371-0.42-3.181-1.263

			C934.931,192.681,934.526,191.469,934.526,189.886z M936.16,189.886c0,1.2,0.261,2.1,0.785,2.697

			c0.523,0.596,1.184,0.896,1.978,0.896c0.79,0,1.445-0.3,1.969-0.901c0.523-0.6,0.786-1.516,0.786-2.744

			c0-1.16-0.263-2.038-0.789-2.636c-0.526-0.597-1.182-0.897-1.964-0.897c-0.794,0-1.454,0.297-1.979,0.893

			C936.423,187.789,936.16,188.685,936.16,189.886z"/>

		<path fill="#FFFFFF" d="M951.29,193.417c-0.589,0.5-1.155,0.853-1.7,1.06c-0.544,0.207-1.129,0.31-1.752,0.31

			c-1.029,0-1.821-0.252-2.374-0.755s-0.828-1.146-0.828-1.93c0-0.457,0.104-0.877,0.313-1.258c0.209-0.377,0.483-0.683,0.821-0.912

			c0.34-0.229,0.72-0.403,1.143-0.521c0.313-0.082,0.783-0.161,1.412-0.238c1.283-0.153,2.228-0.335,2.833-0.548

			c0.007-0.217,0.01-0.355,0.01-0.414c0-0.646-0.149-1.104-0.45-1.367c-0.406-0.36-1.009-0.539-1.81-0.539

			c-0.748,0-1.299,0.131-1.654,0.393c-0.357,0.263-0.619,0.726-0.79,1.391l-1.554-0.212c0.142-0.665,0.373-1.202,0.697-1.61

			c0.323-0.408,0.791-0.723,1.404-0.944c0.611-0.22,1.321-0.33,2.127-0.33c0.801,0,1.45,0.095,1.95,0.283

			c0.5,0.188,0.868,0.425,1.104,0.71c0.234,0.286,0.399,0.646,0.494,1.082c0.053,0.27,0.079,0.758,0.079,1.465v2.118

			c0,1.478,0.034,2.411,0.102,2.803s0.201,0.767,0.402,1.126h-1.66C951.442,194.244,951.337,193.859,951.29,193.417z

			 M951.157,189.869c-0.576,0.235-1.442,0.435-2.596,0.6c-0.652,0.094-1.114,0.2-1.385,0.318c-0.271,0.117-0.481,0.288-0.628,0.516

			s-0.22,0.479-0.22,0.754c0,0.425,0.16,0.777,0.481,1.059c0.321,0.283,0.789,0.425,1.407,0.425c0.612,0,1.156-0.135,1.633-0.401

			c0.479-0.269,0.828-0.635,1.052-1.1c0.171-0.359,0.255-0.889,0.255-1.59V189.869z"/>

		<path fill="#FFFFFF" d="M955.225,194.573v-9.375h1.431v1.333c0.688-1.029,1.683-1.545,2.983-1.545

			c0.564,0,1.085,0.101,1.559,0.304s0.827,0.469,1.063,0.799c0.235,0.329,0.4,0.721,0.494,1.174

			c0.059,0.294,0.089,0.809,0.089,1.545v5.764h-1.59v-5.703c0-0.646-0.061-1.131-0.185-1.451s-0.343-0.577-0.658-0.768

			s-0.684-0.287-1.107-0.287c-0.677,0-1.261,0.215-1.753,0.645c-0.491,0.43-0.737,1.244-0.737,2.445v5.119L955.225,194.573

			L955.225,194.573z"/>

	</g>

	<g enable-background="new    ">

		<path fill="#FFFFFF" d="M914.683,203.328h1.712v7.476c0,1.301-0.146,2.333-0.441,3.099c-0.294,0.765-0.826,1.388-1.594,1.867

			c-0.768,0.48-1.776,0.72-3.022,0.72c-1.214,0-2.204-0.21-2.975-0.628c-0.771-0.417-1.322-1.021-1.65-1.813

			c-0.33-0.79-0.494-1.872-0.494-3.244v-7.476h1.713v7.468c0,1.123,0.104,1.95,0.313,2.483c0.209,0.532,0.566,0.943,1.077,1.231

			c0.509,0.289,1.13,0.433,1.867,0.433c1.259,0,2.155-0.285,2.691-0.856c0.535-0.57,0.804-1.669,0.804-3.292L914.683,203.328

			L914.683,203.328z"/>

		<path fill="#FFFFFF" d="M922.511,214.846l0.23,1.403c-0.446,0.096-0.848,0.142-1.2,0.142c-0.578,0-1.023-0.091-1.342-0.273

			s-0.542-0.423-0.672-0.72c-0.13-0.298-0.193-0.923-0.193-1.876v-5.393h-1.166v-1.236h1.166v-2.321l1.58-0.953v3.274h1.597v1.236

			h-1.597v5.482c0,0.453,0.027,0.744,0.084,0.872c0.056,0.131,0.146,0.233,0.273,0.31c0.126,0.076,0.308,0.115,0.543,0.115

			C921.991,214.908,922.223,214.887,922.511,214.846z"/>

		<path fill="#FFFFFF" d="M924.075,205.153v-1.826h1.589v1.826H924.075z M924.075,216.267v-9.375h1.589v9.375H924.075z"/>

		<path fill="#FFFFFF" d="M928.047,216.267v-12.939h1.589v12.939H928.047z"/>

		<path fill="#FFFFFF" d="M932.106,205.153v-1.826h1.591v1.826H932.106z M932.106,216.267v-9.375h1.591v9.375H932.106z"/>

		<path fill="#FFFFFF" d="M935.479,213.469l1.571-0.247c0.088,0.63,0.333,1.111,0.737,1.447c0.403,0.335,0.966,0.503,1.689,0.503

			c0.73,0,1.271-0.148,1.625-0.445c0.353-0.297,0.529-0.646,0.529-1.046c0-0.36-0.156-0.642-0.468-0.848

			c-0.218-0.141-0.76-0.321-1.625-0.538c-1.166-0.295-1.974-0.549-2.423-0.764s-0.791-0.513-1.023-0.892s-0.349-0.799-0.349-1.258

			c0-0.417,0.096-0.805,0.287-1.161c0.19-0.356,0.451-0.651,0.782-0.887c0.247-0.183,0.583-0.337,1.01-0.463

			c0.428-0.128,0.885-0.189,1.372-0.189c0.736,0,1.382,0.104,1.938,0.317c0.556,0.212,0.967,0.498,1.232,0.86

			c0.264,0.362,0.445,0.846,0.547,1.451l-1.556,0.213c-0.069-0.482-0.274-0.86-0.611-1.131c-0.339-0.271-0.817-0.405-1.437-0.405

			c-0.729,0-1.249,0.121-1.561,0.361c-0.313,0.242-0.468,0.524-0.468,0.848c0,0.207,0.063,0.392,0.194,0.557

			c0.129,0.172,0.331,0.313,0.608,0.425c0.159,0.058,0.627,0.193,1.404,0.404c1.123,0.3,1.907,0.547,2.353,0.737

			c0.443,0.191,0.792,0.47,1.046,0.834c0.253,0.365,0.379,0.818,0.379,1.359c0,0.53-0.154,1.029-0.463,1.496

			c-0.31,0.469-0.756,0.83-1.338,1.086c-0.583,0.256-1.241,0.385-1.977,0.385c-1.219,0-2.147-0.253-2.785-0.76

			C936.063,215.214,935.656,214.464,935.479,213.469z"/>

		<path fill="#FFFFFF" d="M951.271,215.112c-0.589,0.499-1.155,0.853-1.7,1.059c-0.544,0.207-1.129,0.31-1.752,0.31

			c-1.029,0-1.821-0.251-2.373-0.755c-0.555-0.503-0.83-1.146-0.83-1.929c0-0.458,0.104-0.878,0.313-1.258

			c0.209-0.379,0.482-0.685,0.822-0.913c0.339-0.229,0.719-0.402,1.142-0.521c0.313-0.082,0.784-0.162,1.412-0.238

			c1.284-0.154,2.228-0.335,2.834-0.548c0.006-0.217,0.009-0.355,0.009-0.415c0-0.646-0.149-1.103-0.45-1.367

			c-0.405-0.358-1.009-0.538-1.81-0.538c-0.747,0-1.299,0.131-1.655,0.393c-0.356,0.263-0.619,0.726-0.789,1.391l-1.555-0.211

			c0.142-0.665,0.374-1.202,0.699-1.611c0.322-0.409,0.79-0.724,1.402-0.944c0.612-0.22,1.321-0.33,2.128-0.33

			c0.8,0,1.451,0.094,1.95,0.282c0.5,0.188,0.869,0.426,1.104,0.712c0.234,0.285,0.399,0.645,0.492,1.081

			c0.055,0.271,0.08,0.758,0.08,1.465v2.118c0,1.478,0.034,2.411,0.102,2.804c0.067,0.392,0.201,0.766,0.402,1.125h-1.66

			C951.424,215.937,951.317,215.552,951.271,215.112z M951.138,211.563c-0.577,0.235-1.443,0.435-2.597,0.6

			c-0.652,0.095-1.114,0.2-1.385,0.317c-0.271,0.117-0.48,0.29-0.628,0.517c-0.146,0.228-0.22,0.479-0.22,0.754

			c0,0.424,0.159,0.778,0.481,1.06c0.32,0.282,0.789,0.424,1.407,0.424c0.612,0,1.156-0.135,1.634-0.402s0.827-0.635,1.05-1.099

			c0.172-0.358,0.257-0.889,0.257-1.589V211.563z"/>

		<path fill="#FFFFFF" d="M958.676,214.846l0.229,1.403c-0.446,0.096-0.847,0.142-1.2,0.142c-0.577,0-1.022-0.091-1.342-0.273

			c-0.317-0.183-0.541-0.423-0.671-0.72c-0.129-0.298-0.194-0.923-0.194-1.876v-5.393h-1.165v-1.236h1.165v-2.321l1.58-0.953v3.274

			h1.598v1.236h-1.598v5.482c0,0.453,0.028,0.744,0.084,0.872c0.057,0.131,0.147,0.233,0.273,0.31

			c0.127,0.076,0.308,0.115,0.543,0.115C958.156,214.908,958.388,214.887,958.676,214.846z"/>

		<path fill="#FFFFFF" d="M960.238,205.153v-1.826h1.59v1.826H960.238z M960.238,216.267v-9.375h1.59v9.375H960.238z"/>

		<path fill="#FFFFFF" d="M963.655,211.581c0-1.735,0.482-3.021,1.446-3.857c0.808-0.695,1.79-1.041,2.948-1.041

			c1.289,0,2.342,0.422,3.161,1.266c0.817,0.845,1.226,2.012,1.226,3.5c0,1.206-0.18,2.155-0.542,2.846

			c-0.362,0.691-0.889,1.229-1.58,1.611s-1.446,0.574-2.265,0.574c-1.312,0-2.372-0.421-3.182-1.263

			C964.059,214.376,963.655,213.162,963.655,211.581z M965.289,211.581c0,1.2,0.262,2.1,0.786,2.696

			c0.522,0.598,1.182,0.896,1.977,0.896c0.789,0,1.444-0.298,1.969-0.9c0.524-0.6,0.786-1.515,0.786-2.744

			c0-1.16-0.263-2.038-0.79-2.636c-0.526-0.597-1.182-0.896-1.964-0.896c-0.794,0-1.454,0.297-1.978,0.892

			S965.289,210.38,965.289,211.581z"/>

		<path fill="#FFFFFF" d="M974.301,216.267v-9.375h1.43v1.333c0.688-1.03,1.684-1.544,2.984-1.544c0.565,0,1.084,0.1,1.558,0.304

			c0.475,0.203,0.829,0.47,1.064,0.798c0.235,0.33,0.399,0.721,0.494,1.175c0.059,0.293,0.089,0.81,0.089,1.545v5.764h-1.589v-5.702

			c0-0.647-0.062-1.132-0.186-1.452c-0.123-0.32-0.343-0.576-0.658-0.768c-0.314-0.19-0.684-0.288-1.107-0.288

			c-0.676,0-1.261,0.216-1.752,0.646c-0.492,0.43-0.737,1.245-0.737,2.445v5.119L974.301,216.267L974.301,216.267z"/>

	</g>

	<g>

		

			<radialGradient id="XMLID_10_" cx="314.3328" cy="916.0291" r="27.4832" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#231F20"/>

			<stop  offset="0.1891" style="stop-color:#504A4A"/>

			<stop  offset="0.3819" style="stop-color:#7F7979"/>

			<stop  offset="0.5654" style="stop-color:#AEA9A9"/>

			<stop  offset="0.7348" style="stop-color:#D4D1D1"/>

			<stop  offset="0.8849" style="stop-color:#EFEEEE"/>

			<stop  offset="1" style="stop-color:#FFFFFF"/>

		</radialGradient>

		<circle id="XMLID_396_" fill="url(#XMLID_10_)" cx="172.553" cy="244.474" r="27.483"/>

		

			<radialGradient id="XMLID_11_" cx="314.3328" cy="913.0339" r="31.9316" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#231F20"/>

			<stop  offset="0.2388" style="stop-color:#534D4D"/>

			<stop  offset="0.4965" style="stop-color:#8D8788"/>

			<stop  offset="0.7187" style="stop-color:#C2BEBE"/>

			<stop  offset="0.8937" style="stop-color:#E9E7E7"/>

			<stop  offset="1" style="stop-color:#FFFFFF"/>

		</radialGradient>

		<circle id="XMLID_395_" fill="url(#XMLID_11_)" cx="172.553" cy="241.478" r="31.932"/>

		

			<radialGradient id="XMLID_12_" cx="315.3884" cy="914.3044" r="22.2456" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#424143"/>

			<stop  offset="0.1108" style="stop-color:#605D5E"/>

			<stop  offset="0.2481" style="stop-color:#868182"/>

			<stop  offset="0.3894" style="stop-color:#AAA5A6"/>

			<stop  offset="0.533" style="stop-color:#C9C6C7"/>

			<stop  offset="0.6801" style="stop-color:#E3E1E1"/>

			<stop  offset="0.8328" style="stop-color:#F5F4F4"/>

			<stop  offset="1" style="stop-color:#FFFFFF"/>

		</radialGradient>

		<circle id="XMLID_394_" fill="url(#XMLID_12_)" cx="173.608" cy="242.748" r="22.246"/>

		

			<radialGradient id="XMLID_13_" cx="309.3787" cy="908.3914" r="18.2948" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#FFFFFF"/>

			<stop  offset="1" style="stop-color:#F2F2F2"/>

		</radialGradient>

		<circle id="XMLID_65_" fill="url(#XMLID_13_)" cx="167.599" cy="236.836" r="26.781"/>

		

			<linearGradient id="SVGID_9_" gradientUnits="userSpaceOnUse" x1="298.0535" y1="892.7078" x2="333.1848" y2="941.3681" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)">

			<stop  offset="0" style="stop-color:#FFFFFF"/>

			<stop  offset="1" style="stop-color:#CDCCCC"/>

		</linearGradient>

		<path fill="url(#SVGID_9_)" d="M193.555,236.837c0,14.337-11.625,25.957-25.959,25.957c-14.337,0-25.957-11.62-25.957-25.957

			c0-0.428,0.01-0.852,0.032-1.274c0.664-13.744,12.018-24.685,25.927-24.685c7.862,0,14.906,3.494,19.668,9.017

			C191.186,224.442,193.555,230.362,193.555,236.837z"/>

		

			<linearGradient id="SVGID_10_" gradientUnits="userSpaceOnUse" x1="298.8098" y1="893.7546" x2="331.5932" y2="939.163" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)">

			<stop  offset="0" style="stop-color:#FBB052"/>

			<stop  offset="1" style="stop-color:#F37221"/>

		</linearGradient>

		<path fill="url(#SVGID_10_)" d="M191.819,236.837c0,13.377-10.844,24.223-24.22,24.223c-13.377,0-24.224-10.843-24.224-24.223

			c0-0.398,0.01-0.795,0.032-1.189c0.617-12.824,11.214-23.032,24.192-23.032c7.334,0,13.909,3.26,18.352,8.411

			C189.609,225.271,191.819,230.796,191.819,236.837z"/>

	</g>

	<g>

		

			<radialGradient id="XMLID_14_" cx="421.7947" cy="814.5935" r="27.4827" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#231F20"/>

			<stop  offset="0.1891" style="stop-color:#504A4A"/>

			<stop  offset="0.3819" style="stop-color:#7F7979"/>

			<stop  offset="0.5654" style="stop-color:#AEA9A9"/>

			<stop  offset="0.7348" style="stop-color:#D4D1D1"/>

			<stop  offset="0.8849" style="stop-color:#EFEEEE"/>

			<stop  offset="1" style="stop-color:#FFFFFF"/>

		</radialGradient>

		<circle id="XMLID_4_" fill="url(#XMLID_14_)" cx="280.015" cy="143.038" r="27.483"/>

		

			<radialGradient id="XMLID_15_" cx="421.7952" cy="811.5974" r="31.9316" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#231F20"/>

			<stop  offset="0.2388" style="stop-color:#534D4D"/>

			<stop  offset="0.4965" style="stop-color:#8D8788"/>

			<stop  offset="0.7187" style="stop-color:#C2BEBE"/>

			<stop  offset="0.8937" style="stop-color:#E9E7E7"/>

			<stop  offset="1" style="stop-color:#FFFFFF"/>

		</radialGradient>

		<circle id="XMLID_3_" fill="url(#XMLID_15_)" cx="280.015" cy="140.042" r="31.931"/>

		

			<radialGradient id="XMLID_16_" cx="422.8503" cy="812.8689" r="22.2461" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#424143"/>

			<stop  offset="0.1108" style="stop-color:#605D5E"/>

			<stop  offset="0.2481" style="stop-color:#868182"/>

			<stop  offset="0.3894" style="stop-color:#AAA5A6"/>

			<stop  offset="0.533" style="stop-color:#C9C6C7"/>

			<stop  offset="0.6801" style="stop-color:#E3E1E1"/>

			<stop  offset="0.8328" style="stop-color:#F5F4F4"/>

			<stop  offset="1" style="stop-color:#FFFFFF"/>

		</radialGradient>

		<circle id="XMLID_2_" fill="url(#XMLID_16_)" cx="281.07" cy="141.313" r="22.246"/>

		

			<radialGradient id="XMLID_17_" cx="416.8396" cy="806.9568" r="18.2935" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#FFFFFF"/>

			<stop  offset="1" style="stop-color:#F2F2F2"/>

		</radialGradient>

		<circle id="XMLID_1_" fill="url(#XMLID_17_)" cx="275.06" cy="135.401" r="26.781"/>

		

			<linearGradient id="SVGID_11_" gradientUnits="userSpaceOnUse" x1="405.5125" y1="791.2673" x2="440.646" y2="839.9306" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)">

			<stop  offset="0" style="stop-color:#FFFFFF"/>

			<stop  offset="1" style="stop-color:#CDCCCC"/>

		</linearGradient>

		<path fill="url(#SVGID_11_)" d="M301.017,135.402c0,14.336-11.625,25.957-25.959,25.957c-14.336,0-25.957-11.621-25.957-25.957

			c0-0.429,0.01-0.853,0.031-1.276c0.665-13.743,12.019-24.684,25.928-24.684c7.861,0,14.905,3.495,19.669,9.018

			C298.648,123.006,301.017,128.925,301.017,135.402z"/>

		

			<linearGradient id="SVGID_12_" gradientUnits="userSpaceOnUse" x1="406.2703" y1="792.3162" x2="439.0514" y2="837.7213" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)">

			<stop  offset="0" style="stop-color:#EF829A"/>

			<stop  offset="1" style="stop-color:#EE3363"/>

		</linearGradient>

		<path fill="url(#SVGID_12_)" d="M299.281,135.402c0,13.377-10.844,24.222-24.221,24.222c-13.379,0-24.224-10.843-24.224-24.222

			c0-0.4,0.01-0.795,0.032-1.189c0.617-12.825,11.214-23.033,24.192-23.033c7.334,0,13.908,3.26,18.352,8.412

			C297.071,123.835,299.281,129.36,299.281,135.402z"/>

	</g>

	<g enable-background="new    ">

		<path fill="#FFFFFF" d="M169.77,246.044h-3.858v-14.541c-1.409,1.319-3.071,2.293-4.984,2.925v-3.501

			c1.006-0.329,2.1-0.953,3.281-1.874c1.181-0.92,1.991-1.992,2.43-3.22h3.13V246.044z"/>

	</g>

	<g>

		

			<radialGradient id="XMLID_18_" cx="532.7874" cy="916.0291" r="27.4829" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#231F20"/>

			<stop  offset="0.1891" style="stop-color:#504A4A"/>

			<stop  offset="0.3819" style="stop-color:#7F7979"/>

			<stop  offset="0.5654" style="stop-color:#AEA9A9"/>

			<stop  offset="0.7348" style="stop-color:#D4D1D1"/>

			<stop  offset="0.8849" style="stop-color:#EFEEEE"/>

			<stop  offset="1" style="stop-color:#FFFFFF"/>

		</radialGradient>

		<circle id="XMLID_40_" fill="url(#XMLID_18_)" cx="391.007" cy="244.474" r="27.483"/>

		

			<radialGradient id="XMLID_19_" cx="532.7874" cy="913.0339" r="31.9316" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#231F20"/>

			<stop  offset="0.2388" style="stop-color:#534D4D"/>

			<stop  offset="0.4965" style="stop-color:#8D8788"/>

			<stop  offset="0.7187" style="stop-color:#C2BEBE"/>

			<stop  offset="0.8937" style="stop-color:#E9E7E7"/>

			<stop  offset="1" style="stop-color:#FFFFFF"/>

		</radialGradient>

		<circle id="XMLID_39_" fill="url(#XMLID_19_)" cx="391.007" cy="241.478" r="31.932"/>

		

			<radialGradient id="XMLID_20_" cx="533.843" cy="914.3044" r="22.2456" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#424143"/>

			<stop  offset="0.1108" style="stop-color:#605D5E"/>

			<stop  offset="0.2481" style="stop-color:#868182"/>

			<stop  offset="0.3894" style="stop-color:#AAA5A6"/>

			<stop  offset="0.533" style="stop-color:#C9C6C7"/>

			<stop  offset="0.6801" style="stop-color:#E3E1E1"/>

			<stop  offset="0.8328" style="stop-color:#F5F4F4"/>

			<stop  offset="1" style="stop-color:#FFFFFF"/>

		</radialGradient>

		<circle id="XMLID_38_" fill="url(#XMLID_20_)" cx="392.063" cy="242.748" r="22.246"/>

		

			<radialGradient id="XMLID_21_" cx="527.8303" cy="908.3914" r="18.2956" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#FFFFFF"/>

			<stop  offset="1" style="stop-color:#F2F2F2"/>

		</radialGradient>

		<circle id="XMLID_37_" fill="url(#XMLID_21_)" cx="386.051" cy="236.836" r="26.781"/>

		

			<linearGradient id="SVGID_13_" gradientUnits="userSpaceOnUse" x1="516.5061" y1="892.7048" x2="551.6385" y2="941.3667" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)">

			<stop  offset="0" style="stop-color:#FFFFFF"/>

			<stop  offset="1" style="stop-color:#CDCCCC"/>

		</linearGradient>

		<path fill="url(#SVGID_13_)" d="M412.01,236.837c0,14.337-11.624,25.957-25.959,25.957c-14.336,0-25.957-11.62-25.957-25.957

			c0-0.428,0.01-0.852,0.031-1.274c0.665-13.744,12.019-24.685,25.928-24.685c7.862,0,14.905,3.494,19.668,9.017

			C409.639,224.442,412.01,230.362,412.01,236.837z"/>

		

			<linearGradient id="SVGID_14_" gradientUnits="userSpaceOnUse" x1="517.2615" y1="893.7546" x2="550.0466" y2="939.1655" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)">

			<stop  offset="0" style="stop-color:#D469A8"/>

			<stop  offset="1" style="stop-color:#D03593"/>

		</linearGradient>

		<path fill="url(#SVGID_14_)" d="M410.272,236.837c0,13.377-10.844,24.223-24.222,24.223c-13.379,0-24.224-10.843-24.224-24.223

			c0-0.398,0.012-0.795,0.031-1.189c0.618-12.824,11.215-23.032,24.193-23.032c7.335,0,13.909,3.26,18.351,8.411

			C408.062,225.271,410.272,230.796,410.272,236.837z"/>

	</g>

	<g enable-background="new    ">

		<path fill="#FFFFFF" d="M378.215,240.702l3.734-0.452c0.119,0.951,0.44,1.68,0.961,2.183c0.521,0.503,1.153,0.755,1.895,0.755

			c0.796,0,1.467-0.301,2.013-0.906c0.544-0.603,0.816-1.418,0.816-2.443c0-0.97-0.261-1.739-0.783-2.307

			c-0.521-0.567-1.158-0.852-1.908-0.852c-0.495,0-1.084,0.096-1.771,0.288l0.426-3.144c1.043,0.027,1.84-0.198,2.389-0.68

			c0.549-0.48,0.824-1.119,0.824-1.915c0-0.677-0.201-1.217-0.603-1.621c-0.404-0.402-0.939-0.603-1.606-0.603

			c-0.66,0-1.223,0.229-1.689,0.686c-0.467,0.458-0.751,1.127-0.851,2.005l-3.557-0.604c0.248-1.217,0.62-2.189,1.119-2.918

			c0.499-0.728,1.195-1.3,2.087-1.716s1.893-0.625,3-0.625c1.896,0,3.415,0.604,4.559,1.813c0.943,0.988,1.415,2.105,1.415,3.351

			c0,1.767-0.966,3.177-2.897,4.229c1.153,0.248,2.075,0.802,2.766,1.661c0.691,0.862,1.037,1.9,1.037,3.117

			c0,1.767-0.645,3.273-1.937,4.519c-1.29,1.245-2.898,1.867-4.82,1.867c-1.821,0-3.331-0.523-4.531-1.572

			C379.102,243.767,378.407,242.396,378.215,240.702z"/>

	</g>

	<g>

		

			<radialGradient id="XMLID_22_" cx="751.7273" cy="916.0291" r="27.4829" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#231F20"/>

			<stop  offset="0.1891" style="stop-color:#504A4A"/>

			<stop  offset="0.3819" style="stop-color:#7F7979"/>

			<stop  offset="0.5654" style="stop-color:#AEA9A9"/>

			<stop  offset="0.7348" style="stop-color:#D4D1D1"/>

			<stop  offset="0.8849" style="stop-color:#EFEEEE"/>

			<stop  offset="1" style="stop-color:#FFFFFF"/>

		</radialGradient>

		<circle id="XMLID_44_" fill="url(#XMLID_22_)" cx="609.948" cy="244.474" r="27.483"/>

		

			<radialGradient id="XMLID_23_" cx="751.7273" cy="913.0339" r="31.9316" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#231F20"/>

			<stop  offset="0.2388" style="stop-color:#534D4D"/>

			<stop  offset="0.4965" style="stop-color:#8D8788"/>

			<stop  offset="0.7187" style="stop-color:#C2BEBE"/>

			<stop  offset="0.8937" style="stop-color:#E9E7E7"/>

			<stop  offset="1" style="stop-color:#FFFFFF"/>

		</radialGradient>

		<circle id="XMLID_43_" fill="url(#XMLID_23_)" cx="609.948" cy="241.478" r="31.932"/>

		

			<radialGradient id="XMLID_24_" cx="752.783" cy="914.3044" r="22.2456" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#424143"/>

			<stop  offset="0.1108" style="stop-color:#605D5E"/>

			<stop  offset="0.2481" style="stop-color:#868182"/>

			<stop  offset="0.3894" style="stop-color:#AAA5A6"/>

			<stop  offset="0.533" style="stop-color:#C9C6C7"/>

			<stop  offset="0.6801" style="stop-color:#E3E1E1"/>

			<stop  offset="0.8328" style="stop-color:#F5F4F4"/>

			<stop  offset="1" style="stop-color:#FFFFFF"/>

		</radialGradient>

		<circle id="XMLID_42_" fill="url(#XMLID_24_)" cx="611.003" cy="242.748" r="22.246"/>

		

			<radialGradient id="XMLID_25_" cx="746.7703" cy="908.3914" r="18.2959" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#FFFFFF"/>

			<stop  offset="1" style="stop-color:#F2F2F2"/>

		</radialGradient>

		<circle id="XMLID_41_" fill="url(#XMLID_25_)" cx="604.991" cy="236.836" r="26.781"/>

		

			<linearGradient id="SVGID_15_" gradientUnits="userSpaceOnUse" x1="735.4441" y1="892.7029" x2="770.5773" y2="941.3657" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)">

			<stop  offset="0" style="stop-color:#FFFFFF"/>

			<stop  offset="1" style="stop-color:#CDCCCC"/>

		</linearGradient>

		<path fill="url(#SVGID_15_)" d="M630.95,236.837c0,14.337-11.622,25.957-25.959,25.957c-14.336,0-25.957-11.62-25.957-25.957

			c0-0.428,0.01-0.852,0.03-1.274c0.666-13.744,12.019-24.685,25.927-24.685c7.861,0,14.908,3.494,19.668,9.017

			C628.58,224.442,630.95,230.362,630.95,236.837z"/>

		

			<linearGradient id="SVGID_16_" gradientUnits="userSpaceOnUse" x1="736.2" y1="893.7537" x2="768.9828" y2="939.1611" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)">

			<stop  offset="0" style="stop-color:#4D9ECD"/>

			<stop  offset="1" style="stop-color:#127BC0"/>

		</linearGradient>

		<path fill="url(#SVGID_16_)" d="M629.212,236.837c0,13.377-10.844,24.223-24.223,24.223c-13.381,0-24.224-10.843-24.224-24.223

			c0-0.398,0.012-0.795,0.03-1.189c0.618-12.824,11.216-23.032,24.193-23.032c7.335,0,13.911,3.26,18.351,8.411

			C627.002,225.271,629.212,230.796,629.212,236.837z"/>

	</g>

	<g enable-background="new    ">

		<path fill="#FFFFFF" d="M598.326,240.868l3.845-0.398c0.109,0.869,0.434,1.56,0.975,2.066c0.539,0.507,1.162,0.762,1.867,0.762

			c0.806,0,1.488-0.327,2.046-0.982c0.559-0.654,0.838-1.64,0.838-2.959c0-1.236-0.277-2.163-0.83-2.781

			c-0.554-0.618-1.275-0.927-2.163-0.927c-1.107,0-2.1,0.49-2.979,1.47l-3.131-0.453l1.978-10.477h10.201v3.61h-7.276l-0.605,3.42

			c0.862-0.431,1.74-0.646,2.639-0.646c1.711,0,3.162,0.623,4.353,1.868c1.188,1.245,1.784,2.86,1.784,4.847

			c0,1.657-0.48,3.136-1.441,4.436c-1.31,1.776-3.126,2.663-5.451,2.663c-1.859,0-3.372-0.498-4.545-1.496

			S598.554,242.552,598.326,240.868z"/>

	</g>

	<g>

		

			<radialGradient id="XMLID_26_" cx="971.6746" cy="916.0291" r="27.4829" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#231F20"/>

			<stop  offset="0.1891" style="stop-color:#504A4A"/>

			<stop  offset="0.3819" style="stop-color:#7F7979"/>

			<stop  offset="0.5654" style="stop-color:#AEA9A9"/>

			<stop  offset="0.7348" style="stop-color:#D4D1D1"/>

			<stop  offset="0.8849" style="stop-color:#EFEEEE"/>

			<stop  offset="1" style="stop-color:#FFFFFF"/>

		</radialGradient>

		<circle id="XMLID_48_" fill="url(#XMLID_26_)" cx="829.894" cy="244.474" r="27.483"/>

		

			<radialGradient id="XMLID_27_" cx="971.6716" cy="913.0339" r="31.9316" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#231F20"/>

			<stop  offset="0.2388" style="stop-color:#534D4D"/>

			<stop  offset="0.4965" style="stop-color:#8D8788"/>

			<stop  offset="0.7187" style="stop-color:#C2BEBE"/>

			<stop  offset="0.8937" style="stop-color:#E9E7E7"/>

			<stop  offset="1" style="stop-color:#FFFFFF"/>

		</radialGradient>

		<circle id="XMLID_47_" fill="url(#XMLID_27_)" cx="829.892" cy="241.478" r="31.932"/>

		

			<radialGradient id="XMLID_28_" cx="972.7253" cy="914.3044" r="22.2456" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#424143"/>

			<stop  offset="0.1108" style="stop-color:#605D5E"/>

			<stop  offset="0.2481" style="stop-color:#868182"/>

			<stop  offset="0.3894" style="stop-color:#AAA5A6"/>

			<stop  offset="0.533" style="stop-color:#C9C6C7"/>

			<stop  offset="0.6801" style="stop-color:#E3E1E1"/>

			<stop  offset="0.8328" style="stop-color:#F5F4F4"/>

			<stop  offset="1" style="stop-color:#FFFFFF"/>

		</radialGradient>

		<circle id="XMLID_46_" fill="url(#XMLID_28_)" cx="830.945" cy="242.748" r="22.246"/>

		

			<radialGradient id="XMLID_29_" cx="966.7156" cy="908.3914" r="18.2946" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#FFFFFF"/>

			<stop  offset="1" style="stop-color:#F2F2F2"/>

		</radialGradient>

		<circle id="XMLID_45_" fill="url(#XMLID_29_)" cx="824.935" cy="236.836" r="26.781"/>

		

			<linearGradient id="SVGID_17_" gradientUnits="userSpaceOnUse" x1="955.3914" y1="892.7068" x2="990.5239" y2="941.3688" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)">

			<stop  offset="0" style="stop-color:#FFFFFF"/>

			<stop  offset="1" style="stop-color:#CDCCCC"/>

		</linearGradient>

		<path fill="url(#SVGID_17_)" d="M850.894,236.837c0,14.337-11.622,25.957-25.959,25.957s-25.959-11.62-25.959-25.957

			c0-0.428,0.012-0.852,0.033-1.274c0.665-13.744,12.017-24.685,25.926-24.685c7.862,0,14.907,3.494,19.669,9.017

			C848.523,224.442,850.894,230.362,850.894,236.837z"/>

		

			<linearGradient id="SVGID_18_" gradientUnits="userSpaceOnUse" x1="956.1443" y1="893.7546" x2="988.926" y2="939.1605" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)">

			<stop  offset="0" style="stop-color:#86BD66"/>

			<stop  offset="1" style="stop-color:#53B548"/>

		</linearGradient>

		<path fill="url(#SVGID_18_)" d="M849.157,236.837c0,13.377-10.846,24.223-24.224,24.223c-13.38,0-24.224-10.843-24.224-24.223

			c0-0.398,0.01-0.795,0.031-1.189c0.618-12.824,11.214-23.032,24.192-23.032c7.335,0,13.911,3.26,18.352,8.411

			C846.947,225.271,849.157,230.796,849.157,236.837z"/>

	</g>

	<g enable-background="new    ">

		<path fill="#FFFFFF" d="M819.898,229.773v-3.584h13.196v2.801c-1.091,1.071-2.198,2.609-3.324,4.613

			c-1.126,2.005-1.983,4.136-2.574,6.393c-0.592,2.256-0.881,4.271-0.872,6.048h-3.72c0.063-2.782,0.638-5.62,1.723-8.513

			c1.085-2.894,2.533-5.479,4.345-7.759L819.898,229.773L819.898,229.773z"/>

	</g>

	<g enable-background="new    ">

		<path fill="#FFFFFF" d="M280.389,141.026v3.583h-13.525c0.146-1.355,0.585-2.639,1.317-3.852c0.732-1.214,2.179-2.823,4.339-4.827

			c1.739-1.62,2.806-2.718,3.199-3.295c0.531-0.796,0.796-1.583,0.796-2.363c0-0.86-0.231-1.521-0.693-1.982

			s-1.101-0.694-1.916-0.694c-0.806,0-1.447,0.243-1.923,0.728c-0.477,0.485-0.75,1.29-0.823,2.416l-3.845-0.384

			c0.229-2.124,0.947-3.647,2.156-4.573c1.208-0.924,2.718-1.387,4.53-1.387c1.986,0,3.547,0.536,4.682,1.605

			c1.136,1.071,1.703,2.404,1.703,3.996c0,0.906-0.163,1.769-0.486,2.588c-0.326,0.82-0.84,1.678-1.546,2.575

			c-0.467,0.596-1.31,1.451-2.526,2.567c-1.217,1.117-1.989,1.858-2.314,2.225c-0.326,0.366-0.588,0.723-0.79,1.071L280.389,141.026

			L280.389,141.026z"/>

	</g>

	<g>

		

			<radialGradient id="XMLID_30_" cx="640.7351" cy="814.5935" r="27.4829" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#231F20"/>

			<stop  offset="0.1891" style="stop-color:#504A4A"/>

			<stop  offset="0.3819" style="stop-color:#7F7979"/>

			<stop  offset="0.5654" style="stop-color:#AEA9A9"/>

			<stop  offset="0.7348" style="stop-color:#D4D1D1"/>

			<stop  offset="0.8849" style="stop-color:#EFEEEE"/>

			<stop  offset="1" style="stop-color:#FFFFFF"/>

		</radialGradient>

		<circle id="XMLID_8_" fill="url(#XMLID_30_)" cx="498.955" cy="143.038" r="27.483"/>

		

			<radialGradient id="XMLID_31_" cx="640.7351" cy="811.5974" r="31.9316" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#231F20"/>

			<stop  offset="0.2388" style="stop-color:#534D4D"/>

			<stop  offset="0.4965" style="stop-color:#8D8788"/>

			<stop  offset="0.7187" style="stop-color:#C2BEBE"/>

			<stop  offset="0.8937" style="stop-color:#E9E7E7"/>

			<stop  offset="1" style="stop-color:#FFFFFF"/>

		</radialGradient>

		<circle id="XMLID_7_" fill="url(#XMLID_31_)" cx="498.955" cy="140.042" r="31.931"/>

		

			<radialGradient id="XMLID_32_" cx="641.7898" cy="812.8689" r="22.2461" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#424143"/>

			<stop  offset="0.1108" style="stop-color:#605D5E"/>

			<stop  offset="0.2481" style="stop-color:#868182"/>

			<stop  offset="0.3894" style="stop-color:#AAA5A6"/>

			<stop  offset="0.533" style="stop-color:#C9C6C7"/>

			<stop  offset="0.6801" style="stop-color:#E3E1E1"/>

			<stop  offset="0.8328" style="stop-color:#F5F4F4"/>

			<stop  offset="1" style="stop-color:#FFFFFF"/>

		</radialGradient>

		<circle id="XMLID_6_" fill="url(#XMLID_32_)" cx="500.009" cy="141.313" r="22.246"/>

		

			<radialGradient id="XMLID_33_" cx="635.78" cy="806.9568" r="18.2942" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#FFFFFF"/>

			<stop  offset="1" style="stop-color:#F2F2F2"/>

		</radialGradient>

		<circle id="XMLID_5_" fill="url(#XMLID_33_)" cx="494" cy="135.401" r="26.781"/>

		

			<linearGradient id="SVGID_19_" gradientUnits="userSpaceOnUse" x1="624.4529" y1="791.2683" x2="659.5862" y2="839.9315" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)">

			<stop  offset="0" style="stop-color:#FFFFFF"/>

			<stop  offset="1" style="stop-color:#CDCCCC"/>

		</linearGradient>

		<path fill="url(#SVGID_19_)" d="M519.957,135.402c0,14.336-11.624,25.957-25.959,25.957c-14.337,0-25.957-11.621-25.957-25.957

			c0-0.429,0.01-0.853,0.032-1.276c0.665-13.743,12.019-24.684,25.928-24.684c7.861,0,14.906,3.495,19.668,9.018

			C517.588,123.006,519.957,128.925,519.957,135.402z"/>

		

			<linearGradient id="SVGID_20_" gradientUnits="userSpaceOnUse" x1="625.2117" y1="792.3191" x2="657.9943" y2="837.7262" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)">

			<stop  offset="0" style="stop-color:#777BB8"/>

			<stop  offset="1" style="stop-color:#52449B"/>

		</linearGradient>

		<path fill="url(#SVGID_20_)" d="M518.22,135.402c0,13.377-10.843,24.222-24.221,24.222s-24.223-10.843-24.223-24.222

			c0-0.4,0.01-0.795,0.031-1.189c0.618-12.825,11.214-23.033,24.192-23.033c7.335,0,13.909,3.26,18.352,8.412

			C516.01,123.835,518.22,129.36,518.22,135.402z"/>

	</g>

	<g enable-background="new    ">

		<path fill="#FFFFFF" d="M493.865,144.609v-4.051h-8.238v-3.377l8.733-12.784h3.241v12.769h2.499v3.392h-2.499v4.051H493.865z

			 M493.865,137.166v-6.877l-4.627,6.877H493.865z"/>

	</g>

	<g>

		

			<radialGradient id="XMLID_34_" cx="862.6902" cy="814.5935" r="27.4824" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#231F20"/>

			<stop  offset="0.1891" style="stop-color:#504A4A"/>

			<stop  offset="0.3819" style="stop-color:#7F7979"/>

			<stop  offset="0.5654" style="stop-color:#AEA9A9"/>

			<stop  offset="0.7348" style="stop-color:#D4D1D1"/>

			<stop  offset="0.8849" style="stop-color:#EFEEEE"/>

			<stop  offset="1" style="stop-color:#FFFFFF"/>

		</radialGradient>

		<circle id="XMLID_52_" fill="url(#XMLID_34_)" cx="720.909" cy="143.038" r="27.483"/>

		

			<radialGradient id="XMLID_35_" cx="862.6882" cy="811.5974" r="31.9316" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#231F20"/>

			<stop  offset="0.2388" style="stop-color:#534D4D"/>

			<stop  offset="0.4965" style="stop-color:#8D8788"/>

			<stop  offset="0.7187" style="stop-color:#C2BEBE"/>

			<stop  offset="0.8937" style="stop-color:#E9E7E7"/>

			<stop  offset="1" style="stop-color:#FFFFFF"/>

		</radialGradient>

		<circle id="XMLID_51_" fill="url(#XMLID_35_)" cx="720.908" cy="140.042" r="31.931"/>

		

			<radialGradient id="XMLID_36_" cx="863.741" cy="812.8689" r="22.2461" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#424143"/>

			<stop  offset="0.1108" style="stop-color:#605D5E"/>

			<stop  offset="0.2481" style="stop-color:#868182"/>

			<stop  offset="0.3894" style="stop-color:#AAA5A6"/>

			<stop  offset="0.533" style="stop-color:#C9C6C7"/>

			<stop  offset="0.6801" style="stop-color:#E3E1E1"/>

			<stop  offset="0.8328" style="stop-color:#F5F4F4"/>

			<stop  offset="1" style="stop-color:#FFFFFF"/>

		</radialGradient>

		<circle id="XMLID_50_" fill="url(#XMLID_36_)" cx="721.962" cy="141.313" r="22.246"/>

		

			<radialGradient id="XMLID_57_" cx="857.7312" cy="806.9568" r="18.2945" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#FFFFFF"/>

			<stop  offset="1" style="stop-color:#F2F2F2"/>

		</radialGradient>

		<circle id="XMLID_49_" fill="url(#XMLID_57_)" cx="715.952" cy="135.401" r="26.781"/>

		

			<linearGradient id="SVGID_21_" gradientUnits="userSpaceOnUse" x1="846.407" y1="791.2732" x2="881.5381" y2="839.9335" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)">

			<stop  offset="0" style="stop-color:#FFFFFF"/>

			<stop  offset="1" style="stop-color:#CDCCCC"/>

		</linearGradient>

		<path fill="url(#SVGID_21_)" d="M741.911,135.402c0,14.336-11.622,25.957-25.961,25.957c-14.337,0-25.958-11.621-25.958-25.957

			c0-0.429,0.011-0.853,0.033-1.276c0.666-13.743,12.016-24.684,25.925-24.684c7.861,0,14.91,3.495,19.668,9.018

			C739.54,123.006,741.911,128.925,741.911,135.402z"/>

		

			<linearGradient id="SVGID_22_" gradientUnits="userSpaceOnUse" x1="847.1628" y1="792.3201" x2="879.9448" y2="837.7264" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)">

			<stop  offset="0" style="stop-color:#85CBC3"/>

			<stop  offset="1" style="stop-color:#28B4C9"/>

		</linearGradient>

		<path fill="url(#SVGID_22_)" d="M740.175,135.402c0,13.377-10.846,24.222-24.226,24.222c-13.378,0-24.222-10.843-24.222-24.222

			c0-0.4,0.011-0.795,0.029-1.189c0.62-12.825,11.215-23.033,24.192-23.033c7.336,0,13.913,3.26,18.354,8.412

			C737.962,123.835,740.175,129.36,740.175,135.402z"/>

	</g>

	<g enable-background="new    ">

		<path fill="#FFFFFF" d="M722.421,129.409l-3.734,0.412c-0.093-0.769-0.33-1.337-0.714-1.703c-0.385-0.366-0.884-0.55-1.498-0.55

			c-0.814,0-1.503,0.367-2.064,1.099c-0.563,0.732-0.919,2.256-1.064,4.572c0.961-1.135,2.155-1.703,3.583-1.703

			c1.612,0,2.992,0.614,4.141,1.84c1.148,1.226,1.722,2.81,1.722,4.75c0,2.061-0.603,3.714-1.812,4.958

			c-1.209,1.245-2.759,1.868-4.655,1.868c-2.031,0-3.702-0.79-5.012-2.369c-1.309-1.58-1.963-4.167-1.963-7.765

			c0-3.688,0.681-6.349,2.046-7.978c1.364-1.63,3.136-2.445,5.313-2.445c1.528,0,2.794,0.429,3.796,1.284

			C721.508,126.535,722.145,127.78,722.421,129.409z M713.674,137.826c0,1.253,0.289,2.221,0.865,2.904

			c0.577,0.682,1.235,1.021,1.978,1.021c0.714,0,1.309-0.278,1.786-0.836c0.476-0.559,0.713-1.475,0.713-2.747

			c0-1.308-0.257-2.267-0.769-2.875c-0.513-0.609-1.154-0.913-1.923-0.913c-0.741,0-1.367,0.291-1.881,0.872

			C713.93,135.833,713.674,136.691,713.674,137.826z"/>

	</g>

	<g>

		

			<radialGradient id="XMLID_58_" cx="1090.6687" cy="814.5935" r="27.4829" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#231F20"/>

			<stop  offset="0.1891" style="stop-color:#504A4A"/>

			<stop  offset="0.3819" style="stop-color:#7F7979"/>

			<stop  offset="0.5654" style="stop-color:#AEA9A9"/>

			<stop  offset="0.7348" style="stop-color:#D4D1D1"/>

			<stop  offset="0.8849" style="stop-color:#EFEEEE"/>

			<stop  offset="1" style="stop-color:#FFFFFF"/>

		</radialGradient>

		<circle id="XMLID_56_" fill="url(#XMLID_58_)" cx="948.888" cy="143.038" r="27.483"/>

		

			<radialGradient id="XMLID_59_" cx="1090.6687" cy="811.5974" r="31.9331" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#231F20"/>

			<stop  offset="0.2388" style="stop-color:#534D4D"/>

			<stop  offset="0.4965" style="stop-color:#8D8788"/>

			<stop  offset="0.7187" style="stop-color:#C2BEBE"/>

			<stop  offset="0.8937" style="stop-color:#E9E7E7"/>

			<stop  offset="1" style="stop-color:#FFFFFF"/>

		</radialGradient>

		<circle id="XMLID_55_" fill="url(#XMLID_59_)" cx="948.887" cy="140.042" r="31.933"/>

		

			<radialGradient id="XMLID_60_" cx="1091.7205" cy="812.8689" r="22.2461" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#424143"/>

			<stop  offset="0.1108" style="stop-color:#605D5E"/>

			<stop  offset="0.2481" style="stop-color:#868182"/>

			<stop  offset="0.3894" style="stop-color:#AAA5A6"/>

			<stop  offset="0.533" style="stop-color:#C9C6C7"/>

			<stop  offset="0.6801" style="stop-color:#E3E1E1"/>

			<stop  offset="0.8328" style="stop-color:#F5F4F4"/>

			<stop  offset="1" style="stop-color:#FFFFFF"/>

		</radialGradient>

		<circle id="XMLID_54_" fill="url(#XMLID_60_)" cx="949.94" cy="141.313" r="22.246"/>

		

			<radialGradient id="XMLID_61_" cx="1085.7078" cy="806.9568" r="18.2951" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)" gradientUnits="userSpaceOnUse">

			<stop  offset="0" style="stop-color:#FFFFFF"/>

			<stop  offset="1" style="stop-color:#F2F2F2"/>

		</radialGradient>

		<circle id="XMLID_53_" fill="url(#XMLID_61_)" cx="943.927" cy="135.401" r="26.779"/>

		

			<linearGradient id="SVGID_23_" gradientUnits="userSpaceOnUse" x1="1074.3875" y1="791.2732" x2="1109.5199" y2="839.9351" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)">

			<stop  offset="0" style="stop-color:#FFFFFF"/>

			<stop  offset="1" style="stop-color:#CDCCCC"/>

		</linearGradient>

		<path fill="url(#SVGID_23_)" d="M969.89,135.402c0,14.336-11.625,25.957-25.963,25.957c-14.334,0-25.957-11.621-25.957-25.957

			c0-0.429,0.011-0.853,0.033-1.276c0.665-13.743,12.017-24.684,25.924-24.684c7.865,0,14.911,3.495,19.669,9.018

			C967.52,123.006,969.89,128.925,969.89,135.402z"/>

		

			<linearGradient id="SVGID_24_" gradientUnits="userSpaceOnUse" x1="1075.1404" y1="792.3181" x2="1107.9246" y2="837.7273" gradientTransform="matrix(1 0 0 1 -141.7803 -671.5557)">

			<stop  offset="0" style="stop-color:#E3E670"/>

			<stop  offset="0.6191" style="stop-color:#C5DA4F"/>

			<stop  offset="1" style="stop-color:#B6D441"/>

		</linearGradient>

		<path fill="url(#SVGID_24_)" d="M968.152,135.402c0,13.377-10.844,24.222-24.225,24.222c-13.376,0-24.222-10.843-24.222-24.222

			c0-0.4,0.011-0.795,0.03-1.189c0.62-12.825,11.215-23.033,24.191-23.033c7.339,0,13.914,3.26,18.354,8.412

			C965.943,123.835,968.152,129.36,968.152,135.402z"/>

	</g>

	<g enable-background="new    ">

		<path fill="#FFFFFF" d="M939.542,133.748c-0.998-0.421-1.724-1-2.177-1.738c-0.453-0.736-0.68-1.545-0.68-2.422

			c0-1.502,0.523-2.743,1.572-3.722c1.048-0.98,2.538-1.469,4.469-1.469c1.913,0,3.399,0.489,4.456,1.469s1.586,2.22,1.586,3.722

			c0,0.933-0.243,1.763-0.728,2.491c-0.484,0.728-1.168,1.285-2.046,1.669c1.116,0.448,1.966,1.102,2.547,1.963

			c0.581,0.86,0.872,1.854,0.872,2.979c0,1.859-0.593,3.369-1.778,4.531c-1.186,1.164-2.762,1.745-4.73,1.745

			c-1.831,0-3.354-0.482-4.571-1.443c-1.438-1.135-2.156-2.69-2.156-4.668c0-1.089,0.27-2.089,0.811-3.001

			C937.527,134.945,938.378,134.242,939.542,133.748z M939.98,138.485c0,1.062,0.272,1.891,0.817,2.484

			c0.545,0.596,1.224,0.893,2.039,0.893c0.797,0,1.455-0.286,1.978-0.858c0.522-0.572,0.783-1.397,0.783-2.478

			c0-0.944-0.266-1.7-0.796-2.272c-0.531-0.572-1.204-0.859-2.02-0.859c-0.942,0-1.645,0.326-2.106,0.976

			S939.98,137.725,939.98,138.485z M940.338,129.862c0,0.769,0.218,1.367,0.652,1.798c0.435,0.432,1.014,0.646,1.736,0.646

			c0.733,0,1.318-0.218,1.758-0.653c0.439-0.434,0.659-1.036,0.659-1.805c0-0.722-0.218-1.302-0.651-1.737

			c-0.436-0.435-1.01-0.652-1.724-0.652c-0.741,0-1.331,0.22-1.771,0.66C940.558,128.557,940.338,129.139,940.338,129.862z"/>

	</g>

</g>

</svg>

