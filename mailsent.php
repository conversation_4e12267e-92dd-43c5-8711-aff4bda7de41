<?php

use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

require_once 'PHPMailer/src/Exception.php';
require_once 'PHPMailer/src/PHPMailer.php';
require_once 'PHPMailer/src/SMTP.php';

$mail = new PHPMailer(true);
$alert = ''; // Initialize error message

if (isset($_POST['Send'])) {
    $name = $_POST['name'];
    $email = $_POST['email'];
    $mobile = $_POST['phone'];
    $position = $_POST['position'];
    $exp = $_POST['exp'];
    $ctc = $_POST['ctc'];

    // File upload handling
    $fileToUpload_tmp = $_FILES['fileToUpload']['tmp_name'];
    $fileToUpload_name = $_FILES['fileToUpload']['name'];
    $fileToUpload_upload_path = 'uploads/';

    // Check if the upload directory exists and is writable
    if (!is_dir($fileToUpload_upload_path) || !is_writable($fileToUpload_upload_path)) {
        $alert = "<div class='alert-error'><span>Error: Upload directory is not writable.</span></div>";
    } 
    elseif (!move_uploaded_file($fileToUpload_tmp, $fileToUpload_upload_path . $fileToUpload_name)) {
        $alert = "<div class='alert-error'><span>Error uploading file.</span></div>";
    } 
    else {
        try {
            // Configure PHPMailer
            $mail->isSMTP();
            $mail->Host = 'bom1plzcpnl503357.prod.bom1.secureserver.net';
            $mail->SMTPAuth = true;
            $mail->Username = '<EMAIL>';
            $mail->Password = 'KMhZ4$s84goj';
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $mail->Port = 587;

            // Set sender and recipient
            $mail->setFrom('<EMAIL>', $name);
            $mail->addAddress('<EMAIL>', 'Tatwa Recruiter');

            // Set email subject
            $mail->Subject = "Job Application For " . $position;

            // Build the email body as HTML
            $message = "
                <p>Dear Recruiter,</p>
                <p>Here are the employee details:</p>
                <ul>
                    <li><strong>Name:</strong> {$name}</li>
                    <li><strong>Email Id:</strong> {$email}</li>
                    <li><strong>Contact:</strong> {$mobile}</li>
                    <li><strong>Interested job position:</strong> {$position}</li>
                    <li><strong>Experience:</strong> {$exp}</li>
                    <li><strong>CTC:</strong> {$ctc}</li>
                </ul>
            ";

            // Set the email body as HTML
            $mail->isHTML(true);
            $mail->Body = $message;

            // Attach the uploaded file
            $mail->addAttachment($fileToUpload_upload_path . $fileToUpload_name, $fileToUpload_name);

            // Send the email
            $mail->send();
            
            // Success message
            $alert = "<div class='alert-success'><span>Message sent successfully!</span></div>";

        } catch (Exception $e) {
            // Handle PHPMailer errors
            $alert = "<div class='alert-error'><span>Message Sending Failed! PHPMailer Error: {$mail->ErrorInfo}</span></div>";
        }
    }
}

?>
