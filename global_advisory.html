<!DOCTYPE html>
<!--[if lt IE 7]>      <html class="no-js lt-ie9 lt-ie8 lt-ie7"> <![endif]-->
<!--[if IE 7]>         <html class="no-js lt-ie9 lt-ie8"> <![endif]-->
<!--[if IE 8]>         <html class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]>      <html class="no-js"> <!--<![endif]-->
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-Y36RRN9KDY"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());

        gtag('config', 'G-Y36RRN9KDY');
    </script>

    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport"
        content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1, user-scalable=0" />
    <meta name="description" content="Page scroll animation using CSS animate" />
    <meta name="keywords" content="css animation, loading effect, animate, scroll loading, scroll animation" />
    <meta name="author" content="Tatwa Technologies" />
    <link rel="icon" type="image/ico" href="images/tatwa.ico">
    <title>TATWA Technologies - Business Technology Services | Mobility &amp; m Commerce Solutions | Business Process
        Outsourcing</title>
    <!--css area start-->
    <link rel="stylesheet" href="./css/style-for-nav.css">
    <link rel="stylesheet" href="http://maxcdn.bootstrapcdn.com/font-awesome/4.3.0/css/font-awesome.min.css">
    <link href="css/style.css" rel="stylesheet" type="text/css" />
    <!-- <link href="css/nav.css" rel="stylesheet" type="text/css" /> -->
    <link href="css/responsive.css" rel="stylesheet" type="text/css" />
    <link href="css/flexslider1.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="css/base.css" type="text/css">
    <link rel="stylesheet" href="css/index.css" type="text/css">
    <link href="SpryAssets/SpryTabbedPanels2.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="lightbox/colorbox.css" type="text/css" media="screen" />
    <!--css area end-->
    <!--javascripr area start-->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.1/jquery.min.js"></script>
    <!-- Include all compiled plugins (below), or include individual files as needed -->
    <script src="bootstrap/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="js/jquery-1.7.1.min.js"></script>
    <script type="text/javascript" src="lightbox/jquery.colorbox-min.js"></script>
    <script type="text/javascript" src="lightbox/jquery.colorbox.js"></script>
    <!-- links for new navbar -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.1/dist/js/bootstrap.min.js"
        integrity="sha384-Atwg2Pkwv9vp0ygtn1JAojH0nYbwNJLPhwyoVbhoPwBhjQPR5VtM2+xf0Uwh9KtT"
        crossorigin="anonymous"></script>
    <link rel="stylesheet" href="./css/style-for-nav.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <!-- links for new navbar -->
    <style>
        .social-item {
            list-style: none !important;
        }

        .social-icon .fa {
            padding-top: 11px !important;
        }
    </style>
    <script type="text/javascript">
        $m = jQuery.noConflict();
        $m(document).ready(function () {
            //Examples of how to assign the ColorBox event to elements
            var i;
            for (i = 0; i <= 100; i++) {
                //alert(gal)
                //alert("a[rel='gal"+i+"']")
                $m("a[rel='gal" + i + "']").colorbox({ transition: "fade" });
                $m(".example8").colorbox({ width: "700px", inline: true, href: "#inline_example1" });
            }
            //Example of preserving a JavaScript event for inline calls.
            $m("#click").click(function () {
                $m('#click').css({ "background-color": "#f00", "color": "#fff", "cursor": "inherit" }).text("Open this window again and this message will still be here.");
                return false;
            });
        });

        $m = jQuery.noConflict();
        $m(document).ready(function () {
            //Examples of how to assign the ColorBox event to elements
            var i;
            for (i = 0; i <= 100; i++) {
                //alert(gal)
                //alert("a[rel='gal"+i+"']")
                $m("a[rel='gal" + i + "']").colorbox({ transition: "fade" });
                $m(".example9").colorbox({ width: "700px", inline: true, href: "#inline_example2" });
            }
            //Example of preserving a JavaScript event for inline calls.
            $m("#click").click(function () {
                $m('#click').css({ "background-color": "#f00", "color": "#fff", "cursor": "inherit" }).text("Open this window again and this message will still be here.");
                return false;
            });
        });


        $m = jQuery.noConflict();
        $m(document).ready(function () {
            //Examples of how to assign the ColorBox event to elements
            var i;
            for (i = 0; i <= 100; i++) {
                //alert(gal)
                //alert("a[rel='gal"+i+"']")
                $m("a[rel='gal" + i + "']").colorbox({ transition: "fade" });
                $m(".example10").colorbox({ width: "700px", inline: true, href: "#inline_example3" });
            }
            //Example of preserving a JavaScript event for inline calls.
            $m("#click").click(function () {
                $m('#click').css({ "background-color": "#f00", "color": "#fff", "cursor": "inherit" }).text("Open this window again and this message will still be here.");
                return false;
            });
        });

        $m = jQuery.noConflict();
        $m(document).ready(function () {
            //Examples of how to assign the ColorBox event to elements
            var i;
            for (i = 0; i <= 100; i++) {
                //alert(gal)
                //alert("a[rel='gal"+i+"']")
                $m("a[rel='gal" + i + "']").colorbox({ transition: "fade" });
                $m(".example11").colorbox({ width: "700px", inline: true, href: "#inline_example4" });
            }
            //Example of preserving a JavaScript event for inline calls.
            $m("#click").click(function () {
                $m('#click').css({ "background-color": "#f00", "color": "#fff", "cursor": "inherit" }).text("Open this window again and this message will still be here.");
                return false;
            });
        });

        $m = jQuery.noConflict();
        $m(document).ready(function () {
            //Examples of how to assign the ColorBox event to elements
            var i;
            for (i = 0; i <= 100; i++) {
                //alert(gal)
                //alert("a[rel='gal"+i+"']")
                $m("a[rel='gal" + i + "']").colorbox({ transition: "fade" });
                $m(".example12").colorbox({ width: "700px", inline: true, href: "#inline_example5" });
            }
            //Example of preserving a JavaScript event for inline calls.
            $m("#click").click(function () {
                $m('#click').css({ "background-color": "#f00", "color": "#fff", "cursor": "inherit" }).text("Open this window again and this message will still be here.");
                return false;
            });
        });

        $m = jQuery.noConflict();
        $m(document).ready(function () {
            //Examples of how to assign the ColorBox event to elements
            var i;
            for (i = 0; i <= 100; i++) {
                //alert(gal)
                //alert("a[rel='gal"+i+"']")
                $m("a[rel='gal" + i + "']").colorbox({ transition: "fade" });
                $m(".example14").colorbox({ width: "700px", inline: true, href: "#inline_example7" });
            }
            //Example of preserving a JavaScript event for inline calls.
            $m("#click").click(function () {
                $m('#click').css({ "background-color": "#f00", "color": "#fff", "cursor": "inherit" }).text("Open this window again and this message will still be here.");
                return false;
            });
        });

        $m = jQuery.noConflict();
        $m(document).ready(function () {
            //Examples of how to assign the ColorBox event to elements
            var i;
            for (i = 0; i <= 100; i++) {
                //alert(gal)
                //alert("a[rel='gal"+i+"']")
                $m("a[rel='gal" + i + "']").colorbox({ transition: "fade" });
                $m(".example15").colorbox({ width: "700px", inline: true, href: "#inline_example8" });
            }
            //Example of preserving a JavaScript event for inline calls.
            $m("#click").click(function () {
                $m('#click').css({ "background-color": "#f00", "color": "#fff", "cursor": "inherit" }).text("Open this window again and this message will still be here.");
                return false;
            });
        });



    </script>
    <script language="javascript" type="text/javascript" src="js/jquery.coolfieldset.js"></script>
    <link href="http://www.jqueryscript.net/css/jquerysctipttop.css" rel="stylesheet" type="text/css">
    <script src="js/script.js"></script>
    <script>
        $(function () {
            var pull = $('#pull');
            menu = $('nav ul');
            menuHeight = menu.height();

            $(pull).on('click', function (e) {
                e.preventDefault();
                menu.slideToggle();
            });

            $(window).resize(function () {
                var w = $(window).width();
                if (w > 320 && menu.is(':hidden')) {
                    menu.removeAttr('style');
                }
            });
        });
    </script>
    <script type="text/javascript">
        $(document).ready(function () {
            //move he last list item before the first item. The purpose of this is if the user clicks to slide left he will be able to see the last item.
            $('#carousel_ul li:first').before($('#carousel_ul li:last'));


            //when user clicks the image for sliding right        
            $('#right_scroll img').click(function () {

                //get the width of the items ( i like making the jquery part dynamic, so if you change the width in the css you won't have o change it here too ) '
                var item_width = $('#carousel_ul li').outerWidth() - 10;

                //calculae the new left indent of the unordered list
                var left_indent = parseInt($('#carousel_ul').css('left')) - item_width;

                //make the sliding effect using jquery's anumate function '
                $('#carousel_ul:not(:animated)').animate({ 'left': left_indent }, 400, function () {

                    //get the first list item and put it after the last list item (that's how the infinite effects is made) '
                    $('#carousel_ul li:last').after($('#carousel_ul li:first'));

                    //and get the left indent to the default -210px
                    $('#carousel_ul').css({ 'left': '0px' });
                });
            });

            //when user clicks the image for sliding left
            $('#left_scroll img').click(function () {

                var item_width = $('#carousel_ul li').outerWidth() - 10;

                /* same as for sliding right except that it's current left indent + the item width (for the sliding right it's - item_width) */
                var left_indent = parseInt($('#carousel_ul').css('left')) + item_width;

                $('#carousel_ul:not(:animated)').animate({ 'left': left_indent }, 400, function () {

                    /* when sliding to left we are moving the last item before the first list item */
                    $('#carousel_ul li:first').before($('#carousel_ul li:last'));

                    /* and again, when we make that change we are setting the left indent of our unordered list to the default -210px */
                    $('#carousel_ul').css({ 'left': '0px' });
                });


            });
            setInterval(function () { $("#right_scroll img").trigger("click") }, 8000)
        });


        $(document).ready(function () {
            //move he last list item before the first item. The purpose of this is if the user clicks to slide left he will be able to see the last item.
            $('#carousel_ul1 li:first').before($('#carousel_ul1 li:last'));


            //when user clicks the image for sliding right        
            $('#right_scroll1 img').click(function () {

                //get the width of the items ( i like making the jquery part dynamic, so if you change the width in the css you won't have o change it here too ) '
                var item_width = $('#carousel_ul1 li').outerWidth() - 10;

                //calculae the new left indent of the unordered list
                var left_indent = parseInt($('#carousel_ul1').css('left')) - item_width;

                //make the sliding effect using jquery's anumate function '
                $('#carousel_ul1:not(:animated)').animate({ 'left': left_indent }, 400, function () {

                    //get the first list item and put it after the last list item (that's how the infinite effects is made) '
                    $('#carousel_ul1 li:last').after($('#carousel_ul1 li:first'));

                    //and get the left indent to the default -210px
                    $('#carousel_ul1').css({ 'left': '0px' });
                });
            });

            //when user clicks the image for sliding left
            $('#left_scroll1 img').click(function () {

                var item_width = $('#carousel_ul1 li').outerWidth() - 10;

                /* same as for sliding right except that it's current left indent + the item width (for the sliding right it's - item_width) */
                var left_indent = parseInt($('#carousel_ul1').css('left')) + item_width;

                $('#carousel_ul1:not(:animated)').animate({ 'left': left_indent }, 400, function () {

                    /* when sliding to left we are moving the last item before the first list item */
                    $('#carousel_ul1 li:first').before($('#carousel_ul1 li:last'));

                    /* and again, when we make that change we are setting the left indent of our unordered list to the default -210px */
                    $('#carousel_ul1').css({ 'left': '0px' });
                });


            });
            setInterval(function () { $("#right_scroll1 img").trigger("click") }, 8000)
        });
    </script>
    <script>
        $(window).scroll(function () {
            if ($(this).scrollTop() > 1) {
                $('header').addClass("sticky");
                $('.logo-quote').css('display', 'none');
                $('.search').css('display', 'none');
                $('.logo').css('width', '102px');
                $('.logo').css('height', '72px');
                $('.logo').css('padding-top', '12px');
                $('.header_area_right').css('margin-top', '9px');
                $('.head-search1').css('margin-top', '-2px');
            }
            else {
                $('header').removeClass("sticky");
                $('.logo-quote').css('display', 'block');
                $('.search').css('display', 'block');
                $('.logo').css('width', '155px');
                $('.logo').css('height', '72px');
                $('.logo').css('padding-top', '20px');
                $('.header_area_right').css('margin-top', '20px');
                $('.head-search1').css('margin-top', '4px');
            }
        });
    </script>
    <style>
        #first-child {
            top: 0;
            left: 0;
            cursor: pointer;
        }

        #second-child {
            top: 0;
            left: 50%;
            color: black;
        }

        #third-child {
            top: 50%;
            left: 0;
            background-color: crimson;
        }

        #fourth-child {
            top: 50%;
            left: 50%;
            background-color: lightseagreen;
            color: black;
        }

        .big-box {
            position: absolute;
        }

        .big-box h2 {
            text-align: center;
            margin-top: 20%;
            padding: 20px;
            width: 100%;
            font-size: 1.8em;
            letter-spacing: 2px;
            font-weight: 700;
            text-transform: uppercase;
            cursor: pointer;
        }

        @media screen and (max-width: 46.5em) {
            .big-box h2 {
                font-size: 16px;
                padding-left: 0px;
            }
        }

        @media screen and (max-width: 20.5em) {
            .big-box h2 {
                font-size: 12px;
                padding-left: 0px;
                margin-top: 30%;
            }
        }

        .modal-dialog {
            width: 100%;
            height: 100%;
            padding: 0;
            margin: 0;
        }

        .modal-content {
            height: 100%;
            border-radius: 0;
            color: white;
            overflow: auto;
        }

        .modal-title {
            font-size: 1.5em;
            font-weight: 300;
            margin: 8px;
        }

        .modal-content-one {
            background-image: url(images/BG_Services.jpg);
            background-position: center top;
            background-repeat: no-repeat;
            background-size: cover;
            width: 100%;
            height: 100%;
        }

        .modal-content-two {
            background-color: #484b4d;
        }

        .modal-content-three {
            background-color: #484b4d;
        }

        .modal-content-four {
            background-color: #484b4d;
        }

        close {
            color: white ! important;
            opacity: 1.0;
        }
    </style>
</head>

<body style="background-color:#fff; ">
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <div class="container-fluid">
            <a class="navbar-brand" href="index"><img class="img-fluid" src="./images/logo.png" alt=""></a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false"
                aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarSupportedContent">
                <ul class="navbar-nav ms-auto mb-2 mb-lg-0">
                    <li class="nav-item dropdown">
                        <a class="nav-link nav-a-style dropdown-toggle" href="#" id="navbarDropdown" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false">
                            About Us
                        </a>
                        <ul class="dropdown-menu dropdown-for-about" aria-labelledby="navbarDropdown">
                            <div class="row">
                                <div class="dropdown-column">
                                    <li class="list"><a class="dropdown-item" href="overview">Overview</a></li>
                                    <li class="list"><a class="dropdown-item" href="leadership">Leadership</a></li>
                                    <li class="list"><a class="dropdown-item" href="global_advisory">Global
                                            Advisory</a></li>
                                    <li class="list"><a class="dropdown-item" href="accolades">Accolades</a></li>
                                    <li class="list"><a class="dropdown-item" href="infrastructure">Infrastructure</a>
                                    </li>
                                    <li class="list"><a class="dropdown-item" href="privacy_policy">Privacy
                                            Policy</a>
                                    </li>
                                    <li class="list"><a class="dropdown-item" href="quality_policy">Quality
                                            Policy</a></li>
                                    <li class="list"><a class="dropdown-item" href="life-at-tatwa">Life At Tatwa</a>
                                    </li>
                                    <li class="list"><a class="dropdown-item" href="awards">Award</a></li>
                                </div>
                            </div>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link nav-a-style dropdown-toggle" href="#" id="navbarDropdown" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false">
                            Services
                        </a>
                        <ul class="dropdown-menu dropdown-for-service" aria-labelledby="navbarDropdown">
                            <div class="row">
                                <div class="col-lg-4 col-md-4 col-sm-12">
                                    <div class="dropdown-column">
                                        <h6>Cx Automation & Operations</h6>
                                        <li>
                                            <hr class="dropdown-divider">
                                        </li>
                                        <li class="list"><a class="dropdown-item" href="inbound-process">Inbound
                                                Process</a></li>
                                        <li class="list"><a class="dropdown-item" href="outbound-process">Outbound
                                                Process</a></li>
                                        <li class="list"><a class="dropdown-item"
                                                href="technical-help-desk-data-support">Technical Help
                                                Desk & <br>
                                                Data Support</a>
                                        </li>
                                        <li class="list"><a class="dropdown-item"
                                                href="customer-life-cycle-management">Customer Life
                                                Cycle</a></li>
                                        <li class="list"><a class="dropdown-item" href="premium-support-desk">Premium
                                                Support Desk</a></li>
                                        <li class="list"><a class="dropdown-item"
                                                href="customer-acquisition-retention">Customer Acquisition
                                                <br> & Retention</a></li>
                                        <li class="list"><a class="dropdown-item" href="speech-analytics">Speech
                                                Analytics</a></li>
                                        <li class="list"><a class="dropdown-item"
                                                href="contact-center-assessment">Contact
                                                Center
                                                Assessment</a></li>
                                        <li class="list"><a class="dropdown-item"
                                                href="customer-behavioral-study">Customer
                                                Behavioral
                                                Study</a></li>
                                        <li class="list"><a class="dropdown-item"
                                                href="staffing-pay-roll-processing">Staffing & Pay Roll
                                                <br> Processing</a></li>
                                        <li class="list"><a class="dropdown-item" href="kpo-data-analytics">KPO & Data
                                                Analysis</a></li>
                                        <li class="list"><a class="dropdown-item" href="scanning-digitization">Scanning
                                                & Digitization</a>
                                        </li>
                                        <li class="list"><a class="dropdown-item"
                                                href="transaction-processing">Transaction
                                                Processing</a>
                                        </li>
                                        <li class="list"><a class="dropdown-item" href="form-processing">Form
                                                Processing</a></li>
                                        <li class="list"><a class="dropdown-item"
                                                href="survey-and-market-research">Survey &
                                                Market
                                                Research</a></li>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-4 col-sm-12" style="padding-left: 36px;">
                                    <div class="dropdown-column dropdown-column-cus">
                                        <h6>AI, Data Analytics,<br>
                                            Application Development,<br>
                                            Implementation & Maintenance
                                        </h6>
                                        <li>
                                            <hr class="dropdown-divider">
                                        </li>
                                        <li class="list"><a class="dropdown-item" href="digital-transformation">Digital
                                                Transformation</a>
                                        </li>
                                        <li class="list"><a class="dropdown-item" href="blockchain-services">Blockchain
                                                Services</a></li>
                                        <li class="list"><a class="dropdown-item" href="cyber-security-services">Cyber
                                                Security Services</a>
                                        </li>
                                        <li class="list"><a class="dropdown-item"
                                                href="custom-enterprise-software-development">Custom
                                                Enterprise Software <br>
                                                Development</a>
                                        </li>
                                        <li class="list"><a class="dropdown-item"
                                                href="quality-control-assurance-services">Quality
                                                Assurance</a></li>
                                        <li class="list"><a class="dropdown-item" href="web-design">Web Design &
                                                Development</a></li>
                                        <li class="list"><a class="dropdown-item" href="mobile-apps-development">Mobile
                                                Apps Development</a>
                                        </li>
                                        <li class="list"><a class="dropdown-item" href="esign-emSigner">eSign & emSigner
                                                Integration</a>
                                        </li>
                                        <li class="list"><a class="dropdown-item"
                                                href="resource-augmentation-staffing">Resource
                                                Augmentation and Staffing</a></li>
                                        <li class="list"><a class="dropdown-item"
                                                href="artificial-intelligence">Artificial
                                                Intelligence
                                                (Al)</a></li>
                                        <li class="list"><a class="dropdown-item" href="data-analytics">Data
                                                Analytics</a></li>
                                        <li class="list"><a class="dropdown-item"
                                                href="digital_marketing_services">Digital
                                                Marketing
                                                Services</a></li>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-4 col-sm-6" style="padding-left: 36px;">
                                    <div class="dropdown-column dropdown-column-cus bor">
                                        <h6>IT & Cloud <br>Operations</h6>
                                        <li>
                                            <hr class="dropdown-divider">
                                        </li>
                                        <li class="list"><a class="dropdown-item" href="data-center">Data Center</a>
                                        </li>
                                        <li class="list"><a class="dropdown-item" href="cloud-computing">Cloud
                                                Computing</a></li>
                                        <li class="list"><a class="dropdown-item"
                                                href="Virtualization-Services">Virtualization Services</a>
                                        </li>
                                        <li class="list"><a class="dropdown-item" href="Server-Storage-Solutions">Server
                                                & Storage
                                                Solutions</a></li>
                                        <li class="list"><a class="dropdown-item" href="Network-Infrastructure">Network
                                                Infrastructure</a>
                                        </li>
                                        <li class="list"><a class="dropdown-item"
                                                href="Security-and-Safety-Systems">Security & Safety
                                                Systems</a></li>
                                        <br>
                                        <h6><a href="">Cx Digital Services</a></h6>
                                        <li>
                                            <hr class="dropdown-divider">
                                        </li>

                                    </div>
                                </div>
                            </div>
                        </ul>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link nav-a-style" href="products">Products</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-a-style" href="partners">Partners</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-a-style" href="clients">Clients</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-a-style" href="csr">Impact</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-a-style" href="careers">Careers</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-a-style" href="blog">Blogs</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-a-style" href="contacts">Contact</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="wrapper">
        <div class="clear"></div>
        <!--inner page area start-->
        <div class="innerpage_area">
            <!--inner ban area start-->
            <div class="innerban">
                <div class="innerdiv">
                    <div class="ban_left">
                        <div class="heading-1">Global Advisory</div>
                        <div class="clear"></div>
                        <div style="float:left;">
                        </div>
                    </div>
                    <div class="ban_right">
                        <div class="break"><img src="images/bracket.png" /></div>
                        <div><span class="font20">Meet the TATWA Leadership Team.</span><br>
                            <span class="font18">Get to know the people leading the way!</span>
                        </div>
                        <div class="break"><img src="images/bracket_right.png" /></div>
                    </div>
                    <div class="clear"></div>
                </div>
                <div class="clear"></div>
            </div>
            <!--inner ban area end-->
            <div class="clear"></div>
            <!--body area start-->
            <div class="innerdiv">
                <!--left area start-->
                <div class="inner_body_left12">
                    <div class="global_advisory">
                        <div class="global_advisory_inner">
                            <div class="heading-2">Global <span>Advisory</span></div>
                            <div class="mask" style="height:5px;"></div>
                            <div class="clear"></div>
                        </div>
                        <div class="global_advisory_inner mt15">
                            <div class="g-advisory"><img src="images/sunil-sabat.jpg" alt="Dan Mishra"
                                    title="Dan Mishra" /></div>
                            <div class="g-advisory-content">
                                <p><span class="heading">Sunil Sabat:</span> has years of
                                    hardware and software industry
                                    skills in variety of roles -Intel circuit design automation
                                    to delivering enterprise
                                    software solutions involving IBM, Oracle, Microsoft and Open
                                    Source technologies. He is
                                    currently a member of Microsoft Developer Guidance Advisory
                                    council. </p>
                                <p>Sunil has earned MS ( Computer Engineering ) and MS (
                                    Computer Science ) from University
                                    of Louisiana..</p>
                                <p>Sunil is IBM DB2, Microsoft .NET, SNIA Storage and CompTIA (
                                    Linux+, Security+ and
                                    Network+) certified professional. He holds a Software
                                    Security Certificate from Stanford
                                    University. Sunil also has earned an MBA ( Marketing and
                                    Finance ) from Leavey School of
                                    Business at Santa Clara University, California.</p>
                                <p>Sunil has worked in various roles of product strategy,
                                    design, development, release,
                                    marketing, sales and support spanning over the entire
                                    revenue life cycle management backed
                                    up by direct business partner and customer engagement.</p>
                                <p>Specialties include new business development, emerging
                                    technology adaption, product
                                    promotion, customer acquisition, risk management, cost
                                    control and multi-geography
                                    personnel/operations management. </p>
                            </div>
                        </div>
                        <div class="global_advisory_inner mt15">
                            <div class="g-advisory"><img src="images/Nachiketa-Das.jpg" alt="Nachiketa Das"
                                    title="Nachiketa Das" />
                            </div>
                            <div class="g-advisory-content"> <span class="heading">Nachiketa
                                    Das:</span> has joined the
                                Board of Directors of Tatwa.
                                He is a seasoned finance professional with extensive experience
                                in capital markets across
                                the world. He is also an investor and entrepreneur with
                                interests spanning technology,
                                entertainment and healthcare. He has more than 20 years
                                experience in various aspects of
                                investment banking covering risk management, prime brokerage and
                                proprietary investments.
                                Currently, he is a Director with Barclays Capital in the Prime
                                Services division. Most
                                recently, he was Senior Vice President with Lehman Brothers in
                                New York and Tokyo where he
                                managed various exposures to capital markets through several
                                market cycles. <br />
                                <br />
                                Prior to Lehman Brothers, Nachi was a management consultant with
                                PriceWaterhouseCoopers in
                                Europe and Asia. Before that, he was a telecom engineer in India
                                where he was instrumental
                                in developing India’s first indigenous digital telecom
                                technologies.<br />
                                <br />
                                Nachi has an MBA from International University of Japan and
                                London Business School and holds
                                a BE in Electronics Engineering from NIT, Trichy in India.
                                <br />
                                <br />
                                Currently, Nachi is active in capital markets and
                                entrepreneurial ventures in New York and
                                is keen to exploit exciting opportunities in technology,
                                finance, healthcare and
                                energy.<br />
                                <br />
                                Currently, Nachi is active in capital markets and
                                entrepreneurial ventures in New York and
                                is keen to exploit exciting opportunities in technology,
                                finance, healthcare and energy.
                            </div>
                        </div>
                        <div class="global_advisory_inner mt15">
                            <div class="g-advisory"><img src="images/ZAID-FERZLY.jpg" alt="Ziad Ferzly"
                                    title="Ziad Ferzly" /></div>
                            <div class="g-advisory-content">
                                <div><span class="heading">Ziad Ferzly:</span> is now on the
                                    Global Panel of Advisors for
                                    Tatwa.Ziad Ferzly is currently the Managing Director of
                                    Cedarwood Advisors and also the
                                    Executive Director at Organisation of Arab SMEs (OASME). He
                                    has an extensive experience in
                                    turnarounds and restructurings, mergers and acquisitions,
                                    strategic analysis and planning,
                                    venture capital and private equity, corporate finance,
                                    entrepreneurship, SME development,
                                    and business expansion. <br />
                                    <br />
                                    His Specialties are Venture Capital, Private Equity,
                                    Turnarrounds, Restructurings,
                                    Macroeconomic Analysis, SME/Economics Development, Mergers
                                    &amp; Acquisitions,
                                    Consolidations/Rollups, Innovation and Technology.
                                </div>
                                <div class="clear"></div>
                            </div>
                        </div>
                        <div class="global_advisory_inner mt15">
                            <div class="g-advisory"><img src="images/kp-singh.png" alt="Dr. K. P. Singh"
                                    title="Dr. K. P. Singh" />
                            </div>
                            <div class="g-advisory-content">
                                <div><span class="heading">Dr. K. P. Singh: </span>Dr. Singh has
                                    over 20 years experience
                                    working in Aerospace, Manufacturing, Finance,
                                    Telecommunication and Healthcare Industries.
                                    He holds a BS in Chemical Engineering from NIT Rourkela. He
                                    also has a Masters and PhD
                                    from School of Aerospace and Mechanical Engineering, Old
                                    Dominion University, Norfolk, VA,
                                    USA. Dr. Singh went on to pursue Executive Management (KMI)
                                    from Kellogg School of
                                    Management, USA. Dr. Singh is currently based in Louisville,
                                    Kentucky, USA and is a proven
                                    leader in Information Technology. He has worked as Research
                                    Engineer at NASA Glenn
                                    Research Center, Cleveland, Ohio, USA and for the last 15
                                    years worked in various
                                    Technical Management roles in IT. Dr. Singh loves to
                                    volunteer his time towards service
                                    activities.</div>
                                <div class="clear"></div>
                            </div>
                        </div>
                        <div class="global_advisory_inner mt15">
                            <div class="g-advisory"><img src="images/Dierk-Van-Zandt.jpg" alt="Dierk Van Zandt"
                                    title="Dierk Van Zandt" /></div>
                            <div class="g-advisory-content">
                                <div><span class="heading">Dierk Van Zandt: </span>Technology
                                    leadership and project
                                    management with over two decades experience in architecture,
                                    strategy and implementation,
                                    covering buy-side and trading platforms in a broad range of
                                    enterprise
                                    initiatives.<br /><br />
                                    Proven leadership with geographically dispersed and
                                    culturally diverse teams in complex,
                                    high availability IT environments. Familiar with the major
                                    current data, application and
                                    infrastructure architectures, project management tools,
                                    methodologies and best practice
                                    frameworks.<br /><br />
                                    (1) Certified Project Management Professional (PMP)<br />
                                    (2) Sun/Oracle Certified Java Programmer (SCJP)<br />
                                    (3) Microsoft Certified Professional (Database Design &amp;
                                    Implementation)<br />
                                    (4) NICSA Certified Mutual Fund Specialist (CMFS)
                                </div>
                                <div class="clear"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--left area end-->
                <!--right area start-->
                <div class="inner_body_right">
                    <!--testimonial arae start-->
                    <div class="block-secondary">
                        <div class="_quote -gold-black">
                            <blockquote>
                                <h1>Clients Speak</h1>
                                <p class="font-fix">" I can highly recommend Tatwa for all your
                                    Wordpress projects. Tatwa’s
                                    design and service skills are unmatched..."</p>
                            </blockquote>
                            <div class="quote-footer">
                                <div class="quote-image"> <img src="images/testi.jpg" alt="">
                                </div>
                                <strong>Mattijs Wijnmalen</strong><br>
                                <a href="testimonial">More Testimonials</a>
                            </div>
                        </div>
                        <!-- ._quote -->
                    </div>
                    <!--testimonial area end-->
                    <!--events and news area start-->
                    <div class="strip_section1">
                        <h1>Event &amp; News</h1>
                        <div id="carousel_container1">
                            <div id="carousel_inner1">
                                <ul id="carousel_ul1" style="left:0px;">
                                    <li>Tatwa Technologies Ltd. Received the “BEST EXPORT
                                        SERVICES “ award by STPI</li>
                                    <li>Tatwa Technologies Ltd. Received "BEST TECHNOLOGY
                                        SERVICES, Odisha" award in the 8th
                                        Odisha Information Technology Fair- 2016, organized by
                                        VAR INDIA.</li>
                                    <li>Tatwa is pleased to announce the opening of new Delivery
                                        Centres in Hyderabad and
                                        Noida</li>
                                    <li>Tatwa is pleased to work with All india institute of
                                        medical sciences (AIIMS)
                                        Bhubaneshwar the premier institute of medical science of
                                        India, for HIMS.</li>
                                </ul>
                                <div class="clear"></div>
                            </div>
                            <div style="float:left; margin-right:0px; position:relative;top:0px; z-index:1000;">
                                <div id="left_scroll1"><img src="images/arrow-leftt.png"></div>
                                <div id="right_scroll1"><img src="images/arrow-right.png"></div>
                            </div>
                            <div class="clear"></div>
                        </div>
                    </div>
                    <!--events and news area end-->
                    <div class="clear"></div>
                    <!--why atwa area start-->
                    <div class="why_tatwa">
                        <h1>Why Tatwa</h1>
                        <div class="why_tatwa_cont">Speed, Focus &amp; Accountability : Inherent
                            aptitude to solve
                            clients’ business challenges through innovative technology
                            solutions.</div>
                        <div class="mask" style="height:10px;"></div>
                        <div><a href="why-tatwa">Read More</a></div>
                    </div>
                    <!--why tatwa area end-->
                    <div class="clear"></div>
                </div>
                <!--right area end-->
            </div>
            <div class="clear"></div>
            <!--footer area start-->
            <!--top footer area start-->
            <div class="footer_top">
                <div class="arial38"> Let's make something <strong>great</strong> together </div>
                <div class="clear"></div>
                <div class="button_area mt50">
                    <div><a href="blog" class="button_blue" target="_blank">Visit Our Blog</a></div>
                    <div><a href="contacts" class="button_border">Get in touch</a></div>
                    <br><br>
                    <div class="clear"></div>
                </div>
            </div>
            <!--top footer area end-->
            <div class="wrapper-inner2">
                <div class="footer container container-black container-tight">
                    <div class="container-inner social-container">
                        <div class="contact-container12">
                            <img src="images/cmmi-3.png"
                                alt="We are an ISO 9001:2008 and CMMI Level 3 Assesses company."
                                title="We are an ISO 9001:2008 and CMMI Level 3 Assesses company.">
                        </div>
                        <!---contact area start-->
                        <div class="contact-container">
                            <div class="contact">
                                <p class="contact-phone">Support Hotline: <span class="text-phone">+91 97774
                                        13556</span>
                                </p>
                                <a href="tel://******-796-6890" class="contact-phone-icon">
                                    <i style="padding:6px;" class="fa fa-phone fa-2x"></i>
                                </a>
                                <p class="visible-phone">(9am-5:30pm)</p>
                                <p class="contact-email">
                                    <a href="mailto:<EMAIL>"><EMAIL></a>
                                    <span class="hidden-phone">(9.30am-6:30pm)</span>
                                </p>
                                <p class="contact-email">
                                    <a href="mailto:<EMAIL>"><EMAIL></a>
                                </p>
                            </div>
                            <div class="clearfix" style="height:10px;"></div>
                            <!--social network area start-->
                            <ul class="social-list" style="visibility: inherit;">
                                <li class="social-item">
                                    <a href="https://twitter.com/tatwalive" target="_blank">
                                        <div class="social-icon social-icon-twitter">
                                            <span
                                                style="font-family: Arial; font-weight: bold; font-size: 1.2em;">𝕏</span>
                                        </div>
                                    </a>
                                </li>
                                <li class="social-item">
                                    <a href="https://www.facebook.com/tatwalive" target="_blank">
                                        <div class="social-icon social-icon-facebook">
                                            <i class="fa fa-facebook"></i>
                                        </div>
                                    </a>
                                </li>
                                <li class="social-item">
                                    <a href="https://www.linkedin.com/company/tatwalive" target="_blank">
                                        <div class="social-icon social-icon-linkdin">
                                            <i class="fa fa-linkedin"></i>
                                        </div>
                                    </a>
                                </li>
                                <li class="social-item">
                                    <a href="https://www.instagram.com/tatwalive" target="_blank">
                                        <div class="social-icon social-icon-instagram">
                                            <i class="fa fa-instagram"></i>
                                        </div>
                                    </a>
                                </li>
                                <li class="social-item">
                                    <a href="https://www.youtube.com/@tatwalive" target="_blank">
                                        <div class="social-icon social-icon-youtube">
                                            <i class="fa fa-youtube"></i>
                                        </div>
                                    </a>
                                </li>
                            </ul>
                            <!--social network area end-->
                            <a href="#" id="livechat-mobile" class="livechat-mobile"></a>
                        </div>
                        <!---contact area end-->
                        <div class="clearfix"></div>
                    </div>
                    <div class="line-hori"></div>
                    <!--footer bottom area start-->
                    <div class="container-inner container-inner-bordered">
                        <div id="disclaimer"> Copyright © 2024 Tatwa Technologies Ltd. All rights reserved.
                            &nbsp; &nbsp;
                            &nbsp; <a href="privacy-policy">Privacy policy</a>&nbsp; | &nbsp; <a
                                href="disclaimer">Disclaimer</a>&nbsp; | &nbsp; <a
                                href="press-release">Newsroom</a>&nbsp; |
                            &nbsp; <a href="blog" target="_blank">Blog</a>&nbsp; </div>
                    </div>
                    <!--footer bottom area end-->
                </div>
            </div>
            <!--footer area end-->
        </div>
    </div>
    <!--tab menu area end-->
    <div class="clear"></div>
    </div>
    <!--body area end-->
    <div class="clear"></div>
    </div>

    </div>

    <script type="text/javascript">
        var _gaq = _gaq || [];
        _gaq.push([ '_setAccount', 'UA-********-1' ]);
        _gaq.push([ '_setDomainName', 'tatwa.com' ]);
        _gaq.push([ '_trackPageview' ]);

        (function () {
            var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
            ga.src = ('https:' == document.location.protocol ? 'https://' : 'http://') + 'stats.g.doubleclick.net/dc.js';
            var s = document.getElementsByTagName('script')[ 0 ]; s.parentNode.insertBefore(ga, s);
        })();

    </script>
    <script type="text/javascript">jssor_html5_AdWords_slider_init();</script>
    <script type="text/javascript">
        var _gaq = _gaq || [];
        _gaq.push([ '_setAccount', 'UA-********-1' ]);
        _gaq.push([ '_setDomainName', 'tatwa.com' ]);
        _gaq.push([ '_trackPageview' ]);

        (function () {
            var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
            ga.src = ('https:' == document.location.protocol ? 'https://' : 'http://') + 'stats.g.doubleclick.net/dc.js';
            var s = document.getElementsByTagName('script')[ 0 ]; s.parentNode.insertBefore(ga, s);
        })();
    </script>
    <script type="text/javascript">
        const url = window.location.href;
        const idIndex = url.indexOf('#');
        if (idIndex > -1) {
            const targetId = url.substr(idIndex + 1);
            var elementLoadedCheck = setInterval(function () {
                if (document.getElementById(targetId)) {
                    const target = document.getElementById(targetId);
                    console.log(target);
                    target.scrollIntoView();
                    clearInterval(elementLoadedCheck);
                }
            }, 100);
        }
    </script>

    <script type="text/javascript">
        (function (j, f, c, g, e, k, h) {/*! Jssor */
            new (function () { }); var d = { Le: function (a) { return -c.cos(a * c.PI) / 2 + .5 }, kc: function (a) { return a }, Me: function (a) { return a * a }, Sc: function (a) { return -a * (a - 2) }, Ne: function (a) { return (a *= 2) < 1 ? 1 / 2 * a * a : -1 / 2 * (--a * (a - 2) - 1) }, Oe: function (a) { return a * a * a }, Pe: function (a) { return (a -= 1) * a * a + 1 }, Qe: function (a) { return (a *= 2) < 1 ? 1 / 2 * a * a * a : 1 / 2 * ((a -= 2) * a * a + 2) }, Re: function (a) { return a * a * a * a }, Te: function (a) { return -((a -= 1) * a * a * a - 1) }, cf: function (a) { return (a *= 2) < 1 ? 1 / 2 * a * a * a * a : -1 / 2 * ((a -= 2) * a * a * a - 2) }, Ue: function (a) { return a * a * a * a * a }, Ve: function (a) { return (a -= 1) * a * a * a * a + 1 }, We: function (a) { return (a *= 2) < 1 ? 1 / 2 * a * a * a * a * a : 1 / 2 * ((a -= 2) * a * a * a * a + 2) }, Xe: function (a) { return 1 - c.cos(c.PI / 2 * a) }, Ye: function (a) { return c.sin(c.PI / 2 * a) }, Dc: function (a) { return -1 / 2 * (c.cos(c.PI * a) - 1) }, Ze: function (a) { return a == 0 ? 0 : c.pow(2, 10 * (a - 1)) }, af: function (a) { return a == 1 ? 1 : -c.pow(2, -10 * a) + 1 }, bf: function (a) { return a == 0 || a == 1 ? a : (a *= 2) < 1 ? 1 / 2 * c.pow(2, 10 * (a - 1)) : 1 / 2 * (-c.pow(2, -10 * --a) + 2) }, Je: function (a) { return -(c.sqrt(1 - a * a) - 1) }, Ie: function (a) { return c.sqrt(1 - (a -= 1) * a) }, He: function (a) { return (a *= 2) < 1 ? -1 / 2 * (c.sqrt(1 - a * a) - 1) : 1 / 2 * (c.sqrt(1 - (a -= 2) * a) + 1) }, Ge: function (a) { if (!a || a == 1) return a; var b = .3, d = .075; return -(c.pow(2, 10 * (a -= 1)) * c.sin((a - d) * 2 * c.PI / b)) }, pe: function (a) { if (!a || a == 1) return a; var b = .3, d = .075; return c.pow(2, -10 * a) * c.sin((a - d) * 2 * c.PI / b) + 1 }, qe: function (a) { if (!a || a == 1) return a; var b = .45, d = .1125; return (a *= 2) < 1 ? -.5 * c.pow(2, 10 * (a -= 1)) * c.sin((a - d) * 2 * c.PI / b) : c.pow(2, -10 * (a -= 1)) * c.sin((a - d) * 2 * c.PI / b) * .5 + 1 }, re: function (a) { var b = 1.70158; return a * a * ((b + 1) * a - b) }, se: function (a) { var b = 1.70158; return (a -= 1) * a * ((b + 1) * a + b) + 1 }, te: function (a) { var b = 1.70158; return (a *= 2) < 1 ? 1 / 2 * a * a * (((b *= 1.525) + 1) * a - b) : 1 / 2 * ((a -= 2) * a * (((b *= 1.525) + 1) * a + b) + 2) }, sd: function (a) { return 1 - o.rc(1 - a) }, rc: function (a) { return a < 1 / 2.75 ? 7.5625 * a * a : a < 2 / 2.75 ? 7.5625 * (a -= 1.5 / 2.75) * a + .75 : a < 2.5 / 2.75 ? 7.5625 * (a -= 2.25 / 2.75) * a + .9375 : 7.5625 * (a -= 2.625 / 2.75) * a + .984375 }, ue: function (a) { return a < 1 / 2 ? o.sd(a * 2) * .5 : o.rc(a * 2 - 1) * .5 + .5 }, ve: function () { return 1 - c.abs(1) }, we: function (a) { return 1 - c.cos(a * c.PI * 2) }, xe: function (a) { return c.sin(a * c.PI * 2) }, ye: function (a) { return 1 - ((a *= 2) < 1 ? (a = 1 - a) * a * a : (a -= 1) * a * a) }, ze: function (a) { return (a *= 2) < 1 ? a * a * a : (a = 2 - a) * a * a } }, o = {}; var b = new function () { var i = this, Ab = /\S+/g, F = 1, yb = 2, fb = 3, eb = 4, jb = 5, G, r = 0, l = 0, s = 0, Y = 0, A = 0, I = navigator, ob = I.appName, o = I.userAgent, p = parseFloat; function Ib() { if (!G) { G = { of: "ontouchstart" in j || "createTouch" in f }; var a; if (I.pointerEnabled || (a = I.msPointerEnabled)) G.nd = a ? "msTouchAction" : "touchAction" } return G } function v(h) { if (!r) { r = -1; if (ob == "Microsoft Internet Explorer" && !!j.attachEvent && !!j.ActiveXObject) { var e = o.indexOf("MSIE"); r = F; s = p(o.substring(e + 5, o.indexOf(";", e)));/*@cc_on Y=@_jscript_version@*/; l = f.documentMode || s } else if (ob == "Netscape" && !!j.addEventListener) { var d = o.indexOf("Firefox"), b = o.indexOf("Safari"), g = o.indexOf("Chrome"), c = o.indexOf("AppleWebKit"); if (d >= 0) { r = yb; l = p(o.substring(d + 8)) } else if (b >= 0) { var i = o.substring(0, b).lastIndexOf("/"); r = g >= 0 ? eb : fb; l = p(o.substring(i + 1, b)) } else { var a = /Trident\/.*rv:([0-9]{1,}[\.0-9]{0,})/i.exec(o); if (a) { r = F; l = s = p(a[ 1 ]) } } if (c >= 0) A = p(o.substring(c + 12)) } else { var a = /(opera)(?:.*version|)[ \/]([\w.]+)/i.exec(o); if (a) { r = jb; l = p(a[ 2 ]) } } } return h == r } function q() { return v(F) } function Q() { return q() && (l < 6 || f.compatMode == "BackCompat") } function db() { return v(fb) } function ib() { return v(jb) } function vb() { return db() && A > 534 && A < 535 } function J() { v(); return A > 537 || l > 42 || r == F && l >= 11 } function O() { return q() && l < 9 } function wb(a) { var b, c; return function (f) { if (!b) { b = e; var d = a.substr(0, 1).toUpperCase() + a.substr(1); n([ a ].concat([ "WebKit", "ms", "Moz", "O", "webkit" ]), function (g, e) { var b = a; if (e) b = g + d; if (f.style[ b ] != h) return c = b }) } return c } } function ub(b) { var a; return function (c) { a = a || wb(b)(c) || b; return a } } var K = ub("transform"); function nb(a) { return {}.toString.call(a) } var kb = {}; n([ "Boolean", "Number", "String", "Function", "Array", "Date", "RegExp", "Object" ], function (a) { kb[ "[object " + a + "]" ] = a.toLowerCase() }); function n(b, d) { var a, c; if (nb(b) == "[object Array]") { for (a = 0; a < b.length; a++)if (c = d(b[ a ], a, b)) return c } else for (a in b) if (c = d(b[ a ], a, b)) return c } function D(a) { return a == g ? String(a) : kb[ nb(a) ] || "object" } function lb(a) { for (var b in a) return e } function B(a) { try { return D(a) == "object" && !a.nodeType && a != a.window && (!a.constructor || {}.hasOwnProperty.call(a.constructor.prototype, "isPrototypeOf")) } catch (b) { } } function u(a, b) { return { x: a, y: b } } function rb(b, a) { setTimeout(b, a || 0) } function H(b, d, c) { var a = !b || b == "inherit" ? "" : b; n(d, function (c) { var b = c.exec(a); if (b) { var d = a.substr(0, b.index), e = a.substr(b.index + b[ 0 ].length + 1, a.length - 1); a = d + e } }); a = c + (!a.indexOf(" ") ? "" : " ") + a; return a } function tb(b, a) { if (l < 9) b.style.filter = a } i.rf = Ib; i.rd = q; i.tf = db; i.Ff = J; wb("transform"); i.cd = function () { return l }; i.Cd = rb; function X(a) { a.constructor === X.caller && a.ud && a.ud.apply(a, X.caller.arguments) } i.ud = X; i.Db = function (a) { if (i.Df(a)) a = f.getElementById(a); return a }; function t(a) { return a || j.event } i.Fc = t; i.Zb = function (b) { b = t(b); var a = b.target || b.srcElement || f; if (a.nodeType == 3) a = i.Lc(a); return a }; i.Ic = function (a) { a = t(a); return { x: a.pageX || a.clientX || 0, y: a.pageY || a.clientY || 0 } }; function w(c, d, a) { if (a !== h) c.style[ d ] = a == h ? "" : a; else { var b = c.currentStyle || c.style; a = b[ d ]; if (a == "" && j.getComputedStyle) { b = c.ownerDocument.defaultView.getComputedStyle(c, g); b && (a = b.getPropertyValue(d) || b[ d ]) } return a } } function ab(b, c, a, d) { if (a !== h) { if (a == g) a = ""; else d && (a += "px"); w(b, c, a) } else return p(w(b, c)) } function m(c, a) { var d = a ? ab : w, b; if (a & 4) b = ub(c); return function (e, f) { return d(e, b ? b(e) : c, f, a & 2) } } function Db(b) { if (q() && s < 9) { var a = /opacity=([^)]*)/.exec(b.style.filter || ""); return a ? p(a[ 1 ]) / 100 : 1 } else return p(b.style.opacity || "1") } function Fb(b, a, f) { if (q() && s < 9) { var h = b.style.filter || "", i = new RegExp(/[\s]*alpha\([^\)]*\)/g), e = c.round(100 * a), d = ""; if (e < 100 || f) d = "alpha(opacity=" + e + ") "; var g = H(h, [ i ], d); tb(b, g) } else b.style.opacity = a == 1 ? "" : c.round(a * 100) / 100 } var L = { lb: [ "rotate" ], W: [ "rotateX" ], U: [ "rotateY" ], Jb: [ "skewX" ], Ib: [ "skewY" ] }; if (!J()) L = C(L, { z: [ "scaleX", 2 ], s: [ "scaleY", 2 ], N: [ "translateZ", 1 ] }); function M(d, a) { var c = ""; if (a) { if (q() && l && l < 10) { delete a.W; delete a.U; delete a.N } b.f(a, function (d, b) { var a = L[ b ]; if (a) { var e = a[ 1 ] || 0; if (N[ b ] != d) c += " " + a[ 0 ] + "(" + d + ([ "deg", "px", "" ])[ e ] + ")" } }); if (J()) { if (a.gb || a.db || a.N != h) c += " translate3d(" + (a.gb || 0) + "px," + (a.db || 0) + "px," + (a.N || 0) + "px)"; if (a.z == h) a.z = 1; if (a.s == h) a.s = 1; if (a.z != 1 || a.s != 1) c += " scale3d(" + a.z + ", " + a.s + ", 1)" } } d.style[ K(d) ] = c } i.Rc = m("transformOrigin", 4); i.xf = m("backfaceVisibility", 4); i.wf = m("transformStyle", 4); i.vf = m("perspective", 6); i.sf = m("perspectiveOrigin", 4); i.qf = function (a, b) { if (q() && s < 9 || s < 10 && Q()) a.style.zoom = b == 1 ? "" : b; else { var c = K(a), f = "scale(" + b + ")", e = a.style[ c ], g = new RegExp(/[\s]*scale\(.*?\)/g), d = H(e, [ g ], f); a.style[ c ] = d } }; i.ac = function (b, a) { return function (c) { c = t(c); var e = c.type, d = c.relatedTarget || (e == "mouseout" ? c.toElement : c.fromElement); (!d || d !== a && !i.nf(a, d)) && b(c) } }; i.a = function (a, d, b, c) { a = i.Db(a); if (a.addEventListener) { d == "mousewheel" && a.addEventListener("DOMMouseScroll", b, c); a.addEventListener(d, b, c) } else if (a.attachEvent) { a.attachEvent("on" + d, b); c && a.setCapture && a.setCapture() } }; i.L = function (a, c, d, b) { a = i.Db(a); if (a.removeEventListener) { c == "mousewheel" && a.removeEventListener("DOMMouseScroll", d, b); a.removeEventListener(c, d, b) } else if (a.detachEvent) { a.detachEvent("on" + c, d); b && a.releaseCapture && a.releaseCapture() } }; i.Fb = function (a) { a = t(a); a.preventDefault && a.preventDefault(); a.cancel = e; a.returnValue = k }; i.ne = function (a) { a = t(a); a.stopPropagation && a.stopPropagation(); a.cancelBubble = e }; i.J = function (d, c) { var a = [].slice.call(arguments, 2), b = function () { var b = a.concat([].slice.call(arguments, 0)); return c.apply(d, b) }; return b }; i.jf = function (a, b) { if (b == h) return a.textContent || a.innerText; var c = f.createTextNode(b); i.hc(a); a.appendChild(c) }; i.ub = function (d, c) { for (var b = [], a = d.firstChild; a; a = a.nextSibling)(c || a.nodeType == 1) && b.push(a); return b }; function mb(a, c, e, b) { b = b || "u"; for (a = a ? a.firstChild : g; a; a = a.nextSibling)if (a.nodeType == 1) { if (U(a, b) == c) return a; if (!e) { var d = mb(a, c, e, b); if (d) return d } } } i.E = mb; function S(a, d, f, b) { b = b || "u"; var c = []; for (a = a ? a.firstChild : g; a; a = a.nextSibling)if (a.nodeType == 1) { U(a, b) == d && c.push(a); if (!f) { var e = S(a, d, f, b); if (e.length) c = c.concat(e) } } return c } function gb(a, c, d) { for (a = a ? a.firstChild : g; a; a = a.nextSibling)if (a.nodeType == 1) { if (a.tagName == c) return a; if (!d) { var b = gb(a, c, d); if (b) return b } } } i.Kf = gb; i.Hf = function (b, a) { return b.getElementsByTagName(a) }; function C() { var e = arguments, d, c, b, a, g = 1 & e[ 0 ], f = 1 + g; d = e[ f - 1 ] || {}; for (; f < e.length; f++)if (c = e[ f ]) for (b in c) { a = c[ b ]; if (a !== h) { a = c[ b ]; var i = d[ b ]; d[ b ] = g && (B(i) || B(a)) ? C(g, {}, i, a) : a } } return d } i.M = C; function Z(f, g) { var d = {}, c, a, b; for (c in f) { a = f[ c ]; b = g[ c ]; if (a !== b) { var e; if (B(a) && B(b)) { a = Z(a, b); e = !lb(a) } !e && (d[ c ] = a) } } return d } i.dd = function (a) { return D(a) == "function" }; i.Df = function (a) { return D(a) == "string" }; i.nc = function (a) { return !isNaN(p(a)) && isFinite(a) }; i.f = n; i.Ke = B; function R(a) { return f.createElement(a) } i.Vb = function () { return R("DIV") }; i.oe = function () { return R("SPAN") }; i.Xc = function () { }; function V(b, c, a) { if (a == h) return b.getAttribute(c); b.setAttribute(c, a) } function U(a, b) { return V(a, b) || V(a, "data-" + b) } i.q = V; i.i = U; function y(b, a) { if (a == h) return b.className; b.className = a } i.ad = y; function qb(b) { var a = {}; n(b, function (b) { if (b != h) a[ b ] = b }); return a } function sb(b, a) { return b.match(a || Ab) } function P(b, a) { return qb(sb(b || "", a)) } i.Hd = sb; function bb(b, c) { var a = ""; n(c, function (c) { a && (a += b); a += c }); return a } function E(a, c, b) { y(a, bb(" ", C(Z(P(y(a)), P(c)), P(b)))) } i.Lc = function (a) { return a.parentNode }; i.Q = function (a) { i.bb(a, "none") }; i.X = function (a, b) { i.bb(a, b ? "none" : "") }; i.Ed = function (b, a) { b.removeAttribute(a) }; i.Kd = function () { return q() && l < 10 }; i.ke = function (d, a) { if (a) d.style.clip = "rect(" + c.round(a.j || a.A || 0) + "px " + c.round(a.H) + "px " + c.round(a.C) + "px " + c.round(a.k || a.v || 0) + "px)"; else if (a !== h) { var g = d.style.cssText, f = [ new RegExp(/[\s]*clip: rect\(.*?\)[;]?/i), new RegExp(/[\s]*cliptop: .*?[;]?/i), new RegExp(/[\s]*clipright: .*?[;]?/i), new RegExp(/[\s]*clipbottom: .*?[;]?/i), new RegExp(/[\s]*clipleft: .*?[;]?/i) ], e = H(g, f, ""); b.Hb(d, e) } }; i.ab = function () { return +new Date }; i.T = function (b, a) { b.appendChild(a) }; i.Pb = function (b, a, c) { (c || a.parentNode).insertBefore(b, a) }; i.Tb = function (b, a) { a = a || b.parentNode; a && a.removeChild(b) }; i.ie = function (a, b) { n(a, function (a) { i.Tb(a, b) }) }; i.hc = function (a) { i.ie(i.ub(a, e), a) }; i.he = function (a, b) { var c = i.Lc(a); b & 1 && i.D(a, (i.n(c) - i.n(a)) / 2); b & 2 && i.F(a, (i.p(c) - i.p(a)) / 2) }; i.Qb = function (b, a) { return parseInt(b, a || 10) }; i.ge = p; i.nf = function (b, a) { var c = f.body; while (a && b !== a && c !== a) try { a = a.parentNode } catch (d) { return k } return b === a }; function W(d, c, b) { var a = d.cloneNode(!c); !b && i.Ed(a, "id"); return a } i.Eb = W; i.rb = function (d, f) { var a = new Image; function b(e, d) { i.L(a, "load", b); i.L(a, "abort", c); i.L(a, "error", c); f && f(a, d) } function c(a) { b(a, e) } if (ib() && l < 11.6 || !d) b(!d); else { i.a(a, "load", b); i.a(a, "abort", c); i.a(a, "error", c); a.src = d } }; i.fe = function (d, a, e) { var c = d.length + 1; function b(b) { c--; if (a && b && b.src == a.src) a = b; !c && e && e(a) } n(d, function (a) { i.rb(a.src, b) }); b() }; i.ee = function (a, g, i, h) { if (h) a = W(a); var c = S(a, g); if (!c.length) c = b.Hf(a, g); for (var f = c.length - 1; f > -1; f--) { var d = c[ f ], e = W(i); y(e, y(d)); b.Hb(e, d.style.cssText); b.Pb(e, d); b.Tb(d) } return a }; function Gb(a) { var l = this, p = "", r = [ "av", "pv", "ds", "dn" ], e = [], q, k = 0, g = 0, d = 0; function j() { E(a, q, e[ d || k || g & 2 || g ]); b.O(a, "pointer-events", d ? "none" : "") } function c() { k = 0; j(); i.L(f, "mouseup", c); i.L(f, "touchend", c); i.L(f, "touchcancel", c) } function o(a) { if (d) i.Fb(a); else { k = 4; j(); i.a(f, "mouseup", c); i.a(f, "touchend", c); i.a(f, "touchcancel", c) } } l.de = function (a) { if (a === h) return g; g = a & 2 || a & 1; j() }; l.Ub = function (a) { if (a === h) return !d; d = a ? 0 : 3; j() }; l.kb = a = i.Db(a); var m = b.Hd(y(a)); if (m) p = m.shift(); n(r, function (a) { e.push(p + a) }); q = bb(" ", e); e.unshift(""); i.a(a, "mousedown", o); i.a(a, "touchstart", o) } i.Yb = function (a) { return new Gb(a) }; i.O = w; i.Mb = m("overflow"); i.F = m("top", 2); i.D = m("left", 2); i.n = m("width", 2); i.p = m("height", 2); i.dc = m("marginLeft", 2); i.wc = m("marginTop", 2); i.B = m("position"); i.bb = m("display"); i.u = m("zIndex", 1); i.qc = function (b, a, c) { if (a != h) Fb(b, a, c); else return Db(b) }; i.Hb = function (a, b) { if (b != h) a.style.cssText = b; else return a.style.cssText }; i.be = function (b, a) { if (a === h) { a = w(b, "backgroundImage") || ""; var c = /\burl\s*\(\s*["']?([^"'\r\n,]+)["']?\s*\)/gi.exec(a) || []; return c[ 1 ] } w(b, "backgroundImage", a ? "url('" + a + "')" : "") }; var T = { vb: i.qc, j: i.F, k: i.D, tb: i.n, qb: i.p, pb: i.B, Wf: i.bb, ob: i.u }; function x(f, l) { var e = O(), b = J(), d = vb(), j = K(f); function k(b, d, a) { var e = b.eb(u(-d / 2, -a / 2)), f = b.eb(u(d / 2, -a / 2)), g = b.eb(u(d / 2, a / 2)), h = b.eb(u(-d / 2, a / 2)); b.eb(u(300, 300)); return u(c.min(e.x, f.x, g.x, h.x) + d / 2, c.min(e.y, f.y, g.y, h.y) + a / 2) } function a(d, a) { a = a || {}; var n = a.N || 0, p = (a.W || 0) % 360, q = (a.U || 0) % 360, u = (a.lb || 0) % 360, l = a.z, m = a.s, f = a.Vf; if (l == h) l = 1; if (m == h) m = 1; if (f == h) f = 1; if (e) { n = 0; p = 0; q = 0; f = 0 } var c = new Cb(a.gb, a.db, n); c.W(p); c.U(q); c.Zd(u); c.Yd(a.Jb, a.Ib); c.xc(l, m, f); if (b) { c.sb(a.v, a.A); d.style[ j ] = c.Xd() } else if (!Y || Y < 9) { var o = "", g = { x: 0, y: 0 }; if (a.P) g = k(c, a.P, a.fb); i.wc(d, g.y); i.dc(d, g.x); o = c.Wd(); var s = d.style.filter, t = new RegExp(/[\s]*progid:DXImageTransform\.Microsoft\.Matrix\([^\)]*\)/g), r = H(s, [ t ], o); tb(d, r) } } x = function (e, c) { c = c || {}; var j = c.v, k = c.A, f; n(T, function (a, b) { f = c[ b ]; f !== h && a(e, f) }); i.ke(e, c.g); if (!b) { j != h && i.D(e, (c.ed || 0) + j); k != h && i.F(e, (c.id || 0) + k) } if (c.Vd) if (d) rb(i.J(g, M, e, c)); else a(e, c) }; i.wb = M; if (d) i.wb = x; if (e) i.wb = a; else if (!b) a = M; i.V = x; x(f, l) } i.wb = x; i.V = x; function Cb(j, k, o) { var d = this, b = [ 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, j || 0, k || 0, o || 0, 1 ], i = c.sin, h = c.cos, l = c.tan; function f(a) { return a * c.PI / 180 } function n(a, b) { return { x: a, y: b } } function m(c, e, l, m, o, r, t, u, w, z, A, C, E, b, f, k, a, g, i, n, p, q, s, v, x, y, B, D, F, d, h, j) { return [ c * a + e * p + l * x + m * F, c * g + e * q + l * y + m * d, c * i + e * s + l * B + m * h, c * n + e * v + l * D + m * j, o * a + r * p + t * x + u * F, o * g + r * q + t * y + u * d, o * i + r * s + t * B + u * h, o * n + r * v + t * D + u * j, w * a + z * p + A * x + C * F, w * g + z * q + A * y + C * d, w * i + z * s + A * B + C * h, w * n + z * v + A * D + C * j, E * a + b * p + f * x + k * F, E * g + b * q + f * y + k * d, E * i + b * s + f * B + k * h, E * n + b * v + f * D + k * j ] } function e(c, a) { return m.apply(g, (a || b).concat(c)) } d.xc = function (a, c, d) { if (a != 1 || c != 1 || d != 1) b = e([ a, 0, 0, 0, 0, c, 0, 0, 0, 0, d, 0, 0, 0, 0, 1 ]) }; d.sb = function (a, c, d) { b[ 12 ] += a || 0; b[ 13 ] += c || 0; b[ 14 ] += d || 0 }; d.W = function (c) { if (c) { a = f(c); var d = h(a), g = i(a); b = e([ 1, 0, 0, 0, 0, d, g, 0, 0, -g, d, 0, 0, 0, 0, 1 ]) } }; d.U = function (c) { if (c) { a = f(c); var d = h(a), g = i(a); b = e([ d, 0, -g, 0, 0, 1, 0, 0, g, 0, d, 0, 0, 0, 0, 1 ]) } }; d.Zd = function (c) { if (c) { a = f(c); var d = h(a), g = i(a); b = e([ d, g, 0, 0, -g, d, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1 ]) } }; d.Yd = function (a, c) { if (a || c) { j = f(a); k = f(c); b = e([ 1, l(k), 0, 0, l(j), 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1 ]) } }; d.eb = function (c) { var a = e(b, [ 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, c.x, c.y, 0, 1 ]); return n(a[ 12 ], a[ 13 ]) }; d.Xd = function () { return "matrix3d(" + b.join(",") + ")" }; d.Wd = function () { return "progid:DXImageTransform.Microsoft.Matrix(M11=" + b[ 0 ] + ", M12=" + b[ 4 ] + ", M21=" + b[ 1 ] + ", M22=" + b[ 5 ] + ", SizingMethod='auto expand')" } } new function () { var a = this; function b(d, g) { for (var j = d[ 0 ].length, i = d.length, h = g[ 0 ].length, f = [], c = 0; c < i; c++)for (var k = f[ c ] = [], b = 0; b < h; b++) { for (var e = 0, a = 0; a < j; a++)e += d[ c ][ a ] * g[ a ][ b ]; k[ b ] = e } return f } a.z = function (b, c) { return a.jd(b, c, 0) }; a.s = function (b, c) { return a.jd(b, 0, c) }; a.jd = function (a, c, d) { return b(a, [ [ c, 0 ], [ 0, d ] ]) }; a.eb = function (d, c) { var a = b(d, [ [ c.x ], [ c.y ] ]); return u(a[ 0 ][ 0 ], a[ 1 ][ 0 ]) } }; var N = { ed: 0, id: 0, v: 0, A: 0, jb: 1, z: 1, s: 1, lb: 0, W: 0, U: 0, gb: 0, db: 0, N: 0, Jb: 0, Ib: 0 }; i.od = function (c, d) { var a = c || {}; if (c) if (b.dd(c)) a = { R: a }; else if (b.dd(c.g)) a.g = { R: c.g }; a.R = a.R || d; if (a.g) a.g.R = a.g.R || d; return a }; function pb(c, a) { var b = {}; n(c, function (c, d) { var e = c; if (a[ d ] != h) if (i.nc(c)) e = c + a[ d ]; else e = pb(c, a[ d ]); b[ d ] = e }); return b } i.Ud = pb; i.Td = function (l, m, x, q, z, A, n) { var a = m; if (l) { a = {}; for (var i in m) { var B = A[ i ] || 1, w = z[ i ] || [ 0, 1 ], f = (x - w[ 0 ]) / w[ 1 ]; f = c.min(c.max(f, 0), 1); f = f * B; var u = c.floor(f); if (f != u) f -= u; var j = q.R || d.kc, k, C = l[ i ], o = m[ i ]; if (b.nc(o)) { j = q[ i ] || j; var y = j(f); k = C + o * y } else { k = b.M({ Sb: {} }, l[ i ]); var v = q[ i ] || {}; b.f(o.Sb || o, function (d, a) { j = v[ a ] || v.R || j; var c = j(f), b = d * c; k.Sb[ a ] = b; k[ a ] += b }) } a[ i ] = k } var t = b.f(m, function (b, a) { return N[ a ] != h }); t && b.f(N, function (c, b) { if (a[ b ] == h && l[ b ] !== h) a[ b ] = l[ b ] }); if (t) { if (a.jb) a.z = a.s = a.jb; a.P = n.P; a.fb = n.fb; a.Vd = e } } if (m.g && n.sb) { var p = a.g.Sb, s = (p.j || 0) + (p.C || 0), r = (p.k || 0) + (p.H || 0); a.k = (a.k || 0) + r; a.j = (a.j || 0) + s; a.g.k -= r; a.g.H -= r; a.g.j -= s; a.g.C -= s } if (a.g && b.Kd() && !a.g.j && !a.g.k && !a.g.A && !a.g.v && a.g.H == n.P && a.g.C == n.fb) a.g = g; return a } }; function m() { var a = this, d = []; function h(a, b) { d.push({ tc: a, sc: b }) } function g(a, c) { b.f(d, function (b, e) { b.tc == a && b.sc === c && d.splice(e, 1) }) } a.xb = a.addEventListener = h; a.removeEventListener = g; a.l = function (a) { var c = [].slice.call(arguments, 1); b.f(d, function (b) { b.tc == a && b.sc.apply(j, c) }) } } var l = function (z, E, f, K, N, M) { z = z || 0; var a = this, q, o, p, u, B = 0, H, I, G, C, y = 0, i = 0, m = 0, F, l, h, d, n, D, w = [], x; function P(a) { h += a; d += a; l += a; i += a; m += a; y += a } function t(p) { var g = p; if (n) if (!D && (g >= d || g < h) || D && g >= n) g = ((g - h) % n + n) % n + h; if (!F || u || i != g) { var j = c.min(g, d); j = c.max(j, h); if (!F || u || j != m) { if (M) { var k = (j - l) / (E || 1); if (f.Sd) k = 1 - k; var o = b.Td(N, M, k, H, G, I, f); if (x) b.f(o, function (b, a) { x[ a ] && x[ a ](K, b) }); else b.V(K, o) } a.pc(m - l, j - l); var r = m, q = m = j; b.f(w, function (b, c) { var a = g <= i ? w[ w.length - c - 1 ] : b; a.G(m - y) }); i = g; F = e; a.Ob(r, q) } } } function A(a, b, e) { b && a.Nb(d); if (!e) { h = c.min(h, a.Lb() + y); d = c.max(d, a.S() + y) } w.push(a) } var r = j.requestAnimationFrame || j.webkitRequestAnimationFrame || j.mozRequestAnimationFrame || j.msRequestAnimationFrame; if (b.tf() && b.cd() < 7) r = g; r = r || function (a) { b.Cd(a, f.Pc) }; function J() { if (q) { var d = b.ab(), e = c.min(d - B, f.Kc), a = i + e * p; B = d; if (a * p >= o * p) a = o; t(a); if (!u && a * p >= o * p) L(C); else r(J) } } function s(f, g, j) { if (!q) { q = e; u = j; C = g; f = c.max(f, h); f = c.min(f, d); o = f; p = o < i ? -1 : 1; a.Jc(); B = b.ab(); r(J) } } function L(b) { if (q) { u = q = C = k; a.Gc(); b && b() } } a.Mc = function (a, b, c) { s(a ? i + a : d, b, c) }; a.Cc = s; a.hb = L; a.Rd = function (a) { s(a) }; a.cb = function () { return i }; a.Nc = function () { return o }; a.Bb = function () { return m }; a.G = t; a.sb = function (a) { t(i + a) }; a.Oc = function () { return q }; a.Qd = function (a) { n = a }; a.Nb = P; a.I = function (a, b) { A(a, 0, b) }; a.mc = function (a) { A(a, 1) }; a.Qc = function (a) { d += a }; a.Lb = function () { return h }; a.S = function () { return d }; a.Ob = a.Jc = a.Gc = a.pc = b.Xc; a.lc = b.ab(); f = b.M({ Pc: 16, Kc: 50 }, f); n = f.jc; D = f.Pd; x = f.Od; h = l = z; d = z + E; I = f.Nd || {}; G = f.Md || {}; H = b.od(f.Z) }; new (function () { }); var i = function (p, fc) { var o = this; function Bc() { var a = this; l.call(a, -1e8, 2e8); a.Dd = function () { var b = a.Bb(), d = c.floor(b), f = t(d), e = b - c.floor(b); return { ib: f, Id: d, pb: e } }; a.Ob = function (b, a) { var d = c.floor(a); if (d != a && a > b) d++; Tb(d, e); o.l(i.Fd, t(a), t(b), a, b) } } function Ac() { var a = this; l.call(a, 0, 0, { jc: r }); b.f(C, function (b) { D & 1 && b.Qd(r); a.mc(b); b.Nb(kb / bc) }) } function zc() { var a = this, b = Ub.kb; l.call(a, -1, 2, { Z: d.kc, Od: { pb: Zb }, jc: r }, b, { pb: 1 }, { pb: -2 }); a.oc = b } function mc(n, m) { var b = this, d, f, h, j, c; l.call(b, -1e8, 2e8, { Kc: 100 }); b.Jc = function () { O = e; R = g; o.l(i.me, t(w.cb()), w.cb()) }; b.Gc = function () { O = k; j = k; var a = w.Dd(); o.l(i.Ee, t(w.cb()), w.cb()); !a.pb && Dc(a.Id, s) }; b.Ob = function (i, g) { var b; if (j) b = c; else { b = f; if (h) { var e = g / h; b = a.Wc(e) * (f - d) + d } } w.G(b) }; b.Gb = function (a, e, c, g) { d = a; f = e; h = c; w.G(a); b.G(0); b.Cc(c, g) }; b.Mf = function (a) { j = e; c = a; b.Mc(a, g, e) }; b.If = function (a) { c = a }; w = new Bc; w.I(n); w.I(m) } function oc() { var c = this, a = Xb(); b.u(a, 0); b.O(a, "pointerEvents", "none"); c.kb = a; c.Kb = function () { b.Q(a); b.hc(a) } } function xc(n, f) { var d = this, q, N, v, j, y = [], x, B, W, H, S, F, h, w, p; l.call(d, -u, u + 1, {}); function E(a) { q && q.Yc(); T(n, a, 0); F = e; q = new I.K(n, I, b.ge(b.i(n, "idle")) || lc, !L); q.G(0) } function Z() { q.lc < I.lc && E() } function O(p, r, n) { if (!H) { H = e; if (j && n) { var g = n.width, c = n.height, m = g, l = c; if (g && c && a.nb) { if (a.nb & 3 && (!(a.nb & 4) || g > K || c > J)) { var h = k, q = K / J * c / g; if (a.nb & 1) h = q > 1; else if (a.nb & 2) h = q < 1; m = h ? g * J / c : K; l = h ? J : c * K / g } b.n(j, m); b.p(j, l); b.F(j, (J - l) / 2); b.D(j, (K - m) / 2) } b.B(j, "absolute"); o.l(i.Lf, f) } } b.Q(r); p && p(d) } function Y(b, c, e, g) { if (g == R && s == f && L) if (!Cc) { var a = t(b); A.Jd(a, f, c, d, e); c.Nf(); U.Nb(a - U.Lb() - 1); U.G(a); z.Gb(b, b, 0) } } function bb(b) { if (b == R && s == f) { if (!h) { var a = g; if (A) if (A.ib == f) a = A.le(); else A.Kb(); Z(); h = new vc(n, f, a, q); h.vd(p) } !h.Oc() && h.bc() } } function G(e, i, l) { if (e == f) { if (e != i) C[ i ] && C[ i ].Ad(); else !l && h && h.gf(); p && p.Ub(); var m = R = b.ab(); d.rb(b.J(g, bb, m)) } else { var k = c.min(f, e), j = c.max(f, e), o = c.min(j - k, k + r - j), n = u + a.mf - 1; (!S || o <= n) && d.rb() } } function db() { if (s == f && h) { h.hb(); p && p.pf(); p && p.yf(); h.Hc() } } function eb() { s == f && h && h.hb() } function ab(a) { !P && o.l(i.zf, f, a) } function Q() { p = w.pInstance; h && h.vd(p) } d.rb = function (c, a) { a = a || v; if (y.length && !H) { b.X(a); if (!W) { W = e; o.l(i.Cf, f); b.f(y, function (a) { if (!b.q(a, "src")) { a.src = b.i(a, "src2") || ""; b.bb(a, a[ "display-origin" ]) } }) } b.fe(y, j, b.J(g, O, c, a)) } else O(c, a) }; d.Gf = function () { var j = f; if (a.Bd < 0) j -= r; var e = j + a.Bd * tc; if (D & 2) e = t(e); if (!(D & 1) && !ib) e = c.max(0, c.min(e, r - u)); if (e != f) { if (A) { var h = A.ae(r); if (h) { var k = R = b.ab(), i = C[ t(e) ]; return i.rb(b.J(g, Y, e, i, h, k), v) } } cb(e) } else if (a.Cb) { d.Ad(); G(f, f) } }; d.fc = function () { G(f, f, e) }; d.Ad = function () { p && p.pf(); p && p.yf(); d.xd(); h && h.Bf(); h = g; E() }; d.Nf = function () { b.Q(n) }; d.xd = function () { b.X(n) }; d.uf = function () { p && p.Ub() }; function T(a, c, d) { if (b.q(a, "jssor-slider")) return; if (!F) { if (a.tagName == "IMG") { y.push(a); if (!b.q(a, "src")) { S = e; a[ "display-origin" ] = b.bb(a); b.Q(a) } } var f = b.be(a); if (f) { var g = new Image; b.i(g, "src2", f); y.push(g) } if (d) { b.u(a, (b.u(a) || 0) + 1); b.wc(a, b.wc(a) || 0); b.dc(a, b.dc(a) || 0); b.wb(a, { N: 0 }) } } var h = b.ub(a); b.f(h, function (f) { var h = f.tagName, i = b.i(f, "u"); if (i == "player" && !w) { w = f; if (w.pInstance) Q(); else b.a(w, "dataavailable", Q) } if (i == "caption") { if (c) { b.Rc(f, b.i(f, "to")); b.xf(f, b.i(f, "bf")); b.i(f, "3d") && b.wf(f, "preserve-3d") } else if (!b.rd()) { var g = b.Eb(f, k, e); b.Pb(g, f, a); b.Tb(f, a); f = g; c = e } } else if (!F && !d && !j) { if (h == "A") { if (b.i(f, "u") == "image") j = b.Kf(f, "IMG"); else j = b.E(f, "image", e); if (j) { x = f; b.bb(x, "block"); b.V(x, V); B = b.Eb(x, e); b.B(x, "relative"); b.qc(B, 0); b.O(B, "backgroundColor", "#000") } } else if (h == "IMG" && b.i(f, "u") == "image") j = f; if (j) { j.border = 0; b.V(j, V) } } T(f, c, d + 1) }) } d.pc = function (c, b) { var a = u - b; Zb(N, a) }; d.ib = f; m.call(d); b.vf(n, b.i(n, "p")); b.sf(n, b.i(n, "po")); var M = b.E(n, "thumb", e); if (M) { b.Eb(M); b.Q(M) } b.X(n); v = b.Eb(gb); b.u(v, 1e3); b.a(n, "click", ab); E(e); d.je = j; d.qd = B; d.oc = N = n; b.T(N, v); o.xb(203, G); o.xb(28, eb); o.xb(24, db) } function vc(y, f, p, q) { var a = this, m = 0, u = 0, g, h, d, c, j, t, r, n = C[ f ]; l.call(a, 0, 0); function v() { b.hc(N); cc && j && n.qd && b.T(N, n.qd); b.X(N, !j && n.je) } function w() { a.bc() } function x(b) { r = b; a.hb(); a.bc() } a.bc = function () { var b = a.Bb(); if (!B && !O && !r && s == f) { if (!b) { if (g && !j) { j = e; a.Hc(e); o.l(i.lf, f, m, u, g, c) } v() } var k, p = i.kd; if (b != c) if (b == d) k = c; else if (b == h) k = d; else if (!b) k = h; else k = a.Nc(); o.l(p, f, b, m, h, d, c); var l = L && (!E || F); if (b == c) (d != c && !(E & 12) || l) && n.Gf(); else (l || b != c) && a.Cc(k, w) } }; a.gf = function () { d == c && d == a.Bb() && a.G(h) }; a.Bf = function () { A && A.ib == f && A.Kb(); var b = a.Bb(); b < c && o.l(i.kd, f, -b - 1, m, h, d, c) }; a.Hc = function (a) { p && b.Mb(lb, a && p.gc.Tf ? "" : "hidden") }; a.pc = function (b, a) { if (j && a >= g) { j = k; v(); n.xd(); A.Kb(); o.l(i.hf, f, m, u, g, c) } o.l(i.Ef, f, a, m, h, d, c) }; a.vd = function (a) { if (a && !t) { t = a; a.xb($JssorPlayer$.Ld, x) } }; p && a.mc(p); g = a.S(); a.mc(q); h = g + q.hd; d = g + q.gd; c = a.S() } function Kb(a, c, d) { b.D(a, c); b.F(a, d) } function Zb(c, b) { var a = x > 0 ? x : fb, d = zb * b * (a & 1), e = Ab * b * (a >> 1 & 1); Kb(c, d, e) } function Pb() { qb = O; Ib = z.Nc(); G = w.cb() } function gc() { Pb(); if (B || !F && E & 12) { z.hb(); o.l(i.Jf) } } function ec(f) { if (!B && (F || !(E & 12)) && !z.Oc()) { var d = w.cb(), b = c.ceil(G); if (f && c.abs(H) >= a.fd) { b = c.ceil(d); b += jb } if (!(D & 1)) b = c.min(r - u, c.max(b, 0)); var e = c.abs(b - d); e = 1 - c.pow(1 - e, 5); if (!P && qb) z.Rd(Ib); else if (d == b) { tb.uf(); tb.fc() } else z.Gb(d, b, e * Vb) } } function Hb(a) { !b.i(b.Zb(a), "nodrag") && b.Fb(a) } function rc(a) { Yb(a, 1) } function Yb(a, c) { a = b.Fc(a); var j = b.Zb(a); if (!M && !b.i(j, "nodrag") && sc() && (!c || a.touches.length == 1)) { B = e; yb = k; R = g; b.a(f, c ? "touchmove" : "mousemove", Bb); b.ab(); P = 0; gc(); if (!qb) x = 0; if (c) { var h = a.touches[ 0 ]; ub = h.clientX; vb = h.clientY } else { var d = b.Ic(a); ub = d.x; vb = d.y } H = 0; hb = 0; jb = 0; o.l(i.Of, t(G), G, a) } } function Bb(d) { if (B) { d = b.Fc(d); var f; if (d.type != "mousemove") { var l = d.touches[ 0 ]; f = { x: l.clientX, y: l.clientY } } else f = b.Ic(d); if (f) { var j = f.x - ub, k = f.y - vb; if (c.floor(G) != G) x = x || fb & M; if ((j || k) && !x) { if (M == 3) if (c.abs(k) > c.abs(j)) x = 2; else x = 1; else x = M; if (ob && x == 1 && c.abs(k) - c.abs(j) > 3) yb = e } if (x) { var a = k, i = Ab; if (x == 1) { a = j; i = zb } if (!(D & 1)) { if (a > 0) { var g = i * s, h = a - g; if (h > 0) a = g + c.sqrt(h) * 5 } if (a < 0) { var g = i * (r - u - s), h = -a - g; if (h > 0) a = -g - c.sqrt(h) * 5 } } if (H - hb < -2) jb = 0; else if (H - hb > 2) jb = -1; hb = H; H = a; sb = G - H / i / (Y || 1); if (H && x && !yb) { b.Fb(d); if (!O) z.Mf(sb); else z.If(sb) } } } } } function bb() { qc(); if (B) { B = k; b.ab(); b.L(f, "mousemove", Bb); b.L(f, "touchmove", Bb); P = H; z.hb(); var a = w.cb(); o.l(i.ff, t(a), a, t(G), G); E & 12 && Pb(); ec(e) } } function jc(c) { if (P) { b.ne(c); var a = b.Zb(c); while (a && v !== a) { a.tagName == "A" && b.Fb(c); try { a = a.parentNode } catch (d) { break } } } } function Jb(a) { C[ s ]; s = t(a); tb = C[ s ]; Tb(a); return s } function Dc(a, b) { x = 0; Jb(a); o.l(i.Af, t(a), b) } function Tb(a, c) { wb = a; b.f(S, function (b) { b.ec(t(a), a, c) }) } function sc() { var b = i.md || 0, a = X; if (ob) a & 1 && (a &= 1); i.md |= a; return M = a & ~b } function qc() { if (M) { i.md &= ~X; M = 0 } } function Xb() { var a = b.Vb(); b.V(a, V); b.B(a, "absolute"); return a } function t(a) { return (a % r + r) % r } function kc(b, d) { if (d) if (!D) { b = c.min(c.max(b + wb, 0), r - u); d = k } else if (D & 2) { b = t(b + wb); d = k } cb(b, a.Wb, d) } function xb() { b.f(S, function (a) { a.cc(a.Xb.df <= F) }) } function hc() { if (!F) { F = 1; xb(); if (!B) { E & 12 && ec(); E & 3 && C[ s ].fc() } } } function Ec() { if (F) { F = 0; xb(); B || !(E & 12) || gc() } } function ic() { V = { tb: K, qb: J, j: 0, k: 0 }; b.f(T, function (a) { b.V(a, V); b.B(a, "absolute"); b.Mb(a, "hidden"); b.Q(a) }); b.V(gb, V) } function ab(b, a) { cb(b, a, e) } function cb(g, f, l) { if (Rb && (!B && (F || !(E & 12)) || a.pd)) { O = e; B = k; z.hb(); if (f == h) f = Vb; var d = Cb.Bb(), b = g; if (l) { b = d + g; if (g > 0) b = c.ceil(b); else b = c.floor(b) } if (D & 2) b = t(b); if (!(D & 1)) b = c.max(0, c.min(b, r - u)); var j = (b - d) % r; b = d + j; var i = d == b ? 0 : f * c.abs(j); i = c.min(i, f * u * 1.5); z.Gb(d, b, i || 1) } } o.Mc = function () { if (!L) { L = e; C[ s ] && C[ s ].fc() } }; function W() { return b.n(y || p) } function nb() { return b.p(y || p) } o.P = W; o.fb = nb; function Eb(c, d) { if (c == h) return b.n(p); if (!y) { var a = b.Vb(f); b.ad(a, b.ad(p)); b.Hb(a, b.Hb(p)); b.bb(a, "block"); b.B(a, "relative"); b.F(a, 0); b.D(a, 0); b.Mb(a, "visible"); y = b.Vb(f); b.B(y, "absolute"); b.F(y, 0); b.D(y, 0); b.n(y, b.n(p)); b.p(y, b.p(p)); b.Rc(y, "0 0"); b.T(y, a); var i = b.ub(p); b.T(p, y); b.O(p, "backgroundImage", ""); b.f(i, function (c) { b.T(b.i(c, "noscale") ? p : a, c); b.i(c, "autocenter") && Lb.push(c) }) } Y = c / (d ? b.p : b.n)(y); b.qf(y, Y); var g = d ? Y * W() : c, e = d ? c : Y * nb(); b.n(p, g); b.p(p, e); b.f(Lb, function (a) { var c = b.Qb(b.i(a, "autocenter")); b.he(a, c) }) } m.call(o); o.kb = p = b.Db(p); var a = b.M({ nb: 0, mf: 1, Bc: 1, Ac: 0, yc: k, Cb: 1, mb: e, pd: e, Bd: 1, Ec: 3e3, zd: 1, Wb: 500, Wc: d.Sc, fd: 20, yd: 0, zb: 1, wd: 0, Se: 1, zc: 1, ld: 1 }, fc); a.mb = a.mb && b.Ff(); if (a.bd != h) a.Ec = a.bd; if (a.Fe != h) a.wd = a.Fe; var fb = a.zc & 3, tc = (a.zc & 4) / -4 || 1, mb = a.Qf, I = b.M({ K: q, mb: a.mb }, a.ef); I.yb = I.yb || I.Uf; var Fb = a.De, Z = a.Ce, eb = a.Sf, Q = !a.Se, y, v = b.E(p, "slides", Q), gb = b.E(p, "loading", Q) || b.Vb(f), Nb = b.E(p, "navigator", Q), dc = b.E(p, "arrowleft", Q), ac = b.E(p, "arrowright", Q), Mb = b.E(p, "thumbnavigator", Q), pc = b.n(v), nc = b.p(v), V, T = [], uc = b.ub(v); b.f(uc, function (a) { a.tagName == "DIV" && !b.i(a, "u") && T.push(a); b.u(a, (b.u(a) || 0) + 1) }); var s = -1, wb, tb, r = T.length, K = a.Be || pc, J = a.Ae || nc, Wb = a.yd, zb = K + Wb, Ab = J + Wb, bc = fb & 1 ? zb : Ab, u = c.min(a.zb, r), lb, x, M, yb, S = [], Qb, Sb, Ob, cc, Cc, L, E = a.zd, lc = a.Ec, Vb = a.Wb, rb, ib, kb, Rb = u < r, D = Rb ? a.Cb : 0, X, P, F = 1, O, B, R, ub = 0, vb = 0, H, hb, jb, Cb, w, U, z, Ub = new oc, Y, Lb = []; if (r) { if (a.mb) Kb = function (a, c, d) { b.wb(a, { gb: c, db: d }) }; L = a.yc; o.Xb = fc; ic(); b.q(p, "jssor-slider", e); b.u(v, b.u(v) || 0); b.B(v, "absolute"); lb = b.Eb(v, e); b.Pb(lb, v); if (mb) { cc = mb.Rf; rb = mb.K; ib = u == 1 && r > 1 && rb && (!b.rd() || b.cd() >= 8) } kb = ib || u >= r || !(D & 1) ? 0 : a.wd; X = (u > 1 || kb ? fb : -1) & a.ld; var Gb = v, C = [], A, N, Db = b.rf(), ob = Db.of, G, qb, Ib, sb; Db.nd && b.O(Gb, Db.nd, ([ g, "pan-y", "pan-x", "none" ])[ X ] || ""); U = new zc; if (ib) A = new rb(Ub, K, J, mb, ob); b.T(lb, U.oc); b.Mb(v, "hidden"); N = Xb(); b.O(N, "backgroundColor", "#000"); b.qc(N, 0); b.Pb(N, Gb.firstChild, Gb); for (var db = 0; db < T.length; db++) { var wc = T[ db ], yc = new xc(wc, db); C.push(yc) } b.Q(gb); Cb = new Ac; z = new mc(Cb, U); b.a(v, "click", jc, e); b.a(p, "mouseout", b.ac(hc, p)); b.a(p, "mouseover", b.ac(Ec, p)); if (X) { b.a(v, "mousedown", Yb); b.a(v, "touchstart", rc); b.a(v, "dragstart", Hb); b.a(v, "selectstart", Hb); b.a(f, "mouseup", bb); b.a(f, "touchend", bb); b.a(f, "touchcancel", bb); b.a(j, "blur", bb) } E &= ob ? 10 : 5; if (Nb && Fb) { Qb = new Fb.K(Nb, Fb, W(), nb()); S.push(Qb) } if (Z && dc && ac) { Z.Cb = D; Z.zb = u; Sb = new Z.K(dc, ac, Z, W(), nb()); S.push(Sb) } if (Mb && eb) { eb.Ac = a.Ac; Ob = new eb.K(Mb, eb); S.push(Ob) } b.f(S, function (a) { a.vc(r, C, gb); a.xb(n.uc, kc) }); b.O(p, "visibility", "visible"); Eb(W()); xb(); a.Bc && b.a(f, "keydown", function (b) { if (b.keyCode == 37) ab(-a.Bc); else b.keyCode == 39 && ab(a.Bc) }); var pb = a.Ac; if (!(D & 1)) pb = c.max(0, c.min(pb, r - u)); z.Gb(pb, pb, 0) } }; i.zf = 21; i.Of = 22; i.ff = 23; i.me = 24; i.Ee = 25; i.Cf = 26; i.Lf = 27; i.Jf = 28; i.Fd = 202; i.Af = 203; i.lf = 206; i.hf = 207; i.Ef = 208; i.kd = 209; var n = { uc: 1 }, r = function (d, C) { var f = this; m.call(f); d = b.Db(d); var s, A, z, r, l = 0, a, o, j, w, x, i, h, q, p, B = [], y = []; function v(a) { a != -1 && y[ a ].de(a == l) } function t(a) { f.l(n.uc, a * o) } f.kb = d; f.ec = function (a) { if (a != r) { var d = l, b = c.floor(a / o); l = b; r = a; v(d); v(b) } }; f.cc = function (a) { b.X(d, a) }; var u; f.vc = function (D) { if (!u) { s = c.ceil(D / o); l = 0; var n = q + w, r = p + x, m = c.ceil(s / j) - 1; A = q + n * (!i ? m : j - 1); z = p + r * (i ? m : j - 1); b.n(d, A); b.p(d, z); for (var f = 0; f < s; f++) { var C = b.oe(); b.jf(C, f + 1); var k = b.ee(h, "numbertemplate", C, e); b.B(k, "absolute"); var v = f % (m + 1); b.D(k, !i ? n * v : f % j * n); b.F(k, i ? r * v : c.floor(f / (m + 1)) * r); b.T(d, k); B[ f ] = k; a.Rb & 1 && b.a(k, "click", b.J(g, t, f)); a.Rb & 2 && b.a(k, "mouseover", b.ac(b.J(g, t, f), k)); y[ f ] = b.Yb(k) } u = e } }; f.Xb = a = b.M({ td: 10, Tc: 10, Uc: 1, Rb: 1 }, C); h = b.E(d, "prototype"); q = b.n(h); p = b.p(h); b.Tb(h, d); o = a.Vc || 1; j = a.kf || 1; w = a.td; x = a.Tc; i = a.Uc - 1; a.xc == k && b.q(d, "noscale", e); a.Ab && b.q(d, "autocenter", a.Ab) }, s = function (a, h, i) { var c = this; m.call(c); var r, d, f, j; b.n(a); b.p(a); var p, o; function l(a) { c.l(n.uc, a, e) } function t(c) { b.X(a, c); b.X(h, c) } function s() { p.Ub(i.Cb || d > 0); o.Ub(i.Cb || d < r - i.zb) } c.ec = function (b, a, c) { if (c) d = a; else { d = b; s() } }; c.cc = t; var q; c.vc = function (c) { r = c; d = 0; if (!q) { b.a(a, "click", b.J(g, l, -j)); b.a(h, "click", b.J(g, l, j)); p = b.Yb(a); o = b.Yb(h); q = e } }; c.Xb = f = b.M({ Vc: 1 }, i); j = f.Vc; if (f.xc == k) { b.q(a, "noscale", e); b.q(h, "noscale", e) } if (f.Ab) { b.q(a, "autocenter", f.Ab); b.q(h, "autocenter", f.Ab) } }; function q(e, d, c) { var a = this; l.call(a, 0, c); a.Yc = b.Xc; a.hd = 0; a.gd = c } var t = function (v, j, u, E) { var a = this, w, o = {}, p = j.yb, s = j.Pf, h = new l(0, 0), q = [], i = [], D = E, f = D ? 1e8 : 0; l.call(a, 0, 0); function r(d, c) { var a = {}; b.f(d, function (d, f) { var e = o[ f ]; if (e) { if (b.Ke(d)) d = r(d, c || f == "e"); else if (c) if (b.nc(d)) d = w[ d ]; a[ e ] = d } }); return a } function t(e, c) { var a = [], d = b.ub(e); b.f(d, function (d) { var h = b.i(d, "u") == "caption"; if (h) { var e = b.i(d, "t"), g = p[ b.Qb(e) ] || p[ e ], f = { kb: d, gc: g }; a.push(f) } if (c < 5) a = a.concat(t(d, c + 1)) }); return a } function n(c, e) { var a = q[ c ]; if (a == g) { a = q[ c ] = { Y: c, ic: [], Zc: [] }; var d = 0; !b.f(i, function (a, b) { d = b; return a.Y > c }) && d++; i.splice(d, 0, a) } return a } function z(t, u, h) { var a, d; if (s) { var o = b.i(t, "c"); if (o) { var m = s[ b.Qb(o) ]; if (m) { a = n(m.r, 0); a.ce = m.e || 0 } } } b.f(u, function (i) { var g = b.M(e, {}, r(i)), j = b.od(g.Z); delete g.Z; if (g.k) { g.v = g.k; j.v = j.k; delete g.k } if (g.j) { g.A = g.j; j.A = j.j; delete g.j } var o = { Z: j, P: h.tb, fb: h.qb }, k = new l(i.b, i.d, o, t, h, g); f = c.max(f, i.b + i.d); if (a) if (d) d.I(k); else d = k; else { var m = n(i.b, i.b + i.d); m.ic.push(k) } h = b.Ud(h, g) }); if (a && d) { var i = d, k, j = d.Lb(), p = d.S(), q = c.max(p, a.ce); if (a.Y < p) { if (a.Y > j) { i = new l(j, a.Y - j); i.I(d, e) } else i = g; k = new l(a.Y, q - j, { jc: q - a.Y, Pd: e }); k.I(d, e) } i && a.ic.push(i); k && a.Zc.push(k) } return h } function y(a) { b.f(a, function (d) { var a = d.kb, f = b.n(a), e = b.p(a), c = { k: b.D(a), j: b.F(a), v: 0, A: 0, vb: 1, ob: b.u(a) || 0, lb: 0, W: 0, U: 0, z: 1, s: 1, gb: 0, db: 0, N: 0, Jb: 0, Ib: 0, tb: f, qb: e, g: { j: 0, H: f, C: e, k: 0 } }; c.ed = c.k; c.id = c.j; z(a, d.gc, c) }) } function B(f, d, g) { var c = f.b - d; if (c) { var b = new l(d, c); b.I(h, e); b.Nb(g); a.I(b) } a.Qc(f.d); return c } function A(e) { var c = h.Lb(), d = 0; b.f(e, function (e, f) { e = b.M({ d: u }, e); B(e, c, d); c = e.b; d += e.d; if (!f || e.t == 2) { a.hd = c; a.gd = c + e.d } }) } function k(j, d, e) { var g = d.length; if (g > 4) for (var m = c.ceil(g / 4), a = 0; a < m; a++) { var h = d.slice(a * 4, c.min(a * 4 + 4, g)), i = new l(h[ 0 ].Y, 0); k(i, h, e); j.I(i) } else b.f(d, function (a) { b.f(e ? a.Zc : a.ic, function (a) { e && a.Qc(f - a.S()); j.I(a) }) }) } a.Yc = function () { a.G(-1, e) }; w = [ d.kc, d.Le, d.Me, d.Sc, d.Ne, d.Oe, d.Pe, d.Qe, d.Re, d.Te, d.cf, d.Ue, d.Ve, d.We, d.Xe, d.Ye, d.Dc, d.Ze, d.af, d.bf, d.Je, d.Ie, d.He, d.Ge, d.pe, d.qe, d.re, d.se, d.te, d.sd, d.rc, d.ue, d.ve, d.we, d.xe, d.ye, d.ze ]; var C = { j: "y", k: "x", C: "m", H: "t", lb: "r", W: "rX", U: "rY", z: "sX", s: "sY", gb: "tX", db: "tY", N: "tZ", Jb: "kX", Ib: "kY", vb: "o", Z: "e", ob: "i", g: "c" }; b.f(C, function (b, a) { o[ b ] = a }); y(t(v, 1)); k(h, i); var x = j.Xf || [], m = [].concat(x[ b.Qb(b.i(v, "b")) ] || []); m.push({ b: h.S(), d: m.length ? 0 : u }); A(m); f = c.max(f, a.S()); k(a, i, e); a.G(-1) }; jssor_html5_AdWords_slider_init = function () { var a = [ [ { b: -1, d: 1, o: -1, rY: -120 }, { b: 2600, d: 500, o: 1, rY: 120, e: { rY: 17 } } ], [ { b: -1, d: 1, o: -1 }, { b: 1480, d: 20, o: 1 }, { b: 1500, d: 500, y: -20, e: { y: 19 } }, { b: 2300, d: 300, x: -20, e: { x: 19 } }, { b: 3100, d: 300, o: -1, sY: 9 } ], [ { b: -1, d: 1, o: -1 }, { b: 2300, d: 300, x: 20, o: 1, e: { x: 19 } }, { b: 3100, d: 300, o: -1, sY: 9 } ], [ { b: -1, d: 1, sX: -1, sY: -1 }, { b: 0, d: 1e3, sX: 2, sY: 2, e: { sX: 7, sY: 7 } }, { b: 1e3, d: 500, sX: -1, sY: -1, e: { sX: 16, sY: 16 } }, { b: 1500, d: 500, y: 20, e: { y: 19 } } ], [ { b: 1e3, d: 1e3, r: -30 }, { b: 2e3, d: 500, r: 30, e: { r: 2 } }, { b: 2500, d: 500, r: -30, e: { r: 3 } }, { b: 3e3, d: 3e3, x: 70, y: 40, rY: -20, tZ: -20 } ], [ { b: -1, d: 1, o: -1 }, { b: 0, d: 1e3, o: 1 } ], [ { b: -1, d: 1, o: -1, r: 30 }, { b: 1e3, d: 1e3, o: 1 } ], [ { b: -1, d: 1, o: -1 }, { b: 2780, d: 20, o: 1 }, { b: 2800, d: 500, y: -70, e: { y: 3 } }, { b: 3300, d: 1e3, y: 180 }, { b: 4300, d: 500, y: -40, e: { y: 3 } }, { b: 5300, d: 500, y: -40, e: { y: 3 } }, { b: 6300, d: 500, y: -40, e: { y: 3 } }, { b: 7300, d: 500, y: -40, e: { y: 3 } }, { b: 8300, d: 400, y: -30 } ], [ { b: -1, d: 1, c: { y: 200 } }, { b: 4300, d: 4400, c: { y: -200 }, e: { c: { y: 1 } } } ], [ { b: -1, d: 1, o: -1 }, { b: 4300, d: 20, o: 1 } ], [ { b: -1, d: 1, o: -1 }, { b: 5300, d: 20, o: 1 } ], [ { b: -1, d: 1, o: -1 }, { b: 6300, d: 20, o: 1 } ], [ { b: -1, d: 1, o: -1 }, { b: 7300, d: 20, o: 1 } ], [ { b: -1, d: 1, o: -1 }, { b: 8300, d: 20, o: 1 } ], [ { b: 2e3, d: 1e3, o: -.5, r: 180, sX: 4, sY: 4, e: { r: 5, sX: 5, sY: 5 } }, { b: 3e3, d: 1e3, o: -.5, r: 180, sX: -4, sY: -4, e: { r: 6, sX: 6, sY: 6 } } ], [ { b: -1, d: 1, o: -1, c: { m: -35 } }, { b: 0, d: 1500, x: 150, o: 1, e: { x: 6 } } ], [ { b: -1, d: 1, o: -1, c: { y: 35 } }, { b: 0, d: 1500, x: -150, o: 1, e: { x: 6 } } ], [ { b: -1, d: 1, o: -1 }, { b: 6500, d: 2e3, o: 1 } ], [ { b: -1, d: 1, o: -1 }, { b: 2e3, d: 1e3, o: .5, r: 180, sX: 4, sY: 4, e: { r: 5, sX: 5, sY: 5 } }, { b: 3e3, d: 1e3, o: .5, r: 180, sX: -4, sY: -4, e: { r: 6, sX: 6, sY: 6 } }, { b: 4500, d: 1500, x: -45, y: 60, e: { x: 12, y: 3 } } ], [ { b: -1, d: 1, o: -1 }, { b: 4500, d: 1500, o: 1, e: { o: 5 } }, { b: 6500, d: 2e3, o: -1, r: 10, rX: 30, rY: 20, e: { rY: 6 } } ] ], b = { yc: e, bd: 1600, Wb: 400, Wc: d.Dc, ef: { K: t, yb: a }, Ce: { K: s, df: 1 }, De: { K: r, Rb: 2 } }; new i("jssor_html5_AdWords", b) }
        })(window, document, Math, null, true, false)
    </script>

</body>

</html>